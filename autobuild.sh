#!/bin/bash
sudo chmod 777 /dev/ttyUSB2


set -o errexit #exit on error


echo "==================PHYBOT SOFTWARE=================="
echo "1.realrobot"
echo "2.webots_sim"
echo "3.mujoco_sim_wbc"
echo "4.mujoco_sim_rl"
echo "5.ros_sim"
echo "6.realrobot_rl"
echo "7.realrobot_motion"

read -p "enter control system number:" control_system_enter
case $control_system_enter in
    1)
        control_system_enter=realrobot
        ;;
    2)
        control_system_enter=webots_sim
        ;;
    3)
        control_system_enter=mujoco_sim_wbc
        ;;
    4)
        control_system_enter=mujoco_sim_rl
        ;;
    5)
        control_system_enter=ros_sim
        ;;
    6)
        control_system_enter=realrobot_rl
        ;;
    7)
        control_system_enter=realrobot_motion
        ;;
    *)  
        echo "!invalid enter!"
        exit
        ;;
esac

root=$(pwd)
build_dir=$root/build/
mkdir -p ${build_dir}
echo "${build_dir}"
pushd ${build_dir}

cmake -DWHICH_ENV=${control_system_enter}  $root -DCMAKE_BUILD_TYPE=Release .
make -j8
popd