#include"../JoyStick_rxy/include/joystick_rxy.h"
#include"../UDP_Communication_rxy/include/udp_group.hpp"
int main(){
    std::string server_ip = "************";
    int server_port = 8001;
    std::string client_ip = "************";
    int client_port = 4001;    
    udpGroup Group(server_ip,server_port,client_ip,client_port,20000);
    Group.start();
    // Group.PowerOn();
    // usleep(1000);
    // Group.start();
    Joystick joy;
    joy.init();
    // power flag
    bool power_flag = false;
    while(true)
    {
        if((joy.get_current_state_command()!= "GotoSetZero")&&(joy.get_state_change() == "GotoSetZero")){
            Group.SetZero();
        }
        if((power_flag == false)&&(joy.get_state_change() == "GotoPowerOn")){
            Group.PowerOn();
            power_flag = true;
        }
        if((power_flag == true)&&(joy.get_state_change() == "GotoPowerOff")){
            Group.PowerOff();
            power_flag = false;
        } 
        float pos_cmd = joy.getMotorPosCommd();
        float vel_cmd = 0.0;
        float current_cmd = 0.0;
        float kp_cmd = 2.0;
        float kd_cmd = 0.2;

        float pos_a = 0.0;
        float vel_a = 0.0;
        float current_a = 0.0;
        if(power_flag == true)
        {
            Group.SetPD(kp_cmd,kd_cmd);
            Group.SetPVT(pos_cmd,vel_cmd,current_cmd);
            Group.GetPVT(pos_a,vel_a,current_a);
            std::cout<<"pos: "<<pos_a << "vel: "<<vel_a<<"current: "<<current_a<<std::endl;
        }
        usleep(5*1000);
    }

}