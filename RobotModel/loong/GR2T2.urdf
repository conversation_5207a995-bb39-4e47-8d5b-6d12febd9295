<?xml version="1.0" encoding="utf-8"?>

<!-- 08172024ver1 -->

<robot
  name="GR2T2">
  <link
    name="base">
    <inertial>
      <origin
        xyz="-0.050889 0.00 -0.045579"
        rpy="0 0 0" />
      <mass
        value="6.36" />
      <inertia
        ixx="0.047512"
        ixy="3.99E-06"
        ixz="0.00034188"
        iyy="0.029447"
        iyz="1.7E-05"
        izz="0.039535" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/base.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/base.STL" />
      </geometry>
    </collision> -->
  </link>
  <link
    name="waist1">
    <inertial>
      <origin
        xyz="-0.00039919 -3.55E-05 0.08079"
        rpy="0 0 0" />
      <mass
        value="0.69" />
      <inertia
        ixx="0.0010409"
        ixy="-1E-08"
        ixz="-2.8E-07"
        iyy="0.0010447"
        iyz="4.9E-07"
        izz="0.00056658" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/waist1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/waist1.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="waist1"
    type="revolute">
    <origin
      xyz="0 0 -0.0335"
      rpy="0 0 0" />
    <parent
      link="base" />
    <child
      link="waist1" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-2.617"
      upper="2.617"
      effort="82.5"
      velocity="3.24" />
  </joint>
  <link
    name="torso">
    <inertial>
      <origin
        xyz="-0.021484 -4.63E-05 -0.12733"
        rpy="0 0 0" />
      <mass
        value="14.97" />
      <inertia
        ixx="0.26875"
        ixy="-0.00046521"
        ixz="-0.0028119"
        iyy="0.22441"
        iyz="0.0010695"
        izz="0.11476" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/torso.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/torso.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="torso"
    type="fixed">
    <origin
      xyz="0 0 0.4135"
      rpy="0 0 0" />
    <parent
      link="waist1" />
    <child
      link="torso" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="head1">
    <inertial>
      <origin
        xyz="-2.74E-09 0.018481 0.049744"
        rpy="0 0 0" />
      <mass
        value="0.21" />
      <inertia
        ixx="0.00020193"
        ixy="0"
        ixz="0"
        iyy="0.00017128"
        iyz="-3.72E-05"
        izz="6.13E-05" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/head1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="head1"
    type="revolute">
    <origin
      xyz="0.001 0 0.08575"
      rpy="0 0 0" />
    <parent
      link="torso" />
    <child
      link="head1" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.396"
      upper="1.396"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="head2">
    <inertial>
      <origin
        xyz="0.029215 0.0015527 0.08792"
        rpy="0 0 0" />
      <mass
        value="1.29" />
      <inertia
        ixx="0.0064394"
        ixy="3.09E-05"
        ixz="-0.00015995"
        iyy="0.0074596"
        iyz="7.87E-05"
        izz="0.0041621" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/head2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="head2"
    type="revolute">
    <origin
      xyz="0 0 0.07005"
      rpy="0 0 0" />
    <parent
      link="head1" />
    <child
      link="head2" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.523"
      upper="0.663"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="camera_link">
    <inertial>
      <origin
        xyz="0.018047 2.84E-05 0.00027674"
        rpy="0 0 0" />
      <mass
        value="0.07" />
      <inertia
        ixx="6.41E-05"
        ixy="1.9E-07"
        ixz="2.4E-07"
        iyy="1.34E-05"
        iyz="0"
        izz="6.69E-05" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/camera_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/camera_link.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="camera_joint"
    type="fixed">
    <origin
      xyz="0.090579 0 0.091065"
      rpy="0 0.12217 0" />
    <parent
      link="head2" />
    <child
      link="camera_link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="LArm1">
    <inertial>
      <origin
        xyz="0.0010189 0.078263 0.001"
        rpy="0 0 0" />
      <mass
        value="1.35" />
      <inertia
        ixx="0.0031422"
        ixy="-6.68E-05"
        ixz="5.1E-07"
        iyy="0.0012583"
        iyz="-2.2E-07"
        izz="0.0030484" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LArm1"
    type="revolute">
    <origin
      xyz="0 0.128 0"
      rpy="0 0 0" />
    <parent
      link="torso" />
    <child
      link="LArm1" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.966"
      upper="1.919"
      effort="82.5"
      velocity="3.24" />
  </joint>
  <link
    name="LArm2">
    <inertial>
      <origin
        xyz="0.003813 0.0056748 -0.039656"
        rpy="0 0 0" />
      <mass
        value="0.96" />
      <inertia
        ixx="0.002538"
        ixy="9.63E-06"
        ixz="-0.00033776"
        iyy="0.0027454"
        iyz="9E-08"
        izz="0.00086136" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LArm2"
    type="revolute">
    <origin
      xyz="0 0.102 0"
      rpy="0 0 0" />
    <parent
      link="LArm1" />
    <child
      link="LArm2" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.523"
      upper="2.791"
      effort="82.5"
      velocity="3.24" />
  </joint>
  <link
    name="LArm3">
    <inertial>
      <origin
        xyz="-0.0019473 0.00066361 -0.054501"
        rpy="0 0 0" />
      <mass
        value="1.32" />
      <inertia
        ixx="0.0068752"
        ixy="3E-07"
        ixz="-8.99E-06"
        iyy="0.0069461"
        iyz="0.00010128"
        izz="0.0011309" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.10"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.11" radius="0.03"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="LArm3"
    type="revolute">
    <origin
      xyz="0 0 -0.12298"
      rpy="0 0 0" />
    <parent
      link="LArm2" />
    <child
      link="LArm3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.832"
      upper="1.832"
      effort="47.4"
      velocity="4.97" />
  </joint>
  <link
    name="LArm4">
    <inertial>
      <origin
        xyz="-0.0033138 0.0044992 -0.02228"
        rpy="0 0 0" />
      <mass
        value="0.48" />
      <inertia
        ixx="0.00070355"
        ixy="-2.97E-06"
        ixz="-4.13E-06"
        iyy="0.00058904"
        iyz="-6.85E-05"
        izz="0.00037244" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LArm4"
    type="revolute">
    <origin
      xyz="0 0 -0.13252"
      rpy="0 0 0" />
    <parent
      link="LArm3" />
    <child
      link="LArm4" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.919"
      upper="0.087"
      effort="47.4"
      velocity="4.97" />
  </joint>
  <link
    name="LArm5">
    <inertial>
      <origin
        xyz="-0.00010282 0.0022157 -0.031002"
        rpy="0 0 0" />
      <mass
        value="1.04" />
      <inertia
        ixx="0.0023678"
        ixy="-9.09E-06"
        ixz="2.09E-05"
        iyy="0.00228285"
        iyz="0.00012584"
        izz="0.00086449" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 0.10"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.17" radius="0.035"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="LArm5"
    type="revolute">
    <origin
      xyz="0 0 -0.086418"
      rpy="0 0 0" />
    <parent
      link="LArm4" />
    <child
      link="LArm5" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.832"
      upper="1.832"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="LArm6">
    <inertial>
      <origin
        xyz="0.0058895 7.68E-06 -0.00064642"
        rpy="0 0 0" />
      <mass
        value="0.46" />
      <inertia
        ixx="0.00025616"
        ixy="-5.7E-07"
        ixz="-2.36E-06"
        iyy="0.00022265"
        iyz="-1.1E-07"
        izz="0.00022858" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LArm6"
    type="revolute">
    <origin
      xyz="0 0 -0.13208"
      rpy="0 0 0" />
    <parent
      link="LArm5" />
    <child
      link="LArm6" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.611"
      upper="0.611"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="LArm7">
    <inertial>
      <origin
        xyz="0.0075588 5.06E-05 -0.023965"
        rpy="0 0 0" />
      <mass
        value="0.2" />
      <inertia
        ixx="0.00010941"
        ixy="4E-08"
        ixz="-2.19E-05"
        iyy="0.00017377"
        iyz="5E-08"
        izz="0.00012799" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LArm7"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="LArm6" />
    <child
      link="LArm7" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.959"
      upper="0.959"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="LArm8">
    <inertial>
      <origin
        xyz="-4.96E-08 2.01E-08 -0.004024"
        rpy="0 0 0" />
      <mass
        value="0.55" />
      <inertia
        ixx="0.00099122"
        ixy="-0.00016399"
        ixz="-0.00027553"
        iyy="0.0017054"
        iyz="-3.23E-06"
        izz="0.0009118" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LArm8.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.04"
                rpy="0 0 0"/>
        <geometry>
            <sphere radius="0.035"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="LArm8"
    type="fixed">
    <origin
      xyz="0 0 -0.045"
      rpy="0 0 0" />
    <parent
      link="LArm7" />
    <child
      link="LArm8" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="RArm1">
    <inertial>
      <origin
        xyz="0.0010189 -0.078263 0.00099876"
        rpy="0 0 0" />
      <mass
        value="1.35" />
      <inertia
        ixx="0.00314221"
        ixy="6.68E-05"
        ixz="-2.8E-07"
        iyy="0.00125832"
        iyz="-2.1E-07"
        izz="0.00304841" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RArm1"
    type="revolute">
    <origin
      xyz="0 -0.128 0"
      rpy="0 0 0" />
    <parent
      link="torso" />
    <child
      link="RArm1" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.966"
      upper="1.919"
      effort="82.5"
      velocity="3.24" />
  </joint>
  <link
    name="RArm2">
    <inertial>
      <origin
        xyz="0.0038177 -0.0056812 -0.039659"
        rpy="0 0 0" />
      <mass
        value="0.96" />
      <inertia
        ixx="0.00253796"
        ixy="-9.64E-06"
        ixz="-0.00033779"
        iyy="0.00274532"
        iyz="-1E-07"
        izz="0.00086134" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RArm2"
    type="revolute">
    <origin
      xyz="0 -0.102 0"
      rpy="0 0 0" />
    <parent
      link="RArm1" />
    <child
      link="RArm2" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-2.791"
      upper="0.523"
      effort="82.5"
      velocity="3.24" />
  </joint>
  <link
    name="RArm3">
    <inertial>
      <origin
        xyz="-0.0019495 -0.00067825 -0.054499"
        rpy="0 0 0" />
      <mass
        value="1.32" />
      <inertia
        ixx="0.00687525"
        ixy="-5E-08"
        ixz="-8.43E-06"
        iyy="0.00694622"
        iyz="-0.0001015"
        izz="0.00113084" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.10"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.11" radius="0.03"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="RArm3"
    type="revolute">
    <origin
      xyz="0 0 -0.12298"
      rpy="0 0 0" />
    <parent
      link="RArm2" />
    <child
      link="RArm3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.832"
      upper="1.832"
      effort="47.4"
      velocity="4.97" />
  </joint>
  <link
    name="RArm4">
    <inertial>
      <origin
        xyz="-0.003312 -0.0044989 -0.022283"
        rpy="0 0 0" />
      <mass
        value="0.48" />
      <inertia
        ixx="0.00070352"
        ixy="2.96E-06"
        ixz="-4.13E-06"
        iyy="0.00058903"
        iyz="6.85E-05"
        izz="0.00037243" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RArm4"
    type="revolute">
    <origin
      xyz="0 0 -0.13252"
      rpy="0 0 0" />
    <parent
      link="RArm3" />
    <child
      link="RArm4" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.919"
      upper="0.087"
      effort="47.4"
      velocity="4.97" />
  </joint>
  <link
    name="RArm5">
    <inertial>
      <origin
        xyz="-0.00010192 -0.0022168 -0.03101"
        rpy="0 0 0" />
      <mass
        value="1.04" />
      <inertia
        ixx="0.00236563"
        ixy="8.98E-06"
        ixz="2.07E-05"
        iyy="0.00228058"
        iyz="-0.00012888"
        izz="0.0008652" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 0.10"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.17" radius="0.035"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="RArm5"
    type="revolute">
    <origin
      xyz="0 0 -0.086418"
      rpy="0 0 0" />
    <parent
      link="RArm4" />
    <child
      link="RArm5" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.832"
      upper="1.832"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="RArm6">
    <inertial>
      <origin
        xyz="0.0058903 -7.69E-06 -0.0006462"
        rpy="0 0 0" />
      <mass
        value="0.46" />
      <inertia
        ixx="0.00025616"
        ixy="5.5E-07"
        ixz="-2.32E-06"
        iyy="0.00022274"
        iyz="6E-08"
        izz="0.00022849" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RArm6"
    type="revolute">
    <origin
      xyz="0 0 -0.13208"
      rpy="0 0 0" />
    <parent
      link="RArm5" />
    <child
      link="RArm6" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.611"
      upper="0.611"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="RArm7">
    <inertial>
      <origin
        xyz="0.008305 4.79E-05 -0.022699"
        rpy="0 0 0" />
      <mass
        value="0.2" />
      <inertia
        ixx="0.00010941"
        ixy="4E-08"
        ixz="-2.19E-05"
        iyy="0.00017377"
        iyz="5E-08"
        izz="0.00012799" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RArm7"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="RArm6" />
    <child
      link="RArm7" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.959"
      upper="0.959"
      effort="29.4"
      velocity="4.58" />
  </joint>
  <link
    name="RArm8">
    <inertial>
      <origin
        xyz="7.67E-07 2.01E-08 -0.004024"
        rpy="0 0 0" />
      <mass
        value="0.55" />
      <inertia
        ixx="0.00099122"
        ixy="-0.00016399"
        ixz="-0.00027553"
        iyy="0.00170543"
        iyz="-3.23E-06"
        izz="0.0009118" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RArm8.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.04"
                rpy="0 0 0"/>
        <geometry>
            <sphere radius="0.035"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="RArm8"
    type="fixed">
    <origin
      xyz="0 0 -0.045"
      rpy="0 0 0" />
    <parent
      link="RArm7" />
    <child
      link="RArm8" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="link_LLeg1">
    <inertial>
      <origin
        xyz="-0.050241 0.088014 0.0018636"
        rpy="0 0 0" />
      <mass
        value="2.57" />
      <inertia
        ixx="0.00745357"
        ixy="0.00316003"
        ixz="-0.00019352"
        iyy="0.00829676"
        iyz="-5.27E-05"
        izz="0.01126084" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LLeg1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LLeg1"
    type="revolute">
    <origin
      xyz="0 0.05513 -0.111"
      rpy="-0.5236 0 0" />
    <parent
      link="base" />
    <child
      link="link_LLeg1" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.617"
      upper="2.617"
      effort="360"
      velocity="5.55" />
  </joint>
  <link
    name="link_LLeg2">
    <inertial>
      <origin
        xyz="0.070165 -0.0002902 -0.0054913"
        rpy="0 0 0" />
      <mass
        value="1.34" />
      <inertia
        ixx="0.00114196"
        ixy="-6.47E-06"
        ixz="0.00027308"
        iyy="0.0024568"
        iyz="-4.63E-06"
        izz="0.00274234" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LLeg2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LLeg2"
    type="revolute">
    <origin
      xyz="-0.0855 0.098 0"
      rpy="0.5236 0 0" />
    <parent
      link="link_LLeg1" />
    <child
      link="link_LLeg2" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.419"
      upper="1.57"
      effort="114"
      velocity="10.47" />
  </joint>
  <link
    name="link_LLeg3">
    <inertial>
      <origin
        xyz="0.003909 -0.0089675 -0.19938"
        rpy="0 0 0" />
      <mass
        value="5.04" />
      <inertia
        ixx="0.04254555"
        ixy="4.78E-06"
        ixz="0.00051434"
        iyy="0.04380939"
        iyz="0.00086784"
        izz="0.009955" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LLeg3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.22"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.25" radius="0.05"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="LLeg3"
    type="revolute">
    <origin
      xyz="0.0855 0 0"
      rpy="0 0 0" />
    <parent
      link="link_LLeg2" />
    <child
      link="link_LLeg3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-0.698"
      upper="1.57"
      effort="51"
      velocity="6.18" />
  </joint>
  <link
    name="link_LLeg4">
    <inertial>
      <origin
        xyz="0.0067466 0.002134 -0.16536"
        rpy="0 0 0" />
      <mass
        value="3.08" />
      <inertia
        ixx="0.02732987"
        ixy="3.28E-05"
        ixz="0.00020259"
        iyy="0.02808677"
        iyz="0.00025043"
        izz="0.00331385" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LLeg4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.15"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.2" radius="0.05"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="LLeg4"
    type="revolute">
    <origin
      xyz="0 0 -0.378"
      rpy="0 0 0" />
    <parent
      link="link_LLeg3" />
    <child
      link="link_LLeg4" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.087"
      upper="2.355"
      effort="360"
      velocity="5.55" />
  </joint>
  <link
    name="link_LLeg5">
    <inertial>
      <origin
        xyz="-7.47E-09 0.00013124 5.54E-10"
        rpy="0 0 0" />
      <mass
        value="0.09" />
      <inertia
        ixx="1.55E-05"
        ixy="0"
        ixz="0"
        iyy="8.6E-06"
        iyz="0"
        izz="1.75E-05" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LLeg5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="LLeg5"
    type="revolute">
    <origin
      xyz="0 0 -0.406"
      rpy="0 0 0" />
    <parent
      link="link_LLeg4" />
    <child
      link="link_LLeg5" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.436"
      upper="0.436"
      effort="51"
      velocity="6.18" />
  </joint>
  <link
    name="link_LLeg6">
    <inertial>
      <origin
        xyz="0.056929 0.0010534 -0.040851"
        rpy="0 0 0" />
      <mass
        value="0.7" />
      <inertia
        ixx="0.00050708"
        ixy="-9.64E-06"
        ixz="0.00013803"
        iyy="0.00295253"
        iyz="6.69E-06"
        izz="0.00317121" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/LLeg6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision name="l_foot_1">
        <origin rpy="0 1.5708 0" xyz="0.05 0.02 -0.035"/>
        <geometry>
            <cylinder length="0.24" radius="0.02"/>
        </geometry>
    </collision>
    <collision name="l_foot_2">
        <origin rpy="0 1.5708 0" xyz="0.05 -0.02 -0.035"/>
        <geometry>
            <cylinder length="0.24" radius="0.02"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="LLeg6"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="link_LLeg5" />
    <child
      link="link_LLeg6" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.785"
      upper="0.785"
      effort="29.1"
      velocity="13.61" />
  </joint>
  <link
    name="link_RLeg1">
    <inertial>
      <origin
        xyz="-0.050231 -0.088018 0.0018546"
        rpy="0 0 0" />
      <mass
        value="2.57" />
      <inertia
        ixx="0.0074496"
        ixy="-0.00315768"
        ixz="-6.51E-05"
        iyy="0.00829362"
        iyz="0.00010001"
        izz="0.01126354" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RLeg1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RLeg1"
    type="revolute">
    <origin
      xyz="0 -0.05513 -0.111"
      rpy="0.5236 0 0" />
    <parent
      link="base" />
    <child
      link="link_RLeg1" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.617"
      upper="2.617"
      effort="360"
      velocity="5.55" />
  </joint>
  <link
    name="link_RLeg2">
    <inertial>
      <origin
        xyz="0.070165 0.0002902 -0.0053932"
        rpy="0 0 0" />
      <mass
        value="1.34" />
      <inertia
        ixx="0.00114075"
        ixy="5.4E-06"
        ixz="0.00027195"
        iyy="0.00245524"
        iyz="4.14E-06"
        izz="0.00274174" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RLeg2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RLeg2"
    type="revolute">
    <origin
      xyz="-0.0855 -0.098 0"
      rpy="-0.5236 0 0" />
    <parent
      link="link_RLeg1" />
    <child
      link="link_RLeg2" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-1.57"
      upper="0.419"
      effort="114"
      velocity="10.47" />
  </joint>
  <link
    name="link_RLeg3">
    <inertial>
      <origin
        xyz="0.0039071 0.0089696 -0.19935"
        rpy="0 0 0" />
      <mass
        value="5.04" />
      <inertia
        ixx="0.04249987"
        ixy="0.00015716"
        ixz="0.00054105"
        iyy="0.04376418"
        iyz="-0.00088023"
        izz="0.0099503" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RLeg3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.22"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.25" radius="0.05"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="RLeg3"
    type="revolute">
    <origin
      xyz="0.0855 0 0"
      rpy="0 0 0" />
    <parent
      link="link_RLeg2" />
    <child
      link="link_RLeg3" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-1.57"
      upper="0.698"
      effort="51"
      velocity="6.18" />
  </joint>
  <link
    name="link_RLeg4">
    <inertial>
      <origin
        xyz="0.0067471 -0.002134 -0.16536"
        rpy="0 0 0" />
      <mass
        value="3.08" />
      <inertia
        ixx="0.02722375"
        ixy="-2.97E-05"
        ixz="0.00022034"
        iyy="0.02797575"
        iyz="-0.00024168"
        izz="0.00330915" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RLeg4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision>
        <origin
                xyz="0 0 -0.15"
                rpy="0 0 0"/>
        <geometry>
            <cylinder length="0.2" radius="0.05"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="RLeg4"
    type="revolute">
    <origin
      xyz="0 0 -0.378"
      rpy="0 0 0" />
    <parent
      link="link_RLeg3" />
    <child
      link="link_RLeg4" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.087"
      upper="2.355"
      effort="360"
      velocity="5.55" />
  </joint>
  <link
    name="link_RLeg5">
    <inertial>
      <origin
        xyz="-7.72E-09 -0.00013125 6.01E-10"
        rpy="0 0 0" />
      <mass
        value="0.09" />
      <inertia
        ixx="1.55E-05"
        ixy="0"
        ixz="0"
        iyy="8.6E-06"
        iyz="0"
        izz="1.75E-05" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RLeg5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="RLeg5"
    type="revolute">
    <origin
      xyz="0 0 -0.406"
      rpy="0 0 0" />
    <parent
      link="link_RLeg4" />
    <child
      link="link_RLeg5" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.436"
      upper="0.436"
      effort="51"
      velocity="6.18" />
  </joint>
  <link
    name="link_RLeg6">
    <inertial>
      <origin
        xyz="0.05692 -0.00099969 -0.040842"
        rpy="0 0 0" />
      <mass
        value="0.7" />
      <inertia
        ixx="0.00050723"
        ixy="9.77E-06"
        ixz="0.00013806"
        iyy="0.00295264"
        iyz="-8.07E-06"
        izz="0.00317141" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/RLeg6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual> -->
    <collision name="r_foot_1">
        <origin rpy="0 1.5708 0" xyz="0.05 0.02 -0.035"/>
        <geometry>
            <cylinder length="0.24" radius="0.02"/>
        </geometry>
    </collision>
    <collision name="r_foot_2">
        <origin rpy="0 1.5708 0" xyz="0.05 -0.02 -0.035"/>
        <geometry>
            <cylinder length="0.24" radius="0.02"/>
        </geometry>
    </collision>
  </link>
  <joint
    name="RLeg6"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="0 0 0" />
    <parent
      link="link_RLeg5" />
    <child
      link="link_RLeg6" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.785"
      upper="0.785"
      effort="29.1"
      velocity="13.61" />
  </joint>
  <link
    name="imu_link">
    <inertial>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <mass
        value="0" />
      <inertia
        ixx="0"
        ixy="0"
        ixz="0"
        iyy="0"
        iyz="0"
        izz="0" />
    </inertial>
    <!-- <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="../meshes/imu_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual> -->
  </link>
  <joint
    name="imu_joint"
    type="fixed">
    <origin
      xyz="-0.059 0 -0.068"
      rpy="0 0 0" />
    <parent
      link="base" />
    <child
      link="imu_link" />
    <axis
      xyz="0 0 0" />
  </joint>
</robot>