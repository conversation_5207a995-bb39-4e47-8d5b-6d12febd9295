<mujoco model="scene">
  <include file="AzureLoong.xml"/>

    <visual>
        <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
        <rgba haze="0.15 0.25 0.35 1"/>
        <global azimuth="150" elevation="-5"/>
    </visual>

  <asset>
    <texture name="skybox" type="skybox" builtin="gradient" rgb1=".2 .3 .4" rgb2=".1 .15 0.2" width="512" height="3072"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3"
             markrgb="0.8 0.8 0.8" width="300" height="300"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2"/>
    <texture name="block" type="2d" file="asset/block.png"/>
    <material name="block" texture="block" reflectance="0"  />
  </asset>

  <worldbody>
      <light pos="0 0 3" dir="0 0 -1" directional="true" diffuse="0.6 0.6 0.6" specular=".3 .3 .3"/>
   <geom name="floor" size="0 0 0.05" type="plane" material="groundplane" condim="3"/>
    <geom type="box" size=".3 .6 .025" pos="1 0.4 .025" material="block" euler = "0 0 0.5"/>
    <geom type="box" size=".3 .6 .025" pos="3 0.4 .025" material="block" euler = "0 0 -0.3"/>
    <geom type="box" size=".3 .6 .025" pos="5 0.4 .025" material="block" euler = "0 0 0.2"/>
 </worldbody>


</mujoco>
