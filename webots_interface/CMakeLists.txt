cmake_minimum_required(VERSION 3.0)
add_definitions(-Wall -O2 -g)
get_filename_component(PROJECT ${CMAKE_SOURCE_DIR} NAME)
project(${PROJECT})

add_definitions(-Wall -O3 -g)
# add_subdirectory(bin)
SET(WEBOTS_HOME /usr/local/webots/)
link_directories(${WEBOTS_HOME}/lib/controller)
set (LIBRARIES ${CMAKE_SHARED_LIBRARY_PREFIX}Controller${CMAKE_SHARED_LIBRARY_SUFFIX} ${CMAKE_SHARED_LIBRARY_PREFIX}CppController${CMAKE_SHARED_LIBRARY_SUFFIX})
include_directories(${WEBOTS_HOME}/include/controller/c ${WEBOTS_HOME}/include/controller/cpp)
message(STATUS "webots:${WEBOTS_HOME}")

# set(CMAKE_PREFIX_PATH ${PROJECT_SOURCE_DIR}/libtorch/share/cmake/Torch)
# find_package(Torch REQUIRED)

add_executable(webots_interface ${PROJECT_SOURCE_DIR}/src/minirobotinterface.cpp)
target_link_libraries(webots_interface "${TORCH_LIBRARIES}")
target_link_libraries(webots_interface ${LIBRARIES} BasicFunction joystick_rxy StateMachine DataPackage)