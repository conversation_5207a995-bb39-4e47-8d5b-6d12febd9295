# # Generate the test
SET(WEBOTS_HOME /usr/local/webots/)
link_directories(${WEBOTS_HOME}/lib/controller)
set (LIBRARIES ${CMAKE_SHARED_LIBRARY_PREFIX}Controller${CMAKE_SHARED_LIBRARY_SUFFIX} ${CMAKE_SHARED_LIBRARY_PREFIX}CppController${CMAKE_SHARED_LIBRARY_SUFFIX})
include_directories(${WEBOTS_HOME}/include/controller/c ${WEBOTS_HOME}/include/controller/cpp)
message(STATUS "webots:${WEBOTS_HOME}")

# set(CMAKE_PREFIX_PATH ${PROJECT_SOURCE_DIR}/libtorch/share/cmake/Torch)
# find_package(Torch REQUIRED)

add_executable(webots_interface ${PROJECT_SOURCE_DIR}/src/minirobotinterface.cpp)
target_link_libraries(webots_interface "${TORCH_LIBRARIES}")
target_link_libraries(webots_interface ${LIBRARIES} BasicFunction)
# install
INSTALL(TARGETS webots_interface BasicFunction 
        RUNTIME DESTINATION webotssim/controllers/webots_interface
        LIBRARY DESTINATION webotssim/controllers/webots_interface
        ARCHIVE DESTINATION webotssim/controllers/webots_interface)