// #include <torch/script.h>
#include <iostream>
#include <fstream>
#include <string>
#include <vector> 
#include <stdio.h>  
#include <Eigen/Dense>
#include <chrono>
#include <thread>
#include <sys/time.h>
#include <unistd.h>
#include <stdlib.h>

// #include <webots/Robot.hpp>
#include <webots/Supervisor.hpp>
#include <webots/PositionSensor.hpp>
#include <webots/TouchSensor.hpp>
#include <webots/InertialUnit.hpp>
#include <webots/Gyro.hpp>
#include <webots/Accelerometer.hpp>
#include <webots/Field.hpp>
#include <webots/Motor.hpp>

#include "../../../../BasicFunction/include/basicfunction.hpp"
#include"../../../../DataPackage/include/DataPackage.h"
#include"../../../../StateMachine/include/GaitGenerator.h"
#include"../../../../JoyStick_rxy/include/joystick_rxy.h"
#define TEST_TOR
#define JOYSTICK
// #define MOTORNUM 10;
using namespace webots;
Eigen::Matrix<Motor *, 10, 1> RobotMotor;
Eigen::Matrix<PositionSensor *, 10, 1> RobotMotorSensor;

//find, match and enable device
void FindAndEnbleDevice(webots::Robot *robot, int timeStep);
// get Motor position feedback, left -> right
void GetRobotMotorPosition(Eigen::Matrix<double, 10, 1> &MotorPos);
// get Motor velocity feedback, left -> right
void GetRobotMotorVelocity(Eigen::Matrix<double, 10, 1> &MotorVel);
// get Motor acceleration feedback, left -> right
void GetRobotMotorAcceleration(Eigen::Matrix<double, 10, 1> &MotorAcc);
// get motor torque feedback, left -> right
void GetRobotMotorTorque(Eigen::Matrix<double, 10, 1> &MotorTor);
//set motor pid
void SetMotorPID(double kp, double ki, double kd);
void SetMotorPosition(Eigen::Matrix<double, 10, 1> desiredMotorPos);
// set motor velocity
void SetMotorVelocity(Eigen::Matrix<double, 10, 1> desiredMotorVel);
// set  motor torque
void SetMotorTorque(Eigen::Matrix<double, 10, 1>& desiredMotorTor);

int main(){
    // webots init
    std::cout<<"start program!"<<std::endl;
    // create the Robot instance.
    // webots::Robot *robot = new webots::Robot();
    Supervisor *robot = new Supervisor();
    const int motorNum = 10;
    int floatingNum = 6;
        // joy stick
    #ifdef JOYSTICK
        Joystick_rxy joy;
        joy.init();
    #endif
    // get the time step of the current world.
    int timeStep = (int)robot->getBasicTimeStep();

    FindAndEnbleDevice(robot, timeStep);

    SetMotorPID(20, 0, 0.001);
    // robot global position
    Node *node_robot = robot->getFromDef("minirobot");
    //current simulation time
    double curtime = 0.0;
    int simcount = 0;
    
    // Robot motor position and torque: mobile waist and arm
    Eigen::Matrix<double, motorNum, 1> RobotMotorPos = Eigen::MatrixXd::Zero(motorNum, 1);
    Eigen::Matrix<double, motorNum, 1> RobotMotorVel = Eigen::MatrixXd::Zero(motorNum, 1);
    Eigen::Matrix<double, motorNum, 1> RobotMotorAcc = Eigen::MatrixXd::Zero(motorNum, 1);
    Eigen::Matrix<double, motorNum, 1> RobotMotorTor = Eigen::MatrixXd::Zero(motorNum, 1);
    // last value
    Eigen::Matrix<double, motorNum, 1> RobotMotorPos_last = Eigen::MatrixXd::Zero(motorNum, 1);
    Eigen::Matrix<double, motorNum, 1> RobotMotorVel_last = Eigen::MatrixXd::Zero(motorNum, 1);
    Eigen::Matrix<double, motorNum, 1> RobotMotorAcc_last = Eigen::MatrixXd::Zero(motorNum, 1);
    Eigen::Matrix<double, motorNum, 1> RobotMotorTor_last = Eigen::MatrixXd::Zero(motorNum, 1);

    //desired Pos:
    Eigen::Matrix<double, motorNum, 1> desiredLegMotorPos = Eigen::MatrixXd::Zero(motorNum, 1);
    //desired vel:
    Eigen::Matrix<double, motorNum, 1> desiredLegMotorVel = Eigen::MatrixXd::Zero(motorNum, 1);
    //desired Tor
    Eigen::Matrix<double, motorNum, 1> desiredLegMotorTor = Eigen::MatrixXd::Zero(motorNum, 1);
    
    //init static data
    bool init_flag = 0;

    // floatingbase rpy and angular velocity
    Eigen::Vector3d base_rpy = Eigen::Vector3d::Zero();
    Eigen::Vector3d base_ang = Eigen::Vector3d::Zero();

    Eigen::VectorXd Kp_gain = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd Kd_gain = Eigen::VectorXd::Zero(motorNum);

    Kp_gain << 5.0, 5.0, 5.0, 10.0, 5.0,
                5.0, 5.0, 5.0, 10.0, 5.0;
    Kd_gain << 0.5, 0.5, 0.5, 1.0, 0.5,
                0.5, 0.5, 0.5, 1.0, 0.5;
    // 
    Eigen::VectorXd qEst = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd qDotEst = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd qTorEst = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd qCmd = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd qDotCmd = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd qTorCmd = Eigen::VectorXd::Zero(motorNum);
    // 
    // init datapakage
    DataPackage * data = new DataPackage();
    data->init(motorNum,floatingNum,timeStep*0.001);
    // init state machine
    GaitGenerator gait;
    gait.init(data);
    while (robot->step(timeStep) != -1)
    {
        
        RobotMotorPos_last = RobotMotorPos;
        RobotMotorVel_last = RobotMotorVel;
        GetRobotMotorPosition(RobotMotorPos);
        
        // if(init_flag == 0)
        // {
        // RobotMotorPos_last = RobotMotorPos;
        // RobotMotorPos_init = RobotMotorPos;
        // init_flag = 1;
        // }   
        RobotMotorVel = (RobotMotorPos - RobotMotorPos_last) / (timeStep/1000.0);
        RobotMotorAcc =  (RobotMotorVel - RobotMotorVel_last) / (timeStep/1000.0);
        #ifdef TEST_TOR
            RobotMotorTor = desiredLegMotorTor;
        #endif

        Eigen::Matrix3d R_fb;
        const double* p_fb = node_robot->getOrientation();
        for(int i = 0; i<3; i++){
            for(int j = 0;j<3;j++){
                R_fb(i,j) = p_fb[i*3 + j];
            }
        }    
           
        basicfunction::matrixtoeulerxyz_(R_fb,base_rpy);
        p_fb = node_robot->getVelocity();// the first three is linear velocity the second three is angular velocity in global coord
        for(int i = 0; i < 3; i++)
        {
            base_ang(i) =  p_fb[i+3];
        }
        // // trans to floating coord
        // base_ang = R_fb.transpose()*base_ang;

        // ---------------------------------------------- //
        qEst = RobotMotorPos;
        qDotEst = RobotMotorVel;
        qTorEst = RobotMotorTor;    
        data->q_a.segment(3,3) = base_rpy;
        data->q_a.segment(floatingNum,motorNum) = qEst;
        data->q_dot_a.segment(3,3) = base_ang;
        data->q_dot_a.segment(floatingNum,motorNum) = qDotEst;
        data->tau_a.segment(floatingNum,motorNum) = qTorEst;
        
        #ifdef JOYSTICK
            gait.setevent(joy.get_state_change());
            gait.set_current_fsm_command(joy.get_current_state_command());
            if(gait.fsmstatename == "NLP"){
                gait.setvelocity(joy.get_walk_x_direction_speed(),
                                joy.get_walk_y_direction_speed(),
                                joy.get_walk_yaw_direction_speed());
            }
        #endif
        gait.gait_run();
         
        qCmd = data->q_c.segment(floatingNum,motorNum);
        qDotCmd = data->q_dot_c.segment(floatingNum, motorNum);
        qTorCmd = data->tau_c.segment(floatingNum,motorNum);   

        
        // ---------------------------------------------- //
        for(int i = 0; i< motorNum;i++){
            desiredLegMotorTor(i) = Kp_gain(i)*(qCmd(i) - RobotMotorPos(i)) - Kd_gain(i)*RobotMotorVel(i);
        }    
        // std::cout<<"qCmd - RobotMotorPos: "<<(qCmd - RobotMotorPos).transpose()<<std::endl;
        // std::cout<<"RobotMotorVel: "<<RobotMotorVel.transpose()<<std::endl;
        // std::cout<<"desiredLegMotorTor: "<<desiredLegMotorTor.transpose()<<std::endl;
        SetMotorTorque(desiredLegMotorTor);
        // time
        curtime += timeStep * 0.001;
        simcount += 1;
    }



}

//find, match and enable device
void FindAndEnbleDevice(webots::Robot *robot, int timeStep)
{
  //robot motor
  //left arm
  RobotMotor[0] = robot->getMotor("lefthippitch");
  RobotMotor[1] = robot->getMotor("lefthiproll");
  RobotMotor[2] = robot->getMotor("lefthipyaw");
  RobotMotor[3] = robot->getMotor("leftkneepitch");
  RobotMotor[4] = robot->getMotor("leftanklepitch");
  //right arm
  RobotMotor[5] = robot->getMotor("righthippitch");
  RobotMotor[6] = robot->getMotor("righthiproll");
  RobotMotor[7] = robot->getMotor("righthipyaw");
  RobotMotor[8] = robot->getMotor("rightkneepitch");
  RobotMotor[9] = robot->getMotor("rightanklepitch");
#ifdef DEBUG
  std::cout << "GetRobotMotor is ok!" << std::endl;
#endif

  //enable torque feed back
  //note: if the motor is torque mode, the feed back torque is equal to the input torque
  for (int LoopI = 0; LoopI < RobotMotor.rows(); LoopI++)
  {
    RobotMotor[LoopI]->enableTorqueFeedback(timeStep);
  }
std::cout << "GetRobotMotor is ok!" << std::endl;
  //robot motor sensor
  //left arm
  RobotMotorSensor[0] = robot->getPositionSensor("lefthippitch_sensor");
  RobotMotorSensor[1] = robot->getPositionSensor("lefthiproll_sensor");
  RobotMotorSensor[2] = robot->getPositionSensor("lefthipyaw_sensor");
  RobotMotorSensor[3] = robot->getPositionSensor("leftkneepitch_sensor");
  RobotMotorSensor[4] = robot->getPositionSensor("leftanklepitch_sensor");
  //right arm
  RobotMotorSensor[5] = robot->getPositionSensor("righthippitch_sensor");
  RobotMotorSensor[6] = robot->getPositionSensor("righthiproll_sensor");
  RobotMotorSensor[7] = robot->getPositionSensor("righthipyaw_sensor");
  RobotMotorSensor[8] = robot->getPositionSensor("rightkneepitch_sensor");
  RobotMotorSensor[9] = robot->getPositionSensor("rightanklepitch_sensor");

  //enable position sensor
  for (int LoopI = 0; LoopI < RobotMotorSensor.rows(); LoopI++)
  {
    RobotMotorSensor[LoopI]->enable(timeStep);
  }
#ifdef DEBUG
  std::cout << "Get and Enable RobotMotorSensor is ok!" << std::endl;
#endif

#ifdef DEBUG
  // std::cout << "Get and Enable FTsensor is ok!" << std::endl;
#endif
// gyo_sensor = robot->getGyro("gyro");
// acc_sensor = robot->getAccelerometer("accelerometer");
// gyo_sensor->enable(timeStep);
// acc_sensor->enable(timeStep);
#ifdef DEBUG
  std::cout << "Get and Enable gyo and acc is ok!" << std::endl;
#endif
}

// get Motor position feedback, left -> right
void GetRobotMotorPosition(Eigen::Matrix<double, 10, 1> &MotorPos)
{
  MotorPos.setZero();

  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    MotorPos[LOOPI] = RobotMotorSensor[LOOPI]->getValue();
  }

}
// get Motor velocity feedback, left -> right
void GetRobotMotorVelocity(Eigen::Matrix<double, 10, 1> &MotorVel)
{
  MotorVel.setZero();

  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    MotorVel[LOOPI] = RobotMotor[LOOPI]->getVelocity();
  }
}
// get Motor acceleration feedback, left -> right
void GetRobotMotorAcceleration(Eigen::Matrix<double, 10, 1> &MotorAcc)
{
  MotorAcc.setZero();

  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    MotorAcc[LOOPI] = RobotMotor[LOOPI]->getAcceleration();
  }
}
// get motor torque feedback, left -> right
void GetRobotMotorTorque(Eigen::Matrix<double, 10, 1> &MotorTor)
{
  MotorTor.setZero();

  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    MotorTor[LOOPI] = RobotMotor[LOOPI]->getTorqueFeedback();
  }
}

//set motor pid
void SetMotorPID(double kp, double ki, double kd)
{
  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    RobotMotor[LOOPI]->setControlPID(kp, ki, kd);
  }
}

// set motor position
void SetMotorPosition(Eigen::Matrix<double, 10, 1> desiredMotorPos)
{


  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    RobotMotor[LOOPI]->setPosition(desiredMotorPos[LOOPI]);
  }
}
// set motor velocity
void SetMotorVelocity(Eigen::Matrix<double, 10, 1> desiredMotorVel)
{
  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    RobotMotor[LOOPI]->setPosition(INFINITY);
    RobotMotor[LOOPI]->setVelocity(desiredMotorVel[LOOPI]);
  }
}
// set motor torque
void SetMotorTorque(Eigen::Matrix<double, 10, 1>& desiredMotorTor)
{
  for (int LOOPI = 0; LOOPI < 10; LOOPI++)
  {
    // if (desiredMotorTor[LOOPI] > TOR_LIMIT)
    // {
    //   desiredMotorTor[LOOPI] = TOR_LIMIT;
    // } else if (desiredMotorTor[LOOPI] < -TOR_LIMIT)
    // {
    //   desiredMotorTor[LOOPI] = -TOR_LIMIT;
    // } 

    RobotMotor[LOOPI]->setTorque(desiredMotorTor[LOOPI]);
  }
}
