#ifndef STATEESTIMATOR_H
#define STATEESTIMATOR_H

#include "DataPackage/include/DataPackage.h"
#include <pinocchio/algorithm/centroidal.hpp>
#include <pinocchio/algorithm/crba.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/rnea.hpp>
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/kinematics.hpp"
#include "pinocchio/algorithm/kinematics-derivatives.hpp"
#include "pinocchio/algorithm/center-of-mass.hpp"
#include "StateEstimator/include/LinearKalmanFilter.h"
#include "mujoco_interface.h"


class StateEstimator
{
public:   

    Eigen::VectorXd imudata {9};

    StateEstimator(DataPackage &data);



    
    void GetDataFromPackage(DataPackage &data);
    void SetDataToPackage(DataPackage &data);
    void Step(DataPackage &data);
    void CalculateFootPosition();

    std::vector<bool> contact_flag;

    Eigen::Matrix<double, 6, Eigen::Dynamic> rightfoot_jaco<PERSON>;


    inline Eigen::Vector3d rotm2zyx(Eigen::Matrix3d zyx)
    {
        Eigen::Vector3d rpy;
        rpy(1) = asin(zyx(0,2));
        if (abs(rpy(1) - M_PI/2) < 1.0e-3) 
        {
            rpy(0) = 0.0;
            rpy(2) = atan2(zyx(1,2) - zyx(0,1), zyx(0,2) + zyx(1,1)) + rpy(0);
        }

        else if (abs(rpy(1) + M_PI/2) < 1.0e-3) 
        {
            rpy(0) = 0.0;
            rpy(2)= atan2(zyx(1,2) - zyx(0,1), zyx(0,2) + zyx(1,1)) - rpy(0);
        }

        else 
        {
            rpy(2) = atan2(-zyx(1,2), zyx(2,2));
            rpy(0)= atan2(-zyx(0,1), zyx(0,0));
        }
        return rpy;
    };

    void processRealIMUData(const Eigen::Vector3d& zyx, const Eigen::Vector3d& vel, const Eigen::Vector3d& acc);



private:

    KalmanFilterEstimate kalman_filter;
    
    pinocchio::Model model_est;

    pinocchio::Data data_est;
    

    void estContactForce();
    // contact_flag_t estContactState();



    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_actual;
 
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_dot_actual;

    Eigen::Matrix<double, Eigen::Dynamic,1> torque_actual;



    Eigen::Quaternion<double> imu_quat;      
    Eigen::Matrix<double, 4,1> baseQuat;      
    Eigen::Matrix<double, 3,1> rpy;      
    Eigen::Matrix<double, 3,1> imu_angular_vel;    
    Eigen::Matrix<double, 3,1> imu_lin_acc;    
    Eigen::Matrix<double, 3,1> global_angular_vel;   
    Eigen::Matrix<double, 3,1> imu_zyx; 
    Eigen::Matrix<double, 3,1> imu_xyz; 
    
    Eigen::Quaternion<double> real_imu_quat; 
    Eigen::Matrix<double, 3,1> real_imu_angular_vel; 
    Eigen::Matrix<double, 3,1> real_imu_lin_acc;  
    Eigen::Matrix<double, 3,1> real_imu_zyx; 


    Eigen::Matrix<double, 3, 1> quatToZyx(const Eigen::Quaternion<double>& q);
    Eigen::Matrix<double, 3, 1> getEulerAnglesZyxDerivativesFromLocalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& angularVelocity);
    Eigen::Matrix<double, 3, 1> getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles);
    Eigen::Matrix<double, 3, 1> getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& angularVelocity);
    Eigen::Matrix2d rotation_matrix_plane(double angle);
    Eigen::Vector3d getEulerZYXFromRotationMatrix(const Eigen::Matrix3d& R);


    double control_period;
    double cutoff_frequency;
    int numThreeDofContacts;
    int generalizedCoordinatesNum;
    int actuatedDofNum;
    int SingleLegNum;

    Eigen::Matrix<double, Eigen::Dynamic,1> pSCgZinv_last;
    Eigen::Matrix<double, Eigen::Dynamic,1> contact_force_normal;
    Eigen::Matrix<double, Eigen::Dynamic,1> est_ext_torque;

    Eigen::Matrix<double, 3, 1> feet_r_Pos_W_actual, feet_l_Pos_W_actual;
    Eigen::Matrix<double, 3, 1> hip_r_Pos_W_actual, hip_l_Pos_W_actual;

    Eigen::Matrix<double, 3, 3> feet_r_Rot_W_actual, feet_l_Rot_W_actual;
    double feet_l_EulerZ_W_actual, feet_r_EulerZ_W_actual;
    
    Eigen::Vector3d feet_r_EulerZYX_W_actual, feet_l_EulerZYX_W_actual;
    
    Eigen::Vector3d feet_r_LinVel_W_actual, feet_l_LinVel_W_actual;
    Eigen::Vector3d feet_r_AngVel_W_actual, feet_l_AngVel_W_actual;

    std::vector <int> endEffectorFrameIndices;

    std::vector<double> speed_forward_history;
    double speed_forward_avg{0};
    double forward_speed;
    Eigen::Matrix<double, 3, 1> zyx;



    pinocchio::JointIndex leg_l6_joint;
    pinocchio::JointIndex leg_r6_joint;

    pinocchio::JointIndex leg_l2_joint;
    pinocchio::JointIndex leg_r2_joint;


    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> s;
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> Jac_i;
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> S_li;
    Eigen::Matrix<double, 6, Eigen::Dynamic> jac;

    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>S_JT;

    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>S_tau;
    
    Eigen::Matrix<double, Eigen::Dynamic,1> p;

    Eigen::Matrix<double, Eigen::Dynamic,1> pSCg;

    Eigen::Matrix<double, Eigen::Dynamic,1> pSCg_z_inv;
};

inline double normalizeAngle(double angle_rad) {
    return std::atan2(std::sin(angle_rad), std::cos(angle_rad));
}
#endif // STATEESTIMATOR_H