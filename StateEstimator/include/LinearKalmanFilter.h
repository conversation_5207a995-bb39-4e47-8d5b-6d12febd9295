
#pragma once


#include "DataPackage/include/DataPackage.h"
#include <pinocchio/algorithm/centroidal.hpp>
#include <pinocchio/algorithm/crba.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/rnea.hpp>
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/kinematics.hpp"
#include "pinocchio/algorithm/kinematics-derivatives.hpp"
#include "pinocchio/algorithm/center-of-mass.hpp"

class KalmanFilterEstimate 

{

public:


    void init(DataPackage &data);

    void update(Eigen::Matrix<double, Eigen::Dynamic, 1> &q_measured, Eigen::Matrix<double, Eigen::Dynamic, 1> &v_measured, 
    Eigen::Matrix<double, 3, 1> & imu_zyx, Eigen::Matrix<double, 3, 1> & imu_lin_acc, std::vector<bool> & contact_flag, DataPackage &data);
    Eigen::Matrix<double, 3, 3> getRotationMatrixFromZyxEulerAngles(const Eigen::Matrix<double, 3, 1>& eulerAngles);

protected:

    Eigen::Matrix<double, Eigen::Dynamic, 1> feet_height;

    // Config
    double footRadius;
    double imuProcessNoisePosition;
    double imuProcessNoiseVelocity;
    double footProcessNoisePosition;
    double footSensorNoisePosition;
    double footSensorNoiseVelocity;
    double footHeightSensorNoise;
    double control_period;

  private:
    Eigen::Matrix<double, 30, 30> q;
    Eigen::Matrix<double, 56, 56>  r;
    Eigen::Matrix<double, 30, 1> xHat_;
    Eigen::Matrix<double, 24, 1> ps_;
    Eigen::Matrix<double, 24, 1> vs_;
    Eigen::Matrix<double, 30, 30> a_;
    Eigen::Matrix<double, 30, 30> q_;
    Eigen::Matrix<double, 30, 30> p_;
    Eigen::Matrix<double, 56, 56> r_;
    Eigen::Matrix<double, 30, 3> b_;
    Eigen::Matrix<double, 56, 30> c_;

    Eigen::Matrix<double, 56, 1> y;
    Eigen::Matrix<double, 30, 30> at;
    Eigen::Matrix<double, 30, 30> pm;  
    Eigen::Matrix<double, 30, 56> cT;
    Eigen::Matrix<double, 56, 1> yModel;
    Eigen::Matrix<double, 56, 1> ey;        
    Eigen::Matrix<double, 56, 56> s ;  

    Eigen::Matrix<double, 56, 1> sEy;  

    Eigen::Matrix<double, 56, 30> sC;

    Eigen::Matrix<double, 30, 30> pt;

    pinocchio::Model model_kalman;

    pinocchio::Data data_kalman;
    Eigen::Matrix<double, 3, 3> ones3 = Eigen::Matrix<double, 3, 3>::Identity();
    Eigen::Matrix<double, 30, 30> ones30  = Eigen::Matrix<double, 30, 30>::Identity();
    int numThreeDofContacts;
    int generalizedCoordinatesNum;
    int actuatedDofNum;
    Eigen::Matrix<double, 3, 3> rotationMatrix;
    // std::vector<bool> contact_flag;
    const int fn = numThreeDofContacts;
    Eigen::Matrix<double, 3, 1> g;
    Eigen::Matrix<double, 3, 1> accel;
    std::vector <int> endEffectorFrameIndices;

    Eigen::PartialPivLU<Eigen::Matrix<double, 56, 56> >  lusolver;

    int i1 ;

    int qIndex;
    int rIndex1;
    int rIndex2;
    int rIndex3;

    bool isContact;

};


