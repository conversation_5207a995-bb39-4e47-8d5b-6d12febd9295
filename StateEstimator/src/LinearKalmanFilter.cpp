

#include <pinocchio/fwd.hpp>

#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/kinematics.hpp>

#include "StateEstimator/include/LinearKalmanFilter.h"
#include <chrono>



void KalmanFilterEstimate::init(DataPackage& data)
{
    std::string file_path = "../StateEstimator/config/stateestimator.yaml";
    YAML::Node config = YAML::LoadFile(file_path);

    footRadius = config["footRadius"].as<double>();
    imuProcessNoisePosition = config["imuProcessNoisePosition"].as<double>();
    imuProcessNoiseVelocity = config["imuProcessNoiseVelocity"].as<double>();
    footProcessNoisePosition = config["footProcessNoisePosition"].as<double>();
    footSensorNoisePosition = config["footSensorNoisePosition"].as<double>();
    footSensorNoiseVelocity = config["footSensorNoiseVelocity"].as<double>();
    footHeightSensorNoise = config["footHeightSensorNoise"].as<double>();


    model_kalman = data.robot_model;

    data_kalman = data.robot_data;
    
    generalizedCoordinatesNum = data.generalizedCoordinatesNum;
    actuatedDofNum = data.actuatedDofNum;

    xHat_.setZero();
    ps_.setZero();
    vs_.setZero();
    a_.setZero();
    a_.block(0, 0, 3, 3) = Eigen::Matrix<double, 3, 3>::Identity();
    a_.block(3, 3, 3, 3) = Eigen::Matrix<double, 3, 3>::Identity();
    a_.block(6, 6, 24, 24) = Eigen::Matrix<double, 24, 24>::Identity();
    b_.setZero();

    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> c1(3, 6);
    c1 << Eigen::Matrix<double, 3, 3>::Identity(), Eigen::Matrix<double, 3, 3>::Zero();
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> c2(3, 6);
    c2 << Eigen::Matrix<double, 3, 3>::Zero(), Eigen::Matrix<double, 3, 3>::Identity();
    c_.setZero();

    c_.block(0, 0, 3, 6) = c1;
    c_.block(3, 0, 3, 6) = c1;
    c_.block(6, 0, 3, 6) = c1;
    c_.block(9, 0, 3, 6) = c1;
    c_.block(12, 0, 3, 6) = c1;
    c_.block(15, 0, 3, 6) = c1;
    c_.block(18, 0, 3, 6) = c1;
    c_.block(21, 0, 3, 6) = c1;
    c_.block(0, 6, 24, 24) = -Eigen::Matrix<double, 24, 24>::Identity();
    
    c_.block(24, 0, 3, 6) = c2;
    c_.block(27, 0, 3, 6) = c2;
    c_.block(30, 0, 3, 6) = c2;
    c_.block(33, 0, 3, 6) = c2;
    c_.block(36, 0, 3, 6) = c2;
    c_.block(39, 0, 3, 6) = c2;
    c_.block(42, 0, 3, 6) = c2;
    c_.block(45, 0, 3, 6) = c2;


    c_(55, 29) = 1.0;
    c_(54, 26) = 1.0;
    c_(53, 23) = 1.0;
    c_(52, 20) = 1.0;
    c_(51, 17) = 1.0;
    c_(50, 14) = 1.0;
    c_(49, 11) = 1.0;
    c_(48, 8) = 1.0;
    p_.setIdentity();
    p_ = 100. * p_;
    q_.setIdentity();
    r_.setIdentity();
    control_period = data.control_period;
    numThreeDofContacts = data.foot_contact_size;

    // feet_height.setZero(numThreeDofContacts);
    feet_height.resize(numThreeDofContacts);
    feet_height.setConstant(0);
    // eeKinematics_->setPinocchioInterface(pinocchioInterface_);
    endEffectorFrameIndices = data.endEffectorFrameIndices;
    q = Eigen::Matrix<double, 30, 30>::Identity();
    r = Eigen::Matrix<double, 56, 56>::Identity();

}


void KalmanFilterEstimate::update(Eigen::Matrix<double, Eigen::Dynamic, 1> &q_measured, Eigen::Matrix<double, Eigen::Dynamic, 1> &v_measured, Eigen::Matrix<double, 3, 1> & imu_zyx, Eigen::Matrix<double, 3, 1> & imu_lin_acc, std::vector<bool> & contact_flag, DataPackage &data)
{




    a_.block(0, 3, 3, 3) = control_period * ones3;
    b_.block(0, 0, 3, 3) = 0.5 * control_period * control_period * ones3;
    b_.block(3, 0, 3, 3) = control_period * ones3;
    q_.block(0, 0, 3, 3) = (control_period / 20.f) * ones3;
    q_.block(3, 3, 3, 3) = (control_period * 9.81f / 20.f) * ones3;
    q_.block(6, 6, 24, 24) = control_period * Eigen::Matrix<double, 24, 24>::Identity();

    // // const auto& model = pinocchioInterface_.getModel();
    // int actuatedDofNum = actuatedDofNum;

    // Eigen::Matrix<double, Eigen::Dynamic, 1> qPino(generalizedCoordinatesNum);
    // Eigen::Matrix<double, Eigen::Dynamic, 1> vPino(generalizedCoordinatesNum);
    // qPino.setZero();
    // qPino.segment<3>(3) = rbdState_.head<3>();  // Only set orientation, let position in origin.
    // qPino.tail(actuatedDofNum) = rbdState_.segment(6, actuatedDofNum);

    // vPino.setZero();
    // vPino.segment<3>(3) = getEulerAnglesZyxDerivativesFromGlobalAngularVelocity<double>(
    //     qPino.segment<3>(3),
    //     rbdState_.segment<3>(info_.generalizedCoordinatesNum));  // Only set angular velocity, let linear velocity be zero
    // vPino.tail(actuatedDofNum) = rbdState_.segment(6 + info_.generalizedCoordinatesNum, actuatedDofNum);

    pinocchio::forwardKinematics(model_kalman, data_kalman, q_measured, v_measured);

    pinocchio::updateFramePlacements(model_kalman, data_kalman);
  

    // for (const auto& frameId : endEffectorFrameIndices) {
    //   eePos.emplace_back(data_kalman.oMf[frameId].translation());
    // }

    // for (const auto& frameId : endEffectorFrameIndices) {
    //   eeVel.emplace_back(pinocchio::getFrameVelocity(model_kalman, data_kalman, frameId, pinocchio::LOCAL_WORLD_ALIGNED).linear());
    // }

    // const auto eePos = eeKinematics_->getPosition(Eigen::Matrix<double, Eigen::Dynamic, 1>());
    // const auto eeVel = eeKinematics_->getVelocity(Eigen::Matrix<double, Eigen::Dynamic, 1>(), Eigen::Matrix<double, Eigen::Dynamic, 1>());

    // the covariance of the process noise
    // Eigen::Matrix<double, 30, 30> q = Eigen::Matrix<double, 30, 30>::Identity();
    q.block(0, 0, 3, 3) = q_.block(0, 0, 3, 3) * imuProcessNoisePosition;
    q.block(3, 3, 3, 3) = q_.block(3, 3, 3, 3) * imuProcessNoiseVelocity;
    q.block(6, 6, 24, 24) = q_.block(6, 6, 24, 24) * footProcessNoisePosition;

    // the covariance of the observation noise
    // Eigen::Matrix<double, 56, 56> r = Eigen::Matrix<double, 56, 56>::Identity();
    r.block(0, 0, 24, 24) = r_.block(0, 0, 24, 24) * footSensorNoisePosition;
    r.block(24, 24, 24, 24) = r_.block(24, 24, 24, 24) * footSensorNoiseVelocity;
    r.block(48, 48, fn, fn) = r_.block(48, 48, fn, fn) * footHeightSensorNoise;

    // std::cout << "contact_flag in estimate: ";
    // for (bool flag : contact_flag) {
    //     std::cout << flag << " ";
    // }
    // std::cout << std::endl;

    for (int i = 0; i < numThreeDofContacts; i++)
    {
      i1 = 3 * i;

      qIndex = 6 + i1;
      rIndex1 = i1;
      rIndex2 = 24 + i1;
      rIndex3 = 48 + i;

      isContact = contact_flag[i];

      double high_suspect_number(100);
      q.block(qIndex, qIndex, 3, 3) = (isContact ? 1. : high_suspect_number) * q.block(qIndex, qIndex, 3, 3);
      r.block(rIndex1, rIndex1, 3, 3) = (isContact ? 1. : high_suspect_number) * r.block(rIndex1, rIndex1, 3, 3);
      r.block(rIndex2, rIndex2, 3, 3) = (isContact ? 1. : high_suspect_number) * r.block(rIndex2, rIndex2, 3, 3);
      r(rIndex3, rIndex3) = (isContact ? 1. : high_suspect_number) * r(rIndex3, rIndex3);
      ps_.segment(3 * i, 3) = -data_kalman.oMf[endEffectorFrameIndices[i]].translation();
      // std::cout<<"i : "<<-eeVel[i]<<std::endl;
      ps_.segment(3 * i, 3)[2] += footRadius;
      vs_.segment(3 * i, 3) = -pinocchio::getFrameVelocity(model_kalman, data_kalman, endEffectorFrameIndices[i], pinocchio::LOCAL_WORLD_ALIGNED).linear();
    }

    g<<0, 0, -9.81;
    accel = getRotationMatrixFromZyxEulerAngles(imu_zyx) * imu_lin_acc + g;
    // Eigen::Matrix<double, 3, 1> accel =  imu_lin_acc + g;


    // observation (or measurement)

    y << ps_, vs_, feet_height;

    data.vel_base_obser = vs_.head(3);

    xHat_ = a_ * xHat_ + b_ * accel;  
    // std::cout<<"base_vel_est_before: "<<y<<std::endl;
    at = a_.transpose();
    pm = a_ * p_ * at + q;  
    cT = c_.transpose();
    yModel = c_ * xHat_;
    ey = y - yModel;        
    s = c_ * pm * cT + r;  

    sEy = s.lu().solve(ey);  
    xHat_ += pm * cT * sEy;                                 

    sC = s.lu().solve(c_);
    p_ = (ones30 - pm * cT * sC) * pm;  

    pt = p_.transpose();
    p_ = (p_ + pt) / 2.0;

    if (p_.block(0, 0, 2, 2).determinant() > 0.000001)
    {
      p_.block(0, 2, 2, 16).setZero();
      p_.block(2, 0, 16, 2).setZero();
      p_.block(0, 0, 2, 2) /= 10.;
    }

    q_measured.segment(0,3) = xHat_.segment<3>(0);
    v_measured.segment(0,3) = xHat_.segment<3>(3);
  

}



Eigen::Matrix<double, 3, 3> KalmanFilterEstimate::getRotationMatrixFromZyxEulerAngles(const Eigen::Matrix<double, 3, 1>& eulerAngles) 
{
    const double z = eulerAngles(0);
    const double y = eulerAngles(1);
    const double x = eulerAngles(2);

    const double c1 = cos(z);
    const double c2 = cos(y);
    const double c3 = cos(x);
    const double s1 = sin(z);
    const double s2 = sin(y);
    const double s3 = sin(x);

    const double s2s3 = s2 * s3;
    const double s2c3 = s2 * c3;

    // clang-format off
    
    rotationMatrix << c1 * c2,      c1 * s2s3 - s1 * c3,       c1 * s2c3 + s1 * s3,
                      s1 * c2,      s1 * s2s3 + c1 * c3,       s1 * s2c3 - c1 * s3,
                          -s2,                  c2 * s3,                   c2 * c3;
    // clang-format on
    return rotationMatrix;
}


// void KalmanFilterEstimate::loadSettings(const std::string& taskFile, bool verbose)
// {
//   boost::property_tree::ptree pt;
//   boost::property_tree::read_info(taskFile, pt);
//   std::string prefix = "kalmanFilter.";
//   if (verbose)
//   {
//     std::cerr << "\n #### Kalman Filter Noise:";
//     std::cerr << "\n #### =============================================================================\n";
//   }

//   loadData::loadPtreeValue(pt, footRadius, prefix + "footRadius", verbose);
//   loadData::loadPtreeValue(pt, imuProcessNoisePosition, prefix + "imuProcessNoisePosition", verbose);
//   loadData::loadPtreeValue(pt, imuProcessNoiseVelocity, prefix + "imuProcessNoiseVelocity", verbose);
//   loadData::loadPtreeValue(pt, footProcessNoisePosition, prefix + "footProcessNoisePosition", verbose);
//   loadData::loadPtreeValue(pt, footSensorNoisePosition, prefix + "footSensorNoisePosition", verbose);
//   loadData::loadPtreeValue(pt, footSensorNoiseVelocity, prefix + "footSensorNoiseVelocity", verbose);
//   loadData::loadPtreeValue(pt, footHeightSensorNoise, prefix + "footHeightSensorNoise", verbose);
// }


