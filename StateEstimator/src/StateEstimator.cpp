#include "StateEstimator/include/StateEstimator.h"


void StateEstimator::processRealIMUData(const Eigen::Vector3d& zyx, const Eigen::Vector3d& vel, const Eigen::Vector3d& acc){

    Eigen::Matrix3d R_realIMU;
    Eigen::Matrix3d R_virtualIMU;
	Eigen::AngleAxisd x_Angle(Eigen::AngleAxisd(zyx[2],Eigen::Vector3d::UnitX()));
	Eigen::AngleAxisd y_Angle(Eigen::AngleAxisd(zyx[1],Eigen::Vector3d::UnitY()));
	Eigen::AngleAxisd z_Angle(Eigen::AngleAxisd(zyx[0],Eigen::Vector3d::UnitZ()));
    R_realIMU = x_Angle * y_Angle * z_Angle;

	Eigen::AngleAxisd x_Angle_1(Eigen::AngleAxisd(M_PI,Eigen::Vector3d::UnitX()));
    Eigen::AngleAxisd z_Angle_2(Eigen::AngleAxisd(-M_PI/2,Eigen::Vector3d::UnitZ()));
	R_virtualIMU = z_Angle_2 * x_Angle_1 * R_realIMU;

    Eigen::Vector3d rpy = rotm2zyx(R_virtualIMU);
    imu_zyx << rpy(2), rpy(1), rpy(0);

    imu_angular_vel =  z_Angle_2 * x_Angle_1 * vel;

    imu_lin_acc[0] = acc[1]*(-9.8); 
    imu_lin_acc[1] = acc[0]*(-9.8);
    imu_lin_acc[2] = acc[2]*(-9.8);
}


StateEstimator::StateEstimator(DataPackage &data)
{

    // std::cout << "StateEstimator " << std::endl;
    std::string file_path = "../StateEstimator/config/stateestimator.yaml";
    YAML::Node config = YAML::LoadFile(file_path);
    cutoff_frequency = config["cutoff_frequency"].as<double>();
    SingleLegNum = config["SingleLegNum"].as<int>();


    model_est = data.robot_model;

    data_est = data.robot_data;

    generalizedCoordinatesNum = data.generalizedCoordinatesNum;
    actuatedDofNum = data.actuatedDofNum;
    // SingleLegNum = actuatedDofNum/2;

    control_period = data.control_period;

    pSCgZinv_last.setZero(generalizedCoordinatesNum);
    // contact_force_normal;
    est_ext_torque.setZero(actuatedDofNum);

    contact_force_normal.resize(6 * 2 + 4);
    contact_force_normal.fill(50);

    endEffectorFrameIndices = data.endEffectorFrameIndices;
    
    generalized_q_dot_actual.setZero(generalizedCoordinatesNum);
    generalized_q_actual.setZero(generalizedCoordinatesNum);

    kalman_filter.init(data);

    leg_l6_joint=model_est.getJointId("left_ankle_roll");
    leg_r6_joint=model_est.getJointId("right_ankle_roll");

    leg_l2_joint=model_est.getJointId("left_hip_roll");
    leg_r2_joint=model_est.getJointId("right_hip_roll");
    s.setZero(actuatedDofNum, generalizedCoordinatesNum);
    jac.setZero(SingleLegNum, generalizedCoordinatesNum);
    Jac_i.setZero(SingleLegNum, generalizedCoordinatesNum);
    S_li.setZero(SingleLegNum, generalizedCoordinatesNum);


    S_JT.setZero(SingleLegNum, SingleLegNum);

    S_tau.setZero(SingleLegNum, 1);

    p.setZero(generalizedCoordinatesNum, 1);

    pSCg.setZero(generalizedCoordinatesNum, 1);

    pSCg_z_inv.setZero(generalizedCoordinatesNum, 1);


}

void StateEstimator::GetDataFromPackage(DataPackage &data)
{

    // real_imu_quat = data.imu_quat; 



    // real_imu_zyx = quatToZyx(real_imu_quat);

    // std::cout << "real_imu_zyx:" << real_imu_zyx << std::endl;

    // real_imu_angular_vel = data.imu_angular_vel;
    // real_imu_lin_acc = data.imu_lin_acc;

    // std::cout << "real_imu_lin_acc:" << real_imu_lin_acc << std::endl;

    // processRealIMUData(real_imu_zyx, real_imu_angular_vel, real_imu_lin_acc);

    // std::cout << "imu_zyx:" << imu_zyx << std::endl;
    // std::cout << "imu_angular_vel:" << imu_angular_vel << std::endl;
    // std::cout << "imu_lin_acc:" << imu_lin_acc << std::endl;

    // imu_quat = data.imu_quat;

    // imu_angular_vel[0] = data.imu_angular_vel[0];
    // imu_angular_vel[1] = -data.imu_angular_vel[1];
    // imu_angular_vel[2] = -data.imu_angular_vel[2];

    // data.imu_zyx = quatToZyx(data.imu_quat);

    imu_angular_vel = data.imu_angular_vel;
    imu_zyx = data.imu_zyx; 
    imu_lin_acc = data.imu_lin_acc;  
    // std::cout << "imu_zyx:" << imu_zyx << std::endl;

    // Eigen::Vector3d init_imu_zyx = quatToZyx(imu_quat); 
    // std::cout << "init_imu_zyx:" << init_imu_zyx << std::endl;

    // imu_zyx[0] = -init_imu_zyx[0];
    // imu_zyx[1] = init_imu_zyx[1];
    // imu_zyx[2] = init_imu_zyx[2] - 3.14;
    // std::cout << "imu_zyx:" << imu_zyx << std::endl;

    // imu_lin_acc[0] = data.imu_lin_acc[0];
    // imu_lin_acc[1] = -data.imu_lin_acc[1];
    // imu_lin_acc[2] = -data.imu_lin_acc[2];

    // std::cout << "real_imu_quat: ["
    // << imu_quat.w() << ", "
    // << imu_quat.x() << ", "
    // << imu_quat.y() << ", "
    // << imu_quat.z() << "]" << std::endl;


    torque_actual = data.motor_torque;

    // for(int i = 0; i<torque_actual.size(); ++i){
    //     std::cout << "torque_actual[" << i << "] = " << torque_actual[i] << std::endl;
    // }

    imu_zyx[0] = 0;//imu_zyx[0] - 1.57;
    
    global_angular_vel = getGlobalAngularVelocityFromEulerAnglesZyxDerivatives(imu_zyx, getEulerAnglesZyxDerivativesFromLocalAngularVelocity(imu_zyx, imu_angular_vel));


    generalized_q_actual.segment(0,3) << 0,0,0;
    generalized_q_actual.segment<3>(3) = imu_zyx;
    generalized_q_actual.tail(actuatedDofNum) = data.motor_pos;

    generalized_q_dot_actual.segment(0,3) << 0,0,0;
    generalized_q_dot_actual.segment<3>(3) = getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(imu_zyx, global_angular_vel);

    generalized_q_dot_actual.tail(actuatedDofNum) = data.motor_vel;

    contact_flag = data.contact_flag;
    
    contact_flag = data.contact_flag;
    imu_lin_acc = data.imu_lin_acc;
    
    speed_forward_history = data.speed_forward_history;
    
}

void StateEstimator::Step(DataPackage &data)
{
    // auto record_start_time = std::chrono::steady_clock::now();

    kalman_filter.update(generalized_q_actual, generalized_q_dot_actual, imu_zyx, imu_lin_acc, contact_flag, data);
    // std::cout<<"update"<<std::endl;
    // auto record_now_time = std::chrono::steady_clock::now();
    // std::chrono::duration<double> elapsed_record = record_now_time - record_start_time;
    // std::cout<< "est time:"<<elapsed_record.count()<<std::endl;
    // std::cout<< "CalculateFootPosition time:"<<elapsed_record.count()<<std::endl;
    // generalized_q_actual.head<3>() = data.base_pos;
    // generalized_q_actual.segment(3,3) = imu_zyx;
    // generalized_q_actual.tail(data.actuatedDofNum) = data.motor_pos;

    // generalized_q_dot_actual.head<3>() = data.base_lin_vel;
    // generalized_q_dot_actual.segment(3,3) =  getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(imu_zyx, global_angular_vel);
    // generalized_q_dot_actual.tail(data.actuatedDofNum) = data.motor_vel;


    estContactForce();

    // std::cout<<"estContactForce"<<std::endl;


    CalculateFootPosition();

    // std::cout<<"CalculateFootPosition"<<std::endl;

}

void StateEstimator::CalculateFootPosition()
{
    // pinocchio::Model model_footpos = model_est;
    // pinocchio::Data data_footpos = data_est;
    
    // pinocchio::JointIndex leg_l6_joint=model_footpos.getJointId("left_ankle_roll");
    // pinocchio::JointIndex leg_r6_joint=model_footpos.getJointId("right_ankle_roll");

    // pinocchio::JointIndex leg_l2_joint=model_footpos.getJointId("left_hip_roll");
    // pinocchio::JointIndex leg_r2_joint=model_footpos.getJointId("right_hip_roll");


    pinocchio::forwardKinematics(model_est, data_est, generalized_q_actual, generalized_q_dot_actual);
    pinocchio::computeJointJacobians(model_est, data_est);
    pinocchio::updateFramePlacements(model_est, data_est);

    feet_l_Pos_W_actual = data_est.oMi[leg_l6_joint].translation();
    hip_l_Pos_W_actual = data_est.oMi[leg_l2_joint].translation();
    feet_l_Rot_W_actual = data_est.oMi[leg_l6_joint].rotation();
    feet_r_Pos_W_actual = data_est.oMi[leg_r6_joint].translation();    
    hip_r_Pos_W_actual = data_est.oMi[leg_r2_joint].translation();    
    feet_r_Rot_W_actual = data_est.oMi[leg_r6_joint].rotation();   
    
    // 打印foot位置
    // std::cout << "feet_l_Pos_W_actual: " << feet_l_Pos_W_actual.transpose() << std::endl;
    // std::cout << "feet_r_Pos_W_actual: " << feet_r_Pos_W_actual.transpose() << std::endl;

    feet_r_EulerZYX_W_actual = getEulerZYXFromRotationMatrix(feet_r_Rot_W_actual);   
    feet_l_EulerZYX_W_actual = getEulerZYXFromRotationMatrix(feet_l_Rot_W_actual);   

    // std::cout << "feet_r_EulerZYX_W_actual: " << feet_r_EulerZYX_W_actual.transpose() << std::endl;

    feet_l_EulerZ_W_actual = feet_l_EulerZYX_W_actual[0];
    feet_r_EulerZ_W_actual = feet_r_EulerZYX_W_actual[0];

    const auto& v_l = pinocchio::getVelocity(model_est, data_est, leg_l6_joint, pinocchio::LOCAL_WORLD_ALIGNED);
    feet_l_LinVel_W_actual = v_l.linear();
    feet_l_AngVel_W_actual = v_l.angular();

    const auto& v_r = pinocchio::getVelocity(model_est, data_est, leg_r6_joint, pinocchio::LOCAL_WORLD_ALIGNED);
    feet_r_LinVel_W_actual = v_r.linear();
    feet_r_AngVel_W_actual = v_r.angular();

    // feet_l_LinVel_W_actual = pinocchio::getVelocity(model_footpos, data_footpos, leg_l6_joint, pinocchio::LOCAL_WORLD_ALIGNED).linear();
    // feet_r_LinVel_W_actual = pinocchio::getVelocity(model_footpos, data_footpos, leg_r6_joint, pinocchio::LOCAL_WORLD_ALIGNED).linear();

    rightfoot_jacobi.setZero(6, generalizedCoordinatesNum);
    pinocchio::getJointJacobian(model_est, data_est, model_est.getJointId("right_ankle_roll"), pinocchio::LOCAL_WORLD_ALIGNED, rightfoot_jacobi); 

    // 计算质心位置
    pinocchio::centerOfMass(model_est, data_est, generalized_q_actual, generalized_q_dot_actual);

    // 打印质心位置
    // std::cout << "Center of Mass position: " << data_footpos.com[0].transpose() << std::endl;

    // 计算机器人前进方向的速度
    Eigen::Vector2d base_lin_vel_world_xy = generalized_q_dot_actual.head<2>();    // 水平面上的速度（X 和 Y 方向）
    Eigen::Matrix2d base_rot_world_to_body_xy = rotation_matrix_plane(imu_zyx[0]);  // 旋转矩阵
    Eigen::Vector2d base_lin_vel_body_xy = base_rot_world_to_body_xy.transpose() * base_lin_vel_world_xy;  // 本体系下的水平面速度
    forward_speed = base_lin_vel_body_xy.x();  // 本体系 x 方向的速度（前进方向）

}



void StateEstimator::SetDataToPackage(DataPackage &data)
{
    data.generalized_q_actual = generalized_q_actual;
    // data.generalized_q_actual.head<3>() = data.base_pos;
    // data.generalized_q_actual.segment(3,3) = imu_zyx;
    // data.generalized_q_actual.tail(data.actuatedDofNum) = data.motor_pos;

    data.generalized_q_dot_actual = generalized_q_dot_actual;

    // for(int i = 3; i<6; ++i){
    //     std::cout << "generalized_q_actual[" << i << "] = " << generalized_q_actual[i] << std::endl;
    // }

    // for(int i = 0; i<3; ++i){
    //     std::cout << "generalized_q_dot_actual[" << i << "] = " << generalized_q_dot_actual[i] << std::endl;
    // }

    // data.generalized_q_dot_actual.head<3>() = data.base_lin_vel;
    // data.generalized_q_dot_actual.segment(3,3) =  getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(imu_zyx, global_angular_vel);
    // data.generalized_q_dot_actual.tail(data.actuatedDofNum) = data.motor_vel;

    // data.contact_flag = contact_flag;
    data.contact_force = contact_force_normal.segment(0,12);
    data.contact_force_history.push_back(data.contact_force);

    data.speed_forward_history.push_back(forward_speed);
    // data.speed_forward_history.push_back(generalized_q_actual[0]);

    data.feet_l_Pos_W_actual = feet_l_Pos_W_actual;
    data.feet_r_Pos_W_actual = feet_r_Pos_W_actual;
    data.feet_l_Rot_W_actual = feet_l_Rot_W_actual;
    data.feet_r_Rot_W_actual = feet_r_Rot_W_actual;

    data.hip_l_Pos_W_actual = hip_l_Pos_W_actual;
    data.hip_r_Pos_W_actual = hip_r_Pos_W_actual;


    data.feet_l_LinVel_W_actual = feet_l_LinVel_W_actual;
    data.feet_r_LinVel_W_actual = feet_r_LinVel_W_actual;

    data.feet_l_EulerZ_W_actual = feet_l_EulerZ_W_actual;
    data.feet_r_EulerZ_W_actual = feet_r_EulerZ_W_actual;

    data.global_angular_vel = global_angular_vel;

    data.feet_l_AngVel_W_actual = feet_l_AngVel_W_actual;
    data.feet_r_AngVel_W_actual = feet_r_AngVel_W_actual;

    data.feet_l_EulerZYX_W_actual = feet_l_EulerZYX_W_actual;
    data.feet_r_EulerZYX_W_actual = feet_r_EulerZYX_W_actual;

}



void StateEstimator::estContactForce()
{


    auto& data = data_est;

    const double lamda = cutoff_frequency;
    const double gama = exp(-lamda * control_period);
    const double beta = (1 - gama) / (gama * control_period);



    // Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> s(actuatedDofNum, generalizedCoordinatesNum);
    s.block(0, 0, actuatedDofNum, 6).setZero();
    s.block(0, 6, actuatedDofNum, actuatedDofNum).setIdentity();

    pinocchio::forwardKinematics(model_est, data, generalized_q_actual, generalized_q_dot_actual);
    pinocchio::computeJointJacobians(model_est, data);
    pinocchio::updateFramePlacements(model_est, data);

    pinocchio::crba(model_est, data, generalized_q_actual);

    data.M.triangularView<Eigen::StrictlyLower>() = data.M.transpose().triangularView<Eigen::StrictlyLower>();

    pinocchio::getCoriolisMatrix(model_est, data);

    pinocchio::computeGeneralizedGravity(model_est, data, generalized_q_actual);

    p = data.M * generalized_q_dot_actual;

    pSCg = beta * p + s.transpose() * torque_actual + data.C.transpose() * generalized_q_dot_actual - data.g;

    pSCg_z_inv = (1 - gama) * pSCg + gama * pSCgZinv_last;
    pSCgZinv_last = pSCg_z_inv;

    est_ext_torque = beta * p - pSCg_z_inv;

    // std::cout << "code come to est_ext_torque " << std::endl;




    for (int i = 0; i < 2; ++i)
    {

        // std::cout << "code come to getFrameJacobian " << std::endl;
        pinocchio::getFrameJacobian(model_est, data, endEffectorFrameIndices[i], pinocchio::LOCAL_WORLD_ALIGNED, jac);
        // std::cout << "code come to Jac_i " << std::endl;
        Jac_i = jac.template topRows<6>();
        S_li.setZero();
        int index = 0;
        if (i == 0)
        index = 0;
        else if (i == 1)
        index = SingleLegNum;
        // std::cout << "code come to S_li " << std::endl;
        S_li.block(0, 6 + index, SingleLegNum, SingleLegNum).setIdentity();
        S_JT = S_li * Jac_i.transpose();

        S_tau = S_li * est_ext_torque;

        contact_force_normal.segment<6>(6 * i) = S_JT.bdcSvd(Eigen::ComputeThinU | Eigen::ComputeThinV).solve(S_tau);

    }

    // std::cout << "code come to contact_force_normal " << std::endl;

    for (int i = 0; i < 2; i++)
    {
        contact_force_normal(6 * 2 + i) = contact_force_normal.segment<3>(6 * i).norm();
    }
    for (int i = 0; i < 2; i++)
    {
        contact_force_normal(6 * 2 + 2 + i) = contact_force_normal.segment<6>(6 * i).norm();
    }


}



Eigen::Matrix<double, 3, 1> StateEstimator::quatToZyx(const Eigen::Quaternion<double>& q)
{

    double as = std::min(-2. * (q.x() * q.z() - q.w() * q.y()), 0.99999);

    as = std::max(std::min(as, 1.0), -0.99999);  // 【双向夹紧在[-1, 1]】

    zyx(0) =
        std::atan2(2 * (q.x() * q.y() + q.w() * q.z()), q.w() * q.w() + q.x() * q.x() - q.y() * q.y() - q.z() * q.z());
    zyx(1) = std::asin(as);
    zyx(2) =
        std::atan2(2 * (q.y() * q.z() + q.w() * q.x()), q.w() * q.w() - q.x() * q.x() - q.y() * q.y() + q.z() * q.z());
    return zyx;
}


Eigen::Matrix<double, 3, 1> StateEstimator::getEulerAnglesZyxDerivativesFromLocalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& angularVelocity) 
{
    const double sy = sin(eulerAngles(1));
    const double cy = cos(eulerAngles(1));
    const double sx = sin(eulerAngles(2));
    const double cx = cos(eulerAngles(2));
    const double wx = angularVelocity(0);
    const double wy = angularVelocity(1);
    const double wz = angularVelocity(2);
    const double tmp = sx * wy / cy + cx * wz / cy;
    return {tmp, cx * wy - sx * wz, wx + sy * tmp};
}


Eigen::Matrix<double, 3, 1> StateEstimator::getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles) 
{
    const double sz = sin(eulerAngles(0));
    const double cz = cos(eulerAngles(0));
    const double sy = sin(eulerAngles(1));
    const double cy = cos(eulerAngles(1));
    const double dz = derivativesEulerAngles(0);
    const double dy = derivativesEulerAngles(1);
    const double dx = derivativesEulerAngles(2);
    return {-sz * dy + cy * cz * dx, cz * dy + cy * sz * dx, dz - sy * dx};
}


Eigen::Matrix<double, 3, 1> StateEstimator::getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& angularVelocity) 
{
    const double sz = sin(eulerAngles(0));
    const double cz = cos(eulerAngles(0));
    const double sy = sin(eulerAngles(1));
    const double cy = cos(eulerAngles(1));
    const double wx = angularVelocity(0);
    const double wy = angularVelocity(1);
    const double wz = angularVelocity(2);
    const double tmp = cz * wx / cy + sz * wy / cy;
    return {sy * tmp + wz, -sz * wx + cz * wy, tmp};
}

Eigen::Matrix2d StateEstimator::rotation_matrix_plane(double angle) {
    Eigen::Matrix2d R;
    double c = std::cos(angle);
    double s = std::sin(angle);

    R << c, -s,
        s, c;

    return R;
}

Eigen::Vector3d StateEstimator::getEulerZYXFromRotationMatrix(const Eigen::Matrix3d& R) {
    double yaw   = std::atan2(R(1, 0), R(0, 0));
    double pitch = std::atan2(-R(2, 0), std::sqrt(R(2, 1) * R(2, 1) + R(2, 2) * R(2, 2)));
    double roll  = std::atan2(R(2, 1), R(2, 2));
    return Eigen::Vector3d(yaw, pitch, roll); // ZYX 顺序
}

