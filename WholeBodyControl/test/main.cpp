#include <iostream>
#include <qpOASES.hpp>

using namespace qpOASES;

int main() {
    // 决策变量的数量 (80)
    const int nV = 80;  // number of variables
    // 约束条件的数量 (62)
    const int nC = 62;  // number of constraints

    // 创建一个 qpOASES 的求解器对象
    QProblem example(nV, nC);

    // 设置求解器为适用于 MPC 的模式
    qpOASES::Options options;
    options.setToMPC();
    // options.setToFast();

    options.printLevel = qpOASES::PL_LOW;
    options.enableEqualities = qpOASES::BT_TRUE;
    example.setOptions(options);

    // 定义目标函数的二次项 H 和线性项 g
    real_t H[nV * nV] = {0}; // 初始化为 0（简化处理）
    real_t g[nV] = {0};       // 初始化为 0（简化处理）

    // 定义约束矩阵 A 和边界条件
    real_t A[nC * nV] = {0};  // A 是一个 nC x nV 的矩阵 (62x80)
    real_t lb[nC] = {0};      // 约束的下边界 (62)
    real_t ub[nC] = {0};      // 约束的上边界 (62)

    // 设置一些例子约束条件
    for (int i = 0; i < nC; ++i) {
        lb[i] = -1.0;  // 例如：约束下边界为 -1
        ub[i] = 1.0;   // 例如：约束上边界为 1
    }

    // 设置一些目标函数的值
    for (int i = 0; i < nV; ++i) {
        g[i] = 1.0;    // 例如：线性项 g 为 1
    }

    // 调用求解器进行求解
    int nWSR = 100;  // 设置最大工作集迭代次数
    int status = example.init(H, g, A, lb, ub, NULL, NULL, nWSR);

    // 输出求解状态
    if (status == SUCCESSFUL_RETURN) {
        std::cout << "Optimization successful!" << std::endl;
    } else {
        std::cout << "Optimization failed!" << std::endl;
    }

    // 获取求解结果
    real_t x_opt[nV];  // 存储优化后的决策变量值
    example.getPrimalSolution(x_opt);

    // 输出部分优化后的决策变量
    std::cout << "Optimized decision variables (first 10):" << std::endl;
    for (int i = 0; i < 10; ++i) {
        std::cout << "x[" << i << "] = " << x_opt[i] << std::endl;
    }

    return 0;
}
