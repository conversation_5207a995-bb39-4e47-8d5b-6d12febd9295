cmake_minimum_required(VERSION 3.10)

set(CMAKE_CXX_STANDARD 11)
add_compile_options(-std=c++11)

project(wbc)
message(STATUS "Project source directory: ${PROJECT_SOURCE_DIR}/")
include_directories(${PROJECT_SOURCE_DIR})
include_directories(${PROJECT_SOURCE_DIR}/ThirdParty)
include_directories(${PROJECT_SOURCE_DIR}/ThirdParty/eigen3)


include_directories(${PROJECT_SOURCE_DIR}/ThirdParty/qpOASES/include)



link_directories(${PROJECT_SOURCE_DIR}/ThirdParty/qpOASES/lib)


# aux_source_directory(${PROJECT_SOURCE_DIR}/DataPackage/src dir_datapackage)
# add_library(DataPackage SHARED ${dir_datapackage})

# target_link_libraries(DataPackage pinocchio yaml-cpp)



include_directories(${PROJECT_SOURCE_DIR}/ThirdParty/mujoco)
include_directories(${PROJECT_SOURCE_DIR}/ThirdParty/glfw)
include_directories(${PROJECT_SOURCE_DIR}/mujoco_interface/include)



link_directories(${PROJECT_SOURCE_DIR}/ThirdParty/mujoco/lib)
link_directories(${PROJECT_SOURCE_DIR}/ThirdParty/glfw/lib)

add_executable(main ${PROJECT_SOURCE_DIR}/test/main.cpp)
target_link_libraries(main  PUBLIC   qpOASES 
)






# install(TARGETS DataPackage DESTINATION ${PROJECT_SOURCE_DIR}/lib)

