
#include "WholeBodyControl/include/WBCSolver.h"
#include <fstream>
#include <chrono>

namespace WBC_Solver
{


void WeightedWBCSolver::GetDataFromPackage(DataPackage &data)
{



    generalized_q_actual = data.generalized_q_actual;
    generalized_q_desired = data.generalized_q_desired;
    generalized_q_dot_actual = data.generalized_q_dot_actual ;
    generalized_q_dot_desired = data.generalized_q_dot_desired;
    // generalized_q_dot_actual.tail(24).setZero();
    // generalized_q_actual.head(6) <<0,0,0.8,0,0,0;
    // generalized_q_dot_actual.head(6) <<0,0,0,0,0,0;

    endEffectorFrameIndices = data.endEffectorFrameIndices;
    contact_force = data.contact_force;

    feet_r_Pos_W_desire = data.feet_r_Pos_W_desire;
    feet_l_Pos_W_desire = data.feet_l_Pos_W_desire;
    feet_r_LinVel_W_desire = data.feet_r_LinVel_W_desire;
    feet_l_LinVel_W_desire = data.feet_l_LinVel_W_desire;

    feet_r_Pos_W_actual = data.feet_r_Pos_W_actual;
    feet_l_Pos_W_actual = data.feet_l_Pos_W_actual;
    feet_r_LinVel_W_actual = data.feet_r_LinVel_W_actual;
    feet_r_LinVel_W_actual = data.feet_l_LinVel_W_actual;

    feet_r_AngVel_W_actual = data.feet_r_AngVel_W_actual;
    feet_l_AngVel_W_actual = data.feet_l_AngVel_W_actual;
    feet_r_Rot_W_actual = data.feet_r_Rot_W_actual;
    feet_l_Rot_W_actual = data.feet_l_Rot_W_actual;
    feet_l_OmegaZ_W_desire = data.feet_l_OmegaZ_W_desire;
    feet_r_OmegaZ_W_desire = data.feet_r_OmegaZ_W_desire;
    feet_l_EulerZ_W_desire = data.feet_l_EulerZ_W_desire;
    feet_r_EulerZ_W_desire = data.feet_r_EulerZ_W_desire;

    swingL = data.swingL;
    swingR = data.swingR;

    contact_flag = data.contact_flag;
    // generalized_q_dot_actual.segment(3,3) = getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(data.generalized_q_actual.segment(3,3), data.generalized_q_dot_actual.segment(3,3));
    isWalking = data.isWalking;

    IndexLegStart = data.IndexLegStart;
    IndexLegLength = data.IndexLegLength;
    IndexArmStart = data.IndexArmStart;
    IndexArmLength = data.IndexArmLength;
    IndexWaistStart = data.IndexWaistStart;
    IndexWaistLength = data.IndexWaistLength;

    If_first_Zero = data.If_first_Zero;
    dt_Zero = data.control_period;

    motor_pos = data.motor_pos;

    gait_t = data.t;

    // // Eigen::VectorXd generalized_q_actual(6);
    // generalized_q_actual.head(6) << -0.07305,0.00406,0.80623,0.00000,0.00717,-0.00910;

    // // Eigen::VectorXd generalized_q_dot_actual(6);
    // generalized_q_dot_actual.head(6) << -0.03360,-0.00102,-0.00190,0.00072,-0.04750,-0.00193;

    // generalized_q_desired.head(6) << -0.08000,0.00000,0.80000,0.00000,0.00000,0.00000;

    // // Eigen::VectorXd generalized_q_desired(24);
    // generalized_q_desired.tail(24) << 0.82222,-0.14970,-0.37368,1.36999,-0.61945,0.00129,-0.82289,0.14815,0.37213,1.36999,-0.61943,-0.00129,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000,0.00000;

    // generalized_q_dot_desired.head(6) << 0, 0, 0, 0, 0, 0;
    // // Eigen::VectorXd generalized_q_dot_desired(24);
    // generalized_q_dot_desired.tail(24) << 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0;

    // // Eigen::VectorXd motor_pos(24);
    // generalized_q_actual.tail(24) << -0.81860,-0.14957,-0.37239,1.39175,-0.60632,-0.00700,-0.82339,0.15494,0.36730,1.40153,-0.62253,-0.01275,0.00000,0.00058,0.00010,-0.00345,0.00144,0.00115,-0.00067,-0.00058,0.00010,-0.00067,0.00048,0.00048;

    // // Eigen::VectorXd motor_vel(24);
    // generalized_q_dot_actual.tail(24) << -0.00081,-0.00445,-0.00953,0.00110,0.05985,-0.00536,0.00071,0.00150,-0.00645,-0.00179,0.01935,-0.00315,0.00128,0.00283,0.00230,-0.00656,0.00124,-0.00553,-0.00043,0.00262,0.00018,0.00058,-0.00315,0.00790;



}


void WeightedWBCSolver::SetDataToPackage(DataPackage &data)
{

    torq_desire = qpSol.tail(actuatedDofNum);

    // for(int i = 0; i<torq_desire.size(); ++i){
    //     std::cout << "torq_desire[" << i << "] = " << torq_desire[i] << std::endl;
    // }

    // torq_desire.segment(0,12)<<0,0,0,0,0,0, 0,0,0,0,0,0;
    // std::cout<<"grf: "<<qpSol.segment(generalizedCoordinatesNum, 24)<<std::endl;
    Eigen::VectorXd PD_part = (torq_P * (generalized_q_desired - generalized_q_actual)
                            + torq_D * (generalized_q_dot_desired - generalized_q_dot_actual)).tail(actuatedDofNum);

    // std::cout<<"swingL: "<<swingL<<std::endl;
    // torq_desire.segment<6>(0) *= (1 - (swingL + 0.05)); 
    // torq_desire.segment<6>(6) *= (1 - (swingR + 0.05)); 
    if(gait_t>=0 && gait_t<zero_TransTime){
        torq_desire *= (gait_t/zero_TransTime);
    }
    // std::cout<<"gait_t: "<<gait_t/zero_TransTime<<std::endl;

    PD_part.segment<6>(0) *= swingL ; 
    PD_part.segment<6>(6) *= swingR ;
    PD_part.tail(10) *= 0.2;
    // std::cout<<"torq_desire: "<< torq_desire;

    // contact_flag = data.contact_flag;
    torq_cmd =  torq_desire + PD_part;


    data.torq_desire = torq_cmd ;


    //******************************for real robot**************************************** */

    data.motor_Torque_desire = torq_desire;
    data.motor_Pos_desire = generalized_q_desired.tail(actuatedDofNum);


    // data.motor_Pos_desire = generalized_q_actual.tail(actuatedDofNum);
    data.motor_Vel_desire = generalized_q_dot_desired.tail(actuatedDofNum);


}

void WeightedWBCSolver::SetZeroTorq(DataPackage &data)
{
    if(If_first_Zero){
        If_first_Zero = false;
        t_Zero = 0;
        init_pose = generalized_q_actual.tail(actuatedDofNum);
    }

    t_Zero += dt_Zero;

    // generalized_q_desired.tail(actuatedDofNum) = zero_pose;
    // generalized_q_dot_desired.setZero();

    Eigen::VectorXd Zero_vector = Eigen::VectorXd::Zero(actuatedDofNum);
    Eigen::VectorXd JointPos_desired(actuatedDofNum);
    Eigen::VectorXd JointVel_desired(actuatedDofNum);

    ThirdpolyVector(init_pose, Zero_vector, zero_pose, zero_pose, zero_totalTime, t_Zero, JointPos_desired, JointVel_desired);


    Eigen::VectorXd PD_part = torq_P * (JointPos_desired - generalized_q_actual.tail(actuatedDofNum))
                            + torq_D * (JointVel_desired - generalized_q_dot_actual.tail(actuatedDofNum));

    data.If_first_Zero = If_first_Zero;                        
    data.torq_desire = PD_part;

}

// void WeightedWBCSolver::SetMotorZero(DataPackage &data)
// {
//     if(If_first_Zero){
//         If_first_Zero = false;
//         t_Zero = 0;
//         init_pose = motor_pos;
//     }

//     t_Zero += dt_Zero;

//     // generalized_q_desired.tail(actuatedDofNum) = zero_pose;
//     // generalized_q_dot_desired.setZero();

//     Eigen::VectorXd Zero_vector = Eigen::VectorXd::Zero(actuatedDofNum);
//     Eigen::VectorXd JointPos_desired(actuatedDofNum);
//     Eigen::VectorXd JointVel_desired(actuatedDofNum);

//     ThirdpolyVector(init_pose, Zero_vector, zero_pose, Zero_vector, zero_totalTime, t_Zero, JointPos_desired, JointVel_desired);

//     data.If_first_Zero = If_first_Zero;                        
//     data.motor_Torque_desire = Zero_vector;
//     data.motor_Pos_desire = JointPos_desired;
//     data.motor_Vel_desire = JointVel_desired;


//     // *********************for mujoco******************************

//     Eigen::VectorXd PD_part = torq_P * (JointPos_desired - generalized_q_actual.tail(actuatedDofNum))
//                             + torq_D * (JointVel_desired - generalized_q_dot_actual.tail(actuatedDofNum));
//     data.torq_desire = PD_part;

// }

void WeightedWBCSolver::ThirdpolyVector(const Eigen::VectorXd& p0, const Eigen::VectorXd& p0_dot,
    const Eigen::VectorXd& p1, const Eigen::VectorXd& p1_dot,
    double totalTime, double currentTime,
    Eigen::VectorXd& pd, Eigen::VectorXd& pd_dot) {
    // 分量数量检查
    assert(p0.size() == p0_dot.size());
    assert(p1.size() == p1_dot.size());
    assert(p0.size() == p1.size());

    int dim = p0.size();
    pd.resize(dim);
    pd_dot.resize(dim);

    if (currentTime < 0.0) {
        pd = p0;
        pd_dot.setZero();
    } else if (currentTime <= totalTime) {
        for (int i = 0; i < dim; ++i) {
            double a0 = p0[i];
            double a1 = p0_dot[i];
            double m = p1[i] - p0[i] - p0_dot[i] * totalTime;
            double n = p1_dot[i] - p0_dot[i];
            double a2 = 3.0 * m / (totalTime * totalTime) - n / totalTime;
            double a3 = -2.0 * m / (totalTime * totalTime * totalTime) + n / (totalTime * totalTime);
            pd[i] = a3 * currentTime * currentTime * currentTime +
            a2 * currentTime * currentTime +
            a1 * currentTime + a0;
            pd_dot[i] = 3.0 * a3 * currentTime * currentTime +
                2.0 * a2 * currentTime + a1;
    }
    } else {
        pd = p1;
        pd_dot.setZero();
    }
}

void WeightedWBCSolver::Step()
{
    // auto record_start_time = std::chrono::steady_clock::now();

    numContacts = 0;
    for (bool flag : contact_flag)
    {
        if (flag)
        {
        numContacts++;
        }
    }
    UpdateMeasured();
    // UpdateDesired();

  // Constraints
    Task constraints = formulateConstraints();




    // Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor> A(numConstraints, numDecisionVars);

    // Eigen::Matrix<double, Eigen::Dynamic, 1> lbA(numConstraints), ubA(numConstraints);  // clang-format off

    A << constraints.a_, constraints.d_;

    lbA << constraints.b_, -qpOASES::INFTY * Eigen::Matrix<double, Eigen::Dynamic, 1>::Ones(constraints.f_.size());
    ubA << constraints.b_, constraints.f_;  // clang-format on

    // Cost
    Task weighedTask = formulateCost();

    H = weighedTask.a_.transpose() * weighedTask.a_;

    g = -weighedTask.a_.transpose() * weighedTask.b_;


    // Solve
    // auto qpProblem = qpOASES::QProblem(numDecisionVars, numConstraints);
    // options.setToMPC();
    // // options.setToFast();

    // options.printLevel = qpOASES::PL_LOW;
    // options.enableEqualities = qpOASES::BT_TRUE;
    // qpProblem.setOptions(options);


    numConstraints = constraints.b_.size() + constraints.f_.size();
    // std::cout<<"H: "<<H.rows()<<" " <<H.cols()<<std::endl;
    // std::cout<<"g: "<<g.rows()<<" " <<g.cols()<<std::endl;
    // std::cout<<"A: "<<A.rows()<<" " <<A.cols()<<std::endl;
    // std::cout<<"lbA: "<<lbA.rows()<<" " <<lbA.cols()<<std::endl;
    // std::cout<<"ubA: "<<ubA.rows()<<" " <<ubA.cols()<<std::endl;
    // std::cout<<"last_qpSol: "<<last_qpSol<<std::endl;



    // qpProblem.init(H.data(), g.data(), A.data(), nullptr, nullptr, lbA.data(), ubA.data(), nWsr);


    // qpProblem.getPrimalSolution(qpSol.data());

    if (first_time) {
        int nWsr = 1000;
        // qpOASES::real_t cpu_time =0.0005;

        qpOASES::returnValue result = qpProblem.init(H.data(), g.data(), A.data(), nullptr, nullptr, 
                    lbA.data(), ubA.data(), nWsr);


        qpProblem.getPrimalSolution(qpSol.data());

        if (result == qpOASES::SUCCESSFUL_RETURN) {
            // std::cerr << "[ERROR] QP initial solve failed with code: " << result << "\n";
            first_time = false;

            qpProblem.getPrimalSolution(qpSol.data());
            // return 1;

        }


    } 
    else {

        int nWsr = 1000;
        // qpOASES::real_t cpu_time =0.0005;

        qpProblem.hotstart(H.data(), g.data(), A.data(),   nullptr, nullptr, 
                        lbA.data(), ubA.data(), nWsr);

        qpProblem.getPrimalSolution(qpSol.data());

    }

    if (!qpProblem.isSolved())
    {
    std::cout << "ERROR: WeightWBC Not Solved!!!" << std::endl;
    if (last_qpSol.size() > 0)
        qpSol = last_qpSol;
    }

    last_qpSol = qpSol;
    // std::cout<<"qpSol : "<<qpSol.tail(actuatedDofNum)<<std::endl;
    // std::cout<<"qpSolnumDecisionVars : "<<numDecisionVars<<std::endl;


    // auto record_now_time = std::chrono::steady_clock::now();

    // std::chrono::duration<double> elapsed_record = record_now_time - record_start_time;

    // std::cout<< "qp time:"<<elapsed_record.count()<<std::endl;
}



Task WeightedWBCSolver::formulateConstraints()
{

    return  formulateTorqueLimitsTask() + formulateFrictionConeTask()+formulateFloatingBaseEomTask() ; //formulateNoContactMotionTask();
// formulateTorqueLimitsTask() +   + formulateFrictionConeTask()      formulateFloatingBaseEomTask
}


Task WeightedWBCSolver::formulateCost()
{

//  版本1：base位姿任务，腿关节任务（逆解计算所得，考虑支撑与摆动区别），腰、手臂关节任务，切向力最小，法向力最小，法向力参考任务
    return formulateBasePosTask()*weightBasePos + formulateBaseOriTask()*weightBaseOri 
    + formulateLeftLegJointPositionTask() *(swingL*weightLegJointPosSwing + (1-swingL)*weightLegJointPosSup) 
    + formulateRightLegJointPositionTask() *(swingR*weightLegJointPosSwing + (1-swingR)*weightLegJointPosSup) 
    + formulateArmJointPositionTask() *weightArmJointPos + formulateWaistJointPositionTask() *weightWaistJointPos;
    // + formulateContactTangentialForceMinTask()*weightTangentialContactRorceMin+ formulateContactNormalForceMinTask()*weightNormalContactRorceMin + formulateContactForceRefTask()*weightContactRorceRef;

//  版本2：base位姿任务，脚位姿任务（考虑支撑与摆动区别），腰、手臂关节任务，切向力最小，法向力最小，法向力参考任务
    // return formulateBasePosTask()*weightBasePos + formulateBaseOriTask()*weightBaseOri 
    // + formulateRightFootPosTask()*(weightFootPosSwing*swingR + weightFootPosSup*(1-swingR))
    // + formulateLeftFootPosTask()*(weightFootPosSwing*swingL + weightFootPosSup*(1-swingL))
    // + formulateRightFootOriTask() *(weightFootOriSwing*swingR + weightFootOriSup*(1-swingR))
    // + formulateLeftFootOriTask() *(weightFootOriSwing*swingL + weightFootOriSup*(1-swingL))
    // + formulateArmJointPositionTask() *weightArmJointPos + formulateWaistJointPositionTask() *weightWaistJointPos
    // + formulateContactTangentialForceMinTask()*weightTangentialContactRorceMin+ formulateContactNormalForceMinTask()*weightNormalContactRorceMin + formulateContactForceRefTask()*weightContactRorceRef;
    
//  版本3：base位姿任务，腿关节任务（逆解计算所得，考虑支撑与摆动区别），脚位姿任务（考虑支撑与摆动区别），腰、手臂关节任务，切向力最小，法向力最小，法向力参考任务
    // return formulateBasePosTask()*weightBasePos + formulateBaseOriTask()*weightBaseOri 
    // + formulateLeftLegJointPositionTask() *(swingL*weightLegJointPosSwing + (1-swingL)*weightLegJointPosSup) 
    // + formulateRightLegJointPositionTask() *(swingR*weightLegJointPosSwing + (1-swingR)*weightLegJointPosSup) 
    // + formulateRightFootPosTask()*(weightFootPosSwing*swingR + weightFootPosSup*(1-swingR))
    // + formulateLeftFootPosTask()*(weightFootPosSwing*swingL + weightFootPosSup*(1-swingL))
    // + formulateRightFootOriTask() *(weightFootOriSwing*swingR + weightFootOriSup*(1-swingR))
    // + formulateLeftFootOriTask() *(weightFootOriSwing*swingL + weightFootOriSup*(1-swingL))
    // + formulateArmJointPositionTask() *weightArmJointPos + formulateWaistJointPositionTask() *weightWaistJointPos
    // + formulateContactTangentialForceMinTask()*weightTangentialContactRorceMin+ formulateContactNormalForceMinTask()*weightNormalContactRorceMin + formulateContactForceRefTask()*weightContactRorceRef;

}

}