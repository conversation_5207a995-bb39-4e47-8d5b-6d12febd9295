#include "WholeBodyControl/include/TaskManager.h"

namespace WBC_Solver
{

TaskManager::TaskManager(DataPackage &data)
{
    
    // std::cout << "TaskManager1" << std::endl;
    std::string file_path = "../WholeBodyControl/config/wholebodycontrol.yaml";
    YAML::Node config = YAML::LoadFile(file_path);
    BaseOriTask_P = config["BaseOriTask_P"].as<double>();
    BaseOriTask_D = config["BaseOriTask_D"].as<double>();

    BasePosXTask_P = config["BasePosXTask_P"].as<double>();
    BasePosXTask_D = config["BasePosXTask_D"].as<double>();
    BasePosYTask_P = config["BasePosYTask_P"].as<double>();
    BasePosYTask_D = config["BasePosYTask_D"].as<double>();
    BasePosZTask_P = config["BasePosZTask_P"].as<double>();
    BasePosZTask_D = config["BasePosZTask_D"].as<double>();

    FootPosTask_P = config["FootPosTask_P"].as<double>();
    FootPosTask_D = config["FootPosTask_D"].as<double>();
    // JointPosTask_P = config["JointPosTask_P"].as<double>();
    // JointPosTask_D = config["JointPosTask_D"].as<double>();

    LegJointPosTask_P = config["LegJointPosTask_P"].as<double>();
    LegJointPosTask_D = config["LegJointPosTask_D"].as<double>();
    ArmJointPosTask_P = config["ArmJointPosTask_P"].as<double>();
    ArmJointPosTask_D = config["ArmJointPosTask_D"].as<double>();
    WaistJointPosTask_P = config["WaistJointPosTask_P"].as<double>();
    WaistJointPosTask_D = config["WaistJointPosTask_D"].as<double>();

    FootOriTask_P = config["FootOriTask_P"].as<double>();
    FootOriTask_D = config["FootOriTask_D"].as<double>();

    torq_P = config["torq_P"].as<double>();
    torq_D = config["torq_D"].as<double>(); 
    frictionCoeff = config["frictionCoeff"].as<double>(); 

    zero_TransTime = config["zero_TransTime"].as<double>(); 

    // std::cout << "TaskManager2" << std::endl;


    model_wbc = data.robot_model;
    data_measured = data.robot_data;
    data_desired = data.robot_data;
    data_IK = data.robot_data;
    numThreeDofContacts = data.foot_contact_size;
    generalizedCoordinatesNum = data.generalizedCoordinatesNum;
    actuatedDofNum =data.actuatedDofNum;
    numDecisionVars = generalizedCoordinatesNum + actuatedDofNum + 3 * data.foot_contact_size;
    generalized_q_actual.setZero(generalizedCoordinatesNum);
    generalized_q_dot_actual.setZero(generalizedCoordinatesNum);
    robotMass = pinocchio::computeTotalMass(model_wbc);
    torqueLimits = model_wbc.effortLimit;
    // generalized_q_desired<<0,0,0,0,0,0,-0.2,0,0,0.4,-0.2,0,-0.2,0,0,0.4,-0.2,0;
    generalized_q_desired.setZero(generalizedCoordinatesNum);
    // generalized_q_desired<<0,0,0.8,0,0,0,-0.2,0,0,0.4,-0.2,0,-0.2,0,0,0.4,-0.2,0;
    
    generalized_q_dot_desired.setZero(generalizedCoordinatesNum);
}


void TaskManager::UpdateDesired()
{
    // auto model_desired = model_wbc;

    // const auto qDesired = mapping_.getPinocchioJointPosition(stateDesired);
    
    pinocchio::forwardKinematics(model_wbc, data_desired, generalized_q_desired);
    pinocchio::computeJointJacobians(model_wbc, data_desired, generalized_q_desired);
    pinocchio::updateFramePlacements(model_wbc, data_desired);
    // updateCentroidalDynamics(pinocchioInterfaceDesired_, info_, qDesired);

    pinocchio::computeCentroidalMap(model_wbc, data_desired, generalized_q_desired);
    pinocchio::updateFramePlacements(model_wbc, data_desired);

    //generalized_q_dot_desired = getPinocchioJointVelocity(model_wbc, data_desired);
    pinocchio::forwardKinematics(model_wbc, data_desired, generalized_q_desired, generalized_q_dot_desired);

}



void TaskManager::UpdateMeasured()
{


    pinocchio::forwardKinematics(model_wbc, data_measured, generalized_q_actual, generalized_q_dot_actual);
    pinocchio::computeJointJacobians(model_wbc, data_measured);
    pinocchio::updateFramePlacements(model_wbc, data_measured);
    pinocchio::crba(model_wbc, data_measured, generalized_q_actual);
    data_measured.M.triangularView<Eigen::StrictlyLower>() = data_measured.M.transpose().triangularView<Eigen::StrictlyLower>();
    pinocchio::nonLinearEffects(model_wbc, data_measured, generalized_q_actual, generalized_q_dot_actual);
    jacobi = Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(3 * numThreeDofContacts, generalizedCoordinatesNum);
    for (int i = 0; i < numThreeDofContacts; ++i)
    {
        Eigen::Matrix<double, 6, Eigen::Dynamic> jacobi_contact;
        jacobi_contact.setZero(6, generalizedCoordinatesNum);
        pinocchio::getFrameJacobian(model_wbc, data_measured, endEffectorFrameIndices[i], pinocchio::LOCAL_WORLD_ALIGNED, jacobi_contact);

        jacobi.block(3 * i, 0, 3, generalizedCoordinatesNum) = jacobi_contact.template topRows<3>();
    }
    pinocchio::computeJointJacobiansTimeVariation(model_wbc, data_measured, generalized_q_actual, generalized_q_dot_actual);
    // jacobi_dot = Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(3 * numThreeDofContacts, generalizedCoordinatesNum);
    // for (int  i = 0; i < numThreeDofContacts; ++i)
    // {
    //     Eigen::Matrix<double, 6, Eigen::Dynamic> jacobi_dot_contact;
    //     jacobi_dot_contact.setZero(6, generalizedCoordinatesNum);
    //     pinocchio::getFrameJacobianTimeVariation(model_wbc, data_measured, endEffectorFrameIndices[i], pinocchio::LOCAL_WORLD_ALIGNED, jacobi_dot_contact);
    //     jacobi_dot.block(3 * i, 0, 3, generalizedCoordinatesNum) = jacobi_dot_contact.template topRows<3>();
    // }
  // For base motion tracking task
    base_jacobi.setZero(6, generalizedCoordinatesNum);
    base_jacobi_dot.setZero(6, generalizedCoordinatesNum);
    pinocchio::getFrameJacobian(model_wbc, data_measured, model_wbc.getBodyId("base_link"), pinocchio::LOCAL_WORLD_ALIGNED, base_jacobi);
    pinocchio::getFrameJacobianTimeVariation(model_wbc, data_measured, model_wbc.getBodyId("base_link"), pinocchio::LOCAL_WORLD_ALIGNED,
                                            base_jacobi_dot);


  // For foot motion tracking task
    rightfoot_jacobi.setZero(6, generalizedCoordinatesNum);
    rightfoot_jacobi_dot.setZero(6, generalizedCoordinatesNum);
    pinocchio::getJointJacobian(model_wbc, data_measured, model_wbc.getJointId("right_ankle_roll"), pinocchio::LOCAL_WORLD_ALIGNED, rightfoot_jacobi);
    pinocchio::getJointJacobianTimeVariation(model_wbc, data_measured, model_wbc.getJointId("right_ankle_roll"), pinocchio::LOCAL_WORLD_ALIGNED,
                                            rightfoot_jacobi_dot);      
    leftfoot_jacobi.setZero(6, generalizedCoordinatesNum);
    leftfoot_jacobi_dot.setZero(6, generalizedCoordinatesNum);
    pinocchio::getJointJacobian(model_wbc, data_measured, model_wbc.getJointId("left_ankle_roll"), pinocchio::LOCAL_WORLD_ALIGNED, leftfoot_jacobi);
    pinocchio::getJointJacobianTimeVariation(model_wbc, data_measured, model_wbc.getJointId("left_ankle_roll"), pinocchio::LOCAL_WORLD_ALIGNED,
                                            leftfoot_jacobi_dot);          

}



Task TaskManager::formulateFrictionConeTask()
{
    // 固定为最大数量：假设全部非接触
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(3 * numThreeDofContacts, numDecisionVars);
    a.setZero();

    Eigen::Matrix<double, Eigen::Dynamic, 1> b(3 * numThreeDofContacts);
    b.setZero();

    size_t eq_j = 0;
    for (size_t i = 0; i < numThreeDofContacts; ++i)
    {
        if (!contact_flag[i])
        {
            a.block(3 * eq_j, generalizedCoordinatesNum + 3 * i, 3, 3) = Eigen::Matrix3d::Identity();
            b.segment(3 * eq_j, 3).setZero();
        }
        // 如果是接触点，则这三行保留为零矩阵（不激活）
        eq_j++;
    }

    // 固定为最大数量：假设全部接触
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> frictionPyramid(5, 3);  // 注意修正拼写
    frictionPyramid <<  0,  0, -1,
                        1,  0, -frictionCoeff,
                       -1,  0, -frictionCoeff,
                        0,  1, -frictionCoeff,
                        0, -1, -frictionCoeff;

    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> d(5 * numThreeDofContacts, numDecisionVars);
    d.setZero();

    Eigen::Matrix<double, Eigen::Dynamic, 1> f = Eigen::Matrix<double, Eigen::Dynamic, 1>::Constant(5 * numThreeDofContacts, 1e10);  // 默认设为无穷大

    size_t ineq_j = 0;
    for (size_t i = 0; i < numThreeDofContacts; ++i)
    {
        if (contact_flag[i])
        {
            d.block(5 * ineq_j, generalizedCoordinatesNum + 3 * i, 5, 3) = frictionPyramid;
            f.segment(5 * ineq_j, 5).setZero();  // d * x ≤ 0
        }
        // 如果是非接触点，对应的不等式保持为零（d为0，f为大数），不激活
        ineq_j++;
    }

    return { a, b, d, f };
}


Task TaskManager::formulateTorqueLimitsTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> d(2 * actuatedDofNum, numDecisionVars);
    d.setZero();

    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> i = Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>::Identity(actuatedDofNum, actuatedDofNum);

    d.block(0, generalizedCoordinatesNum + 3 * numThreeDofContacts, actuatedDofNum,
            actuatedDofNum) = i;
    d.block(actuatedDofNum, generalizedCoordinatesNum + 3 * numThreeDofContacts, actuatedDofNum,
            actuatedDofNum) = -i;

    Eigen::Matrix<double, Eigen::Dynamic, 1> f(2 * actuatedDofNum);
    f.segment(0, actuatedDofNum) = torqueLimits.segment(6, actuatedDofNum);

    f.segment(actuatedDofNum, actuatedDofNum) = torqueLimits.segment(6, actuatedDofNum);
    // for (int i = 0; i < contact_flag.size(); ++i) {
    //     std::cout<<"cccccc: " << contact_flag[i] << " ";
    // }


    return { Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>(), d, f };
}



Task TaskManager::formulateFloatingBaseEomTask()
{

    //   auto& data = pinocchioInterfaceMeasured_.getData();
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> s(actuatedDofNum, generalizedCoordinatesNum);
    s.block(0, 0, actuatedDofNum, 6).setZero();

    s.block(0, 6, actuatedDofNum, actuatedDofNum).setIdentity();
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a = (Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(generalizedCoordinatesNum, numDecisionVars) << data_measured.M, -jacobi.transpose(), -s.transpose()).finished();
    Eigen::Matrix<double, Eigen::Dynamic, 1> b = -data_measured.nle;        
    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>() };
}


Task TaskManager::formulateBasePosTask()
{
  Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(3, numDecisionVars);
  Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, 0, 3, 3).setIdentity();
    // std::cout<<"generalized_q_desired[1]: "<<generalized_q_desired[1]<<std::endl;
    //  b[0] = 100 * (0 - generalized_q_actual[0]) + 10 * (0 - generalized_q_dot_actual[0]);

    // b[1] = 100 * (0 - generalized_q_actual[1]) + 10 * (0 - generalized_q_dot_actual[1]);
    b[0] = BasePosXTask_P * (generalized_q_desired[0] - generalized_q_actual[0]) + BasePosXTask_D * (generalized_q_dot_desired[0] - generalized_q_dot_actual[0]);    
    b[1] = BasePosYTask_P * (generalized_q_desired[1] - generalized_q_actual[1]) + BasePosYTask_D * (generalized_q_dot_desired[1]  - generalized_q_dot_actual[1]);
    b[2] =  BasePosZTask_P * (generalized_q_desired[2] - generalized_q_actual[2]) + BasePosZTask_D * (generalized_q_dot_desired[2]  - generalized_q_dot_actual[2]);

    if(isWalking){
        a.topRows(1).setZero();
        b.head(1).setZero();
    }

    // std::cout<<"generalized_q_dot_desired: "<<generalized_q_dot_desired.segment(0,3)<<std::endl;

    // pinocchio::forwardKinematics(model_wbc, data_desired, generalized_q_desired, generalized_q_dot_desired);
    // pinocchio::updateFramePlacements(model_wbc, data_desired);
    // pinocchio::JointIndex leg_l6_joint=model_wbc.getJointId("leg_l6_joint");
    // pinocchio::JointIndex leg_r6_joint=model_wbc.getJointId("leg_r6_joint");
    // std::cout<<"data_desired.oMi[leg_l6_joint].translation()"<<data_desired.oMi[leg_l6_joint].translation()<<std::endl;
    // std::cout<<"data_desired.oMi[leg_r6_joint].translation()"<<data_desired.oMi[leg_r6_joint].translation()<<std::endl;

  return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateBaseOriTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>  a(3, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1>  b(a.rows());

    a.setZero();
    b.setZero();

    a.block(0, 0, 3, generalizedCoordinatesNum) = base_jacobi.block(3, 0, 3, generalizedCoordinatesNum);

    Eigen::Matrix<double, 3, 1>  eulerAngles = generalized_q_actual.segment<3>(3);

    // from derivative euler to angular
    Eigen::Matrix<double, 3, 1>  vMeasuredGlobal = getGlobalAngularVelocityFromEulerAnglesZyxDerivatives(eulerAngles, generalized_q_dot_actual.segment<3>(3));
    Eigen::Matrix<double, 3, 1>  vDesiredGlobal = getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( generalized_q_desired.segment<3>(3), generalized_q_dot_desired.segment<3>(3));

    // from euler to rotation
    Eigen::Matrix<double, 3, 1>  eulerAnglesDesired =generalized_q_desired.segment<3>(3);
    Eigen::Matrix<double, 3, 3>  rotationBaseMeasuredToWorld = getRotationMatrixFromZyxEulerAngles(eulerAngles);
    Eigen::Matrix<double, 3, 3>  rotationBaseReferenceToWorld = getRotationMatrixFromZyxEulerAngles(eulerAnglesDesired);

    Eigen::Matrix<double, 3, 1>  error = rotationErrorInWorld(rotationBaseReferenceToWorld, rotationBaseMeasuredToWorld);

    // std::cout<<"eulerAnglesDesired: "<<eulerAnglesDesired<<std::endl;
    // std::cout<<"eulerAngles: "<<eulerAngles<<std::endl;
    // std::cout<<"vDesiredGlobal: "<<vDesiredGlobal<<std::endl;
    // std::cout<<"rotationBaseReferenceToWorld: "<<rotationBaseReferenceToWorld<<std::endl;
    // std::cout<<"vMeasuredGlobal: "<<vMeasuredGlobal<<std::endl;
    

    b = BaseOriTask_P * error + BaseOriTask_D * (vDesiredGlobal - vMeasuredGlobal)  - base_jacobi_dot.block(3, 0, 3, generalizedCoordinatesNum) * generalized_q_dot_actual;

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}

Task TaskManager::formulateRightFootPosTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(3, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();

    Eigen::Vector3d accel = FootPosTask_P * (feet_r_Pos_W_desire - feet_r_Pos_W_actual) + FootPosTask_D * (feet_r_LinVel_W_desire - feet_r_LinVel_W_actual);
    a.block(0, 0, 3,generalizedCoordinatesNum) = rightfoot_jacobi.block(0, 0, 3, generalizedCoordinatesNum);
    b.segment(0, 3) = accel - rightfoot_jacobi_dot.block(0, 0, 3, generalizedCoordinatesNum) * generalized_q_dot_actual;

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateLeftFootPosTask()
{
    
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(3, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    // std::cout<<"feet_l_Pos_W_actual: "<<feet_l_Pos_W_actual<<std::endl;
    // std::cout<<"feet_l_Pos_W_desire: "<<feet_l_Pos_W_desire<<std::endl;
    // // std::cout<<"feet_l_Pos_W_actual: "<<feet_l_Pos_W_actual<<std::endl;
    // feet_l_Pos_W_desire<<0,02.12,0.16;
    Eigen::Vector3d accel = FootPosTask_P * (feet_l_Pos_W_desire - feet_l_Pos_W_actual) + FootPosTask_D * (feet_l_LinVel_W_desire - feet_l_LinVel_W_actual);

    // std::cout<<"accel: "<<accel<<std::endl;

    // std::cout<<"feet_l_Pos_W_desire: "<<feet_l_Pos_W_desire<<std::endl;
    a.block(0, 0, 3, generalizedCoordinatesNum) = leftfoot_jacobi.block(0, 0, 3, generalizedCoordinatesNum);
    b.segment(0, 3) = accel - leftfoot_jacobi_dot.block(0, 0, 3, generalizedCoordinatesNum) * generalized_q_dot_actual;


    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateRightFootOriTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>  a(3, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1>  b(a.rows());

    a.setZero();
    b.setZero();

    a.block(0, 0, 3, generalizedCoordinatesNum) = rightfoot_jacobi.block(3, 0, 3, generalizedCoordinatesNum);

    Eigen::Matrix<double, 3, 1>  vDesiredGlobal = Eigen::Vector3d(0, 0, feet_r_OmegaZ_W_desire);

    Eigen::Matrix<double, 3, 3>  rotationBaseReferenceToWorld = rotation_matrix("z", feet_r_EulerZ_W_desire);
    Eigen::Matrix<double, 3, 1>  error = rotationErrorInWorld(rotationBaseReferenceToWorld, feet_r_Rot_W_actual);

    // std::cout<<"rotationBaseReferenceToWorld: "<<rotationBaseReferenceToWorld<<std::endl;
    // std::cout<<"feet_r_Rot_W_actual: "<<feet_r_Rot_W_actual<<std::endl;

    b = BaseOriTask_P * error + BaseOriTask_D * (vDesiredGlobal - feet_r_AngVel_W_actual)  - rightfoot_jacobi_dot.block(3, 0, 3, generalizedCoordinatesNum) * generalized_q_dot_actual;

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}

Task TaskManager::formulateLeftFootOriTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>  a(3, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1>  b(a.rows());

    a.setZero();
    b.setZero();

    a.block(0, 0, 3, generalizedCoordinatesNum) = leftfoot_jacobi.block(3, 0, 3, generalizedCoordinatesNum);

    Eigen::Matrix<double, 3, 1>  vDesiredGlobal = Eigen::Vector3d(0, 0, feet_l_OmegaZ_W_desire);

    Eigen::Matrix<double, 3, 3>  rotationBaseReferenceToWorld = rotation_matrix("z", feet_l_EulerZ_W_desire);
    Eigen::Matrix<double, 3, 1>  error = rotationErrorInWorld(rotationBaseReferenceToWorld, feet_l_Rot_W_actual);

    // std::cout<<"rotationBaseReferenceToWorld: "<<rotationBaseReferenceToWorld<<std::endl;
    // std::cout<<"feet_l_Rot_W_actual: "<<feet_l_Rot_W_actual<<std::endl;

    b = BaseOriTask_P * error + BaseOriTask_D * (vDesiredGlobal - feet_l_AngVel_W_actual)  - leftfoot_jacobi_dot.block(3, 0, 3, generalizedCoordinatesNum) * generalized_q_dot_actual;

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}

Task TaskManager::formulateJointPositionTask2()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(actuatedDofNum, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, 6, actuatedDofNum, actuatedDofNum).setIdentity();

    b = JointPosTask_P * (generalized_q_desired.tail(actuatedDofNum) - generalized_q_actual.tail(actuatedDofNum)) + JointPosTask_D* (generalized_q_dot_desired.tail(actuatedDofNum)-generalized_q_dot_actual.tail(actuatedDofNum));

    if (contact_flag[0] == true) {//左脚接触，左腿不位控
        // 将矩阵 a 的前 6 行置为零
        a.block(0, 0, 6, numDecisionVars).setZero();
        // 将向量 b 的前 6 行置为零
        b.head(6).setZero();
    }

    if (contact_flag[1] == true) {//右脚接触，右腿不位控
        // 将矩阵 a 的后 6 行置为零
        a.block(a.rows() - 6, 0, 6, numDecisionVars).setZero();
        // 将向量 b 的后 6 行置为零
        b.tail(6).setZero();
    }

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateJointPositionTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(actuatedDofNum, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, 6, actuatedDofNum, actuatedDofNum).setIdentity();

    // std::cout<<"generalized_q_desired: "<<generalized_q_desired.tail(actuatedDofNum)<<std::endl;
    // std::cout<<"generalized_q_actual: "<<generalized_q_actual.tail(actuatedDofNum)<<std::endl;
    b = JointPosTask_P * (generalized_q_desired.tail(actuatedDofNum) - generalized_q_actual.tail(actuatedDofNum)) + JointPosTask_D* (generalized_q_dot_desired.tail(actuatedDofNum)-generalized_q_dot_actual.tail(actuatedDofNum));

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateLegJointPositionTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(IndexLegLength, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, IndexLegStart, IndexLegLength, IndexLegLength).setIdentity();

    b = (LegJointPosTask_P * (generalized_q_desired - generalized_q_actual) + LegJointPosTask_D* (generalized_q_dot_desired-generalized_q_dot_actual)).segment(IndexLegStart, IndexLegLength);

    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_IK = generalized_q_actual;
    generalized_q_IK.tail(actuatedDofNum) = generalized_q_desired.tail(actuatedDofNum);
    pinocchio::forwardKinematics(model_wbc, data_IK, generalized_q_IK);
    pinocchio::updateFramePlacements(model_wbc, data_IK);
    std::cout<<"data_IK.oMi[left_ankle_roll].translation():"<<data_IK.oMi[model_wbc.getJointId("left_ankle_roll")].translation().transpose()<<std::endl;
    std::cout<<"data_IK.oMi[right_ankle_roll].translation():"<<data_IK.oMi[model_wbc.getJointId("right_ankle_roll")].translation().transpose()<<std::endl;


    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateLeftLegJointPositionTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(IndexLegLength/2, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, IndexLegStart, IndexLegLength/2, IndexLegLength/2).setIdentity();

    b = (LegJointPosTask_P * (generalized_q_desired - generalized_q_actual) + LegJointPosTask_D* (generalized_q_dot_desired-generalized_q_dot_actual)).segment(IndexLegStart, IndexLegLength/2);

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateRightLegJointPositionTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(IndexLegLength/2, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, IndexLegStart+IndexLegLength/2, IndexLegLength/2, IndexLegLength/2).setIdentity();

    b = (LegJointPosTask_P * (generalized_q_desired - generalized_q_actual) + LegJointPosTask_D* (generalized_q_dot_desired-generalized_q_dot_actual)).segment(IndexLegStart+IndexLegLength/2, IndexLegLength/2);

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateArmJointPositionTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(IndexArmLength, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, IndexArmStart, IndexArmLength, IndexArmLength).setIdentity();

    b = (ArmJointPosTask_P * (generalized_q_desired - generalized_q_actual) + ArmJointPosTask_D* (generalized_q_dot_desired-generalized_q_dot_actual)).segment(IndexArmStart, IndexArmLength);

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}

Task TaskManager::formulateWaistJointPositionTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(IndexWaistLength, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

    a.setZero();
    b.setZero();
    a.block(0, IndexWaistStart, IndexWaistLength, IndexWaistLength).setIdentity();

    b = (WaistJointPosTask_P * (generalized_q_desired - generalized_q_actual) + WaistJointPosTask_D* (generalized_q_dot_desired-generalized_q_dot_actual)).segment(IndexWaistStart, IndexWaistLength);

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>()};
}


// Task TaskManager::formulateBaseAngularMotionTask()
// {
//   Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(3, numDecisionVars);
//   Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());

//   a.setZero();
//   b.setZero();

//   a.block(0, 0, 3, generalizedCoordinatesNum) = base_jacobi.block(3, 0, 3, generalizedCoordinatesNum);

//   Eigen::Matrix<double, 3, 1> eulerAngles = generalized_q_actual.segment<3>(3);

//   // from derivative euler to angular
//   Eigen::Matrix<double, 3, 1> vMeasuredGlobal =
//       getGlobalAngularVelocityFromEulerAnglesZyxDerivatives(eulerAngles, generalized_q_actual.segment<3>(3));
//   Eigen::Matrix<double, 3, 1> vDesiredGlobal;

//   // from euler to rotation
//   Eigen::Matrix<double, 3, 1> eulerAnglesDesired = basePoseDes_.tail<3>();
//   Eigen::Matrix<double, 3, 3>  rotationBaseMeasuredToWorld = getRotationMatrixFromZyxEulerAngles<double>(eulerAngles);
//   Eigen::Matrix<double, 3, 3>  rotationBaseReferenceToWorld = getRotationMatrixFromZyxEulerAngles<double>(eulerAnglesDesired);

//   Eigen::Matrix<double, 3, 1> error = rotationErrorInWorld<double>(rotationBaseReferenceToWorld, rotationBaseMeasuredToWorld);

//   // desired acc
//   Eigen::Matrix<double, 3, 1> accDesired = baseAccelerationDes_.tail<3>();

//   b = accDesired + baseAngularKp_ * error + baseAngularKd_ * (vDesiredGlobal - vMeasuredGlobal) -
//       base_jacobi_dot.block(3, 0, 3, info_.generalizedCoordinatesNum) * vMeasured_;

//   return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>() };
// }



Task TaskManager::formulateStanceBaseAccelTask()
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(6, numDecisionVars);
    a.setZero();
    a.block(0, 0, 6, 6) = Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>::Identity(6, 6);

    Eigen::Matrix<double, 6, 1> b;
    b.setZero();

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>() };
}


Task TaskManager::formulateBaseAccelTask() 
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(6, numDecisionVars);
    a.setZero();
    a.block(0, 0, 6, 6) = Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>::Identity(6, 6);

    // Eigen::Matrix<double, Eigen::Dynamic, 1> jointAccel = centroidal_model::getJointVelocities(inputDesired - inputLast_, info_) / period;
    // inputLast_ = inputDesired;
    // mapping_.setPinocchioInterface(pinocchioInterfaceDesired_);

    // const auto& model = pinocchioInterfaceDesired_.getModel();
    // auto& data = pinocchioInterfaceDesired_.getData();
    generalized_q_dot_desired = getPinocchioJointVelocity(model_wbc, data_desired);
    pinocchio::forwardKinematics(model_wbc, data_desired, generalized_q_desired, generalized_q_dot_desired);

    const auto& Ag = data_desired.Ag;
    const Eigen::Matrix<double, 6, 6> Ab = Ag.template leftCols<6>();
    const auto AbInv = computeFloatingBaseCentroidalMomentumMatrixInverse(Ab);
    const auto Aj = Ag.rightCols(actuatedDofNum);
    const auto ADot = pinocchio::dccrba(model_wbc, data_desired, generalized_q_desired, generalized_q_dot_desired);
    Eigen::Matrix<double, 6, 1> centroidalMomentumRate = robotMass * getNormalizedCentroidalMomentumRate();
    centroidalMomentumRate.noalias() -= ADot * generalized_q_dot_desired;
    // centroidalMomentumRate.noalias() -= Aj * jointAccel;

    Eigen::Matrix<double, 6, 1>  b = AbInv * centroidalMomentumRate;

 return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>(), Eigen::Matrix<double, Eigen::Dynamic, 1>() };
}







// Eigen::Matrix<double, 3, 1> TaskManager::getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& angularVelocity) 
// {
//     const double sz = sin(eulerAngles(0));
//     const double cz = cos(eulerAngles(0));
//     const double sy = sin(eulerAngles(1));
//     const double cy = cos(eulerAngles(1));
//     const double wx = angularVelocity(0);
//     const double wy = angularVelocity(1);
//     const double wz = angularVelocity(2);
//     const double tmp = cz * wx / cy + sz * wy / cy;
//     return {sy * tmp + wz, -sz * wx + cz * wy, tmp};
// }



Eigen::Matrix<double, Eigen::Dynamic, 1> TaskManager::getPinocchioJointVelocity(const pinocchio::Model& model_com, const pinocchio::Data& data_com)
 
{

    const auto& Ag = data_com.Ag;
    const Eigen::Matrix<double, 6, 6> Ab = Ag.template leftCols<6>();
    const auto Ab_inv = computeFloatingBaseCentroidalMomentumMatrixInverse(Ab);

    const auto jointVelocities = generalized_q_dot_desired.tail(actuatedDofNum);

    Eigen::Matrix<double, 6, 1> momentum;
    momentum.setZero();

    momentum.noalias() -= Ag.rightCols(actuatedDofNum) * jointVelocities;


    Eigen::Matrix<double, Eigen::Dynamic, 1> vPinocchio(generalizedCoordinatesNum);
    vPinocchio.template head<6>().noalias() = Ab_inv * momentum;
    vPinocchio.tail(actuatedDofNum) = jointVelocities;

    return vPinocchio;
}

Task TaskManager::formulateContactTangentialForceMinTask() const
{
  Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(2 * numThreeDofContacts, numDecisionVars);
  Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());
  a.setZero();

  for (size_t i = 0; i < numThreeDofContacts; ++i)
  {
    a.block(2 * i, generalizedCoordinatesNum + 3 * i, 2, 2) = Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>::Identity(2, 2);
  }
  b.setZero();

  return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}
Task TaskManager::formulateContactNormalForceMinTask() const
{
  Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(numThreeDofContacts, numDecisionVars);
  Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());
  a.setZero();
  b.setZero();

  for (size_t i = 0; i < numThreeDofContacts; ++i)
  {
    a(i, generalizedCoordinatesNum + 2 + 3 * i) = 1;
    b[i] = 0;
  }
  

  return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}
Task TaskManager::formulateContactForceRefTask() const
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(numContacts, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());
    a.setZero();
    b.setZero();
    int j =0 ;



    for (size_t i = 0; i < numThreeDofContacts; ++i)
    {
        if (contact_flag[i])
            {
                a(j, generalizedCoordinatesNum + 2 + 3 * i) = 1;
                b[j] = robotMass * 9.81 / (double)numContacts;
                j++;
               
            }
    }
    

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}

Task TaskManager::formulateContactForceRefTask2() const
{
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a(1, numDecisionVars);
    Eigen::Matrix<double, Eigen::Dynamic, 1> b(a.rows());
    a.setZero();
    b.setZero();

    for (size_t i = 0; i < numThreeDofContacts; ++i)
    {
        if (contact_flag[i])
            {
                a(0, generalizedCoordinatesNum + 2 + 3 * i) = 1;
                b[0] = robotMass * 9.81;
               
            }
    }
    

    return { a, b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> (), Eigen::Matrix<double, Eigen::Dynamic, 1> () };
}

Eigen::Matrix<double, 6, 6> TaskManager::computeFloatingBaseCentroidalMomentumMatrixInverse(const Eigen::Matrix<double, 6, 6>& Ab) 
{
    const double mass = Ab(0, 0);
    Eigen::Matrix<double, 3, 3> Ab_22_inv = Ab.template block<3, 3>(3, 3).inverse();
    Eigen::Matrix<double, 6, 6> Ab_inv = Eigen::Matrix<double, 6, 6>::Zero();
    Ab_inv << 1.0 / mass * Eigen::Matrix<double, 3, 3>::Identity(), -1.0 / mass * Ab.template block<3, 3>(0, 3) * Ab_22_inv,
        Eigen::Matrix<double, 3, 3>::Zero(), Ab_22_inv;
    return Ab_inv;
}






Eigen::Matrix<double, 6, 1> TaskManager::getNormalizedCentroidalMomentumRate() 
{
    // const auto& data = interface.getData();

    const Eigen::Matrix<double, 3, 1> gravityVector(double(0.0), double(0.0), double(-9.81));
    Eigen::Matrix<double, 6, 1> centroidalMomentumRate;
    centroidalMomentumRate.setZero();
    centroidalMomentumRate << robotMass * gravityVector, Eigen::Matrix<double, 3, 1>::Zero();

    for (int i = 0; i < 2; i++) {
        Eigen::Matrix<double, 3, 1> contactForceInWorldFrame = contact_force.segment(i*6, 3);
        Eigen::Matrix<double, 3, 1> positionComToContactPointInWorldFrame = data_desired.oMf[endEffectorFrameIndices[i]].translation() - data_desired.com[0];
        centroidalMomentumRate.template head<3>() += contactForceInWorldFrame;
        centroidalMomentumRate.template tail<3>().noalias() += positionComToContactPointInWorldFrame.cross(contactForceInWorldFrame);
    }  // end of i loop



    // normalize by the total mass
    centroidalMomentumRate /= robotMass;

  return centroidalMomentumRate;
}


Eigen::Matrix<double, 3, 1> TaskManager::getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles) 
{
    const double sz = sin(eulerAngles(0));
    const double cz = cos(eulerAngles(0));
    const double sy = sin(eulerAngles(1));
    const double cy = cos(eulerAngles(1));
    const double dz = derivativesEulerAngles(0);
    const double dy = derivativesEulerAngles(1);
    const double dx = derivativesEulerAngles(2);
    return {-sz * dy + cy * cz * dx, cz * dy + cy * sz * dx, dz - sy * dx};
}



Eigen::Matrix<double, 3, 3> TaskManager::getRotationMatrixFromZyxEulerAngles(const Eigen::Matrix<double, 3, 1>& eulerAngles) 
{
    const double z = eulerAngles(0);
    const double y = eulerAngles(1);
    const double x = eulerAngles(2);

    const double c1 = cos(z);
    const double c2 = cos(y);
    const double c3 = cos(x);
    const double s1 = sin(z);
    const double s2 = sin(y);
    const double s3 = sin(x);

    const double s2s3 = s2 * s3;
    const double s2c3 = s2 * c3;

    // clang-format off
    Eigen::Matrix<double, 3, 3> rotationMatrix;
    rotationMatrix << c1 * c2,      c1 * s2s3 - s1 * c3,       c1 * s2c3 + s1 * s3,
                      s1 * c2,      s1 * s2s3 + c1 * c3,       s1 * s2c3 - c1 * s3,
                          -s2,                  c2 * s3,                   c2 * c3;
    // clang-format on
    return rotationMatrix;
}




Eigen::Matrix<double, 3, 1> TaskManager::rotationErrorInWorld(const Eigen::Matrix<double, 3, 3>& rotationMatrixLhs, const Eigen::Matrix<double, 3, 3>& rotationMatrixRhs) 
{
    /* Note that this error (W_R_lhs * rhs_R_W) does not follow the usual concatination of rotations.
    * It follows from simplifying:
    *    errorInWorld = W_R_lhs * angleAxis(rhs_R_W * W_R_lhs)
    *                 = angleAxis(W_R_lhs * rhs_R_W * W_R_lhs * lhs_R_W)
    *                 = angleAxis(W_R_lhs * rhs_R_W)
    */
        const auto& R = rotationMatrixLhs * rotationMatrixRhs.transpose();


        // Compute trace and skew vector
        const double trace = R(0, 0) + R(1, 1) + R(2, 2);
        const Eigen::Vector3d skewVector(R(2, 1) - R(1, 2), R(0, 2) - R(2, 0), R(1, 0) - R(0, 1));

        // Tolerance to select alternative solution near singularity
        const double eps = 1e-8;
        const double smallAngleThreshold = 3.0 - eps;   // Use Taylor expansion if trace > 3 - eps
        const double largeAngleThreshold = -1.0 + eps;  // Use quaternion solution if trace < -1.0 + eps

        // Clip trace away from singularities
        const double safeHighTrace = (trace > smallAngleThreshold) ? smallAngleThreshold : trace;
        const double safeTrace = (safeHighTrace > largeAngleThreshold) ? safeHighTrace : largeAngleThreshold;

        // Rotation close to zero -> use Taylor expansion
        const Eigen::Vector3d taylorExpansionSol = (0.75 - trace / 12.0) * skewVector;

        // Normal rotation, use normal logarithmic map
        const double tmp = 0.5 * (safeTrace - 1.0);
        const double theta = std::acos(tmp);
        const Eigen::Vector3d normalSol = (0.5 * theta / std::sqrt(1.0 - tmp * tmp)) * skewVector;

        // Quaternion solution for rotations near 180 degrees
        Eigen::Quaterniond q(R);
        if (q.w() < 0.0) {
            q.coeffs() = -q.coeffs(); // Ensure quaternion scalar part is positive
        }

        const double qVecNorm = 0.5 * std::sqrt(3.0 - safeHighTrace);
        const Eigen::Vector3d quaternionSol = 4.0 * std::atan(qVecNorm / (q.w() + 1.0)) * q.vec() / qVecNorm;

        // Select solution
        return (trace > largeAngleThreshold) ? ((trace > smallAngleThreshold) ? taylorExpansionSol : normalSol) : quaternionSol;
}

Eigen::Matrix3d TaskManager::rotation_matrix(const std::string& axis, double angle) {
    Eigen::Matrix3d R;
    double c = std::cos(angle);
    double s = std::sin(angle);

    if (axis == "x") {
        R << 1, 0, 0,
             0, c, -s,
             0, s, c;
    } else if (axis == "y") {
        R << c, 0, s,
             0, 1, 0,
            -s, 0, c;
    } else if (axis == "z") {
        R << c, -s, 0,
             s, c, 0,
             0, 0, 1;
    } else {
        throw std::invalid_argument("Axis must be 'x', 'y', or 'z'.");
    }
    return R;
}

}