
#pragma once

#include <Eigen/Dense>
#include <utility>
#include <vector>
#include <string>
#include <iostream>
#include "BaseTask.h"
#include <pinocchio/algorithm/centroidal.hpp>
#include <pinocchio/algorithm/crba.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/rnea.hpp>
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/kinematics.hpp"
#include "pinocchio/algorithm/kinematics-derivatives.hpp"
#include "pinocchio/algorithm/center-of-mass.hpp"

#include "DataPackage/include/DataPackage.h"

namespace WBC_Solver
{


class TaskManager {
public:

    TaskManager(DataPackage & data);
    Task formulateFloatingBaseEomTask();
    Task formulateTorqueLimitsTask();
    Task formulateFrictionConeTask();
    Task formulateBaseAccelTask();
    Task formulateBasePosTask();
    Task formulateRightFootPosTask();
    Task formulateRightFootOriTask();
    Task formulateLeftFootPosTask();
    Task formulateLeftFootOriTask();
    Task formulateBaseOriTask();
    Task formulateJointPositionTask2();
    Task formulateJointPositionTask();

    Task formulateLegJointPositionTask();
    Task formulateLeftLegJointPositionTask();
    Task formulateRightLegJointPositionTask();
    Task formulateArmJointPositionTask();
    Task formulateWaistJointPositionTask();

    Task formulateContactTangentialForceMinTask() const;
    Task formulateContactNormalForceMinTask() const;
    Task formulateContactForceRefTask() const;
    Task formulateContactForceRefTask2() const;

    Task formulateSwingLegTask();
    Task formulateStanceBaseAccelTask();
    // Task formulateZeroPositionAccelTask(const Eigen::VectorXd& stateDesired, const Eigen::VectorXd& inputDesired, double period);



protected:
    // friend class WeightedWBCSolver;
    double frictionCoeff ;

    pinocchio::Model model_wbc;

    pinocchio::Data data_measured;
    
    pinocchio::Data data_desired;

    pinocchio::Data data_IK;


    void UpdateMeasured ();

    void UpdateDesired();


    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_desired;
    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_dot_desired;

    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_actual;
    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_dot_actual;


    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> jacobi, jacobi_dot;
    Eigen::Matrix<double, 6, Eigen::Dynamic> base_jacobi, base_jacobi_dot;
    Eigen::Matrix<double, 6, Eigen::Dynamic> leftfoot_jacobi, leftfoot_jacobi_dot;
    Eigen::Matrix<double, 6, Eigen::Dynamic> rightfoot_jacobi, rightfoot_jacobi_dot;



    Eigen::Matrix<double, 6, 1> base_pose_desired;
    Eigen::Matrix<double, 6, 1> base_velocity_desired;
    Eigen::Matrix<double, 6, 1> base_acceleration_desired;

    Eigen::Vector3d feet_r_Pos_W_desire, feet_l_Pos_W_desire;
    Eigen::Vector3d feet_r_LinVel_W_desire, feet_l_LinVel_W_desire;
    Eigen::Vector3d feet_r_Pos_W_actual, feet_l_Pos_W_actual;
    Eigen::Vector3d feet_r_LinVel_W_actual, feet_l_LinVel_W_actual;

    Eigen::Vector3d feet_r_AngVel_W_actual, feet_l_AngVel_W_actual;
    Eigen::Matrix<double, 3, 3> feet_r_Rot_W_actual, feet_l_Rot_W_actual;
    double feet_l_OmegaZ_W_desire, feet_r_OmegaZ_W_desire;
    double feet_l_EulerZ_W_desire, feet_r_EulerZ_W_desire;

    Eigen::Matrix<double, Eigen::Dynamic,1> torq_desire, torq_cmd; 


    Eigen::Matrix<double, Eigen::Dynamic, 1> torqueLimits;

    Eigen::Matrix<double, 12,1> contact_force;

    int numThreeDofContacts;
    int generalizedCoordinatesNum;
    int numConstraints;
    int numContacts;
    int numDecisionVars;
    int actuatedDofNum;

    std::vector <int> endEffectorFrameIndices;
    // std::array<bool, 8> contact_flag { false, false, false, false, false, false, false, false };
    // std::array<bool, 8> contact_flag { false, true, false, true, false, true, false, true };
    std::vector<bool> contact_flag; //{ true, true, true, true, true, true, true, true };
    // std::array<bool, 4> contact_flag { true, true, true, true };


    double BaseOriTask_P,BaseOriTask_D, BasePosTask_P, BasePosTask_D;

    double BasePosXTask_P, BasePosXTask_D;
    double BasePosYTask_P, BasePosYTask_D;
    double BasePosZTask_P, BasePosZTask_D;

    double FootOriTask_P,FootOriTask_D;

    double FootPosTask_P, FootPosTask_D;
    double JointPosTask_P, JointPosTask_D;

    double LegJointPosTask_P, LegJointPosTask_D;
    double ArmJointPosTask_P, ArmJointPosTask_D;
    double WaistJointPosTask_P, WaistJointPosTask_D;

    double torq_P, torq_D;

    int IndexLegStart, IndexLegLength;
    int IndexArmStart, IndexArmLength;
    int IndexWaistStart, IndexWaistLength;

    bool isWalking;

    double zero_TransTime{2};

private:

    //CentroidalDynamic
    double robotMass;

    Eigen::Matrix<double, Eigen::Dynamic, 1> getPinocchioJointVelocity(const pinocchio::Model& model_com, const pinocchio::Data& data_com);
    Eigen::Matrix<double, Eigen::Dynamic, 1> getPinocchioJointPosition(pinocchio::Model& model_com, pinocchio::Model& data_com, const Eigen::Matrix<double, Eigen::Dynamic, 1>& state_desired);
    void computeBaseKinematicsFromCentroidalModel(const pinocchio::Model& model_com, const pinocchio::Data& data_com);

    Eigen::Matrix<double, 3, 3> getMappingFromEulerAnglesZyxDerivativeToGlobalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles); 
    Eigen::Matrix<double, 3, 1> getGlobalAngularAccelerationFromEulerAnglesZyxDerivatives(
        const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles,
        const Eigen::Matrix<double, 3, 1>& secondDerivativesEulerAngles); 
    Eigen::Matrix<double, 3, 1> getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles);
    Eigen::Matrix<double, 6, 6> computeFloatingBaseCentroidalMomentumMatrixInverse(const Eigen::Matrix<double, 6, 6>& Ab);
    Eigen::Matrix<double, 3, 1> getEulerAnglesZyxDerivativesFromGlobalAngularVelocity(const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& angularVelocity) ;
    Eigen::Matrix<double, 6, 1> getNormalizedCentroidalMomentumRate();
    Eigen::Matrix<double, 3, 3> getRotationMatrixFromZyxEulerAngles(const Eigen::Matrix<double, 3, 1>& eulerAngles);
    Eigen::Matrix<double, 3, 1> rotationErrorInWorld(const Eigen::Matrix<double, 3, 3>& rotationMatrixLhs, const Eigen::Matrix<double, 3, 3>& rotationMatrixRhs) ; 
    Eigen::Matrix3d rotation_matrix(const std::string& axis, double angle) ;
};


}
