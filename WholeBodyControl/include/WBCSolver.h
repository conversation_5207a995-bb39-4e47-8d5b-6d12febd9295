#ifndef WHOLEBODYCONTROL_H
#define WHOLEBODYCONTROL_H


#include "DataPackage/include/DataPackage.h"
#include "TaskManager.h"
#include "qpOASES.hpp"

namespace WBC_Solver
{

// using vector_t = Eigen::Matrix<double, Eigen::Dynamic, 1>;


class WeightedWBCSolver: public TaskManager
{

public:

    WeightedWBCSolver(DataPackage &data, std::string state):TaskManager(data),qpProblem(78, 142)
    {
        // std::cout << "WBC " << std::endl;
        
        std::string file_path = "../StateMachine/config/" + state + ".yaml";
        std::cout<<"cc: "<<file_path<<std::endl;

        YAML::Node task_config = YAML::LoadFile(file_path);

        if(state == "ZERO"){

            auto data = task_config["zero_joint_position"];
            int N = data.size();  // 期望向量长度
            zero_pose.resize(N);
        
            for (int i = 0; i < N; ++i) {
                zero_pose(i) = data[i].as<double>();
            }

            zero_totalTime = task_config["zero_totalTime"].as<double>();

        }else{
            // weightBaseAccel = task_config["formulateBaseAccelTask"].as<double>();
            weightBaseOri = task_config["formulateBaseOriTask"].as<double>();
            weightBasePos = task_config["formulateBasePosTask"].as<double>();
            weightFootPosSwing = task_config["weightFootPosSwing"].as<double>();
            weightFootPosSup = task_config["weightFootPosSup"].as<double>();
            // weightJointPos = task_config["weightJointPos"].as<double>();
            weightTangentialContactRorceMin = task_config["weightTangentialContactRorceMin"].as<double>();
            weightNormalContactRorceMin = task_config["weightNormalContactRorceMin"].as<double>();
            weightContactRorceRef = task_config["weightContactRorceRef"].as<double>();

            // weightLegJointPos = task_config["weightLegJointPos"].as<double>();
            weightArmJointPos = task_config["weightArmJointPos"].as<double>();
            weightWaistJointPos = task_config["weightWaistJointPos"].as<double>();
            weightLegJointPosSwing = task_config["weightLegJointPosSwing"].as<double>();
            weightLegJointPosSup = task_config["weightLegJointPosSup"].as<double>();

            weightFootOriSwing = task_config["weightFootOriSwing"].as<double>();
            weightFootOriSup = task_config["weightFootOriSup"].as<double>();
        }



        last_qpSol.setZero(numDecisionVars,1);
        qpSol.setZero(numDecisionVars,1);
        A.setZero(142, 78);
        H.setZero(78,78);
        g.setZero(78,1);
        lbA.setZero(142,1);
        ubA.setZero(142,1);
        options.setToMPC();
    // options.setToFast();

        options.printLevel = qpOASES::PL_LOW;
        options.enableEqualities = qpOASES::BT_TRUE;
        qpProblem.setOptions(options);

    }

    double swingR{0}, swingL{0};
    
    void LoadConfigurationFromFSM();

    void GetDataFromPackage(DataPackage &data);
    void SetDataToPackage(DataPackage &data);
    void SetZeroTorq(DataPackage &data);
    void SetMotorZero(DataPackage &data);
    
    Eigen::Matrix<double, Eigen::Dynamic,1> motor_pos;

    void ThirdpolyVector(const Eigen::VectorXd& p0, const Eigen::VectorXd& p0_dot,
        const Eigen::VectorXd& p1, const Eigen::VectorXd& p1_dot,
        double totalTime, double currentTime,
        Eigen::VectorXd& pd, Eigen::VectorXd& pd_dot);
    void Step();
    Eigen::Matrix<double, Eigen::Dynamic, 1> last_qpSol;
    Eigen::Matrix<double, Eigen::Dynamic, 1> qpSol;

    Eigen::VectorXd zero_pose;
    Eigen::VectorXd init_pose;
    bool If_first_Zero{true};
    double t_Zero, dt_Zero;
    double zero_totalTime;

private:
    

    double weightBaseAccel, weightBaseOri, weightBasePos;
    double weightFootPosSwing, weightFootPosSup, weightJointPos;
    // double weightLegJointPos;
    double weightArmJointPos, weightWaistJointPos;
    double weightLegJointPosSwing, weightLegJointPosSup;
    double weightFootOriSwing, weightFootOriSup;
    double weightTangentialContactRorceMin, weightNormalContactRorceMin, weightContactRorceRef;

    double gait_t{0};    
    

    // TaskManager taskmanager;

    Task formulateConstraints();
    Task formulateCost();

    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor> A;
    Eigen::Matrix<double, Eigen::Dynamic, 1> g;
    Eigen::Matrix<double, Eigen::Dynamic, 1> lbA, ubA;  // clang-format off
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> H;
    // qpOASES::QProblem qpProblem;
    qpOASES::SQProblem qpProblem;
    qpOASES::Options options;
    // int nWsr = 100;
    bool first_time = true;
};

}
#endif // WHOLEBODYCONTROL_H