#pragma once

#include <Eigen/Dense>
#include <utility>
#include <vector>
#include <string>
#include <iostream>


namespace WBC_Solver
{

class Task
{
public:
//   EIGEN_MAKE_ALIGNED_OPERATOR_NEW

  Task() = default;

  Task(Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a, Eigen::Matrix<double, Eigen::Dynamic, 1> b, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> d, Eigen::Matrix<double, Eigen::Dynamic, 1> f)
    : a_(std::move(a)), d_(std::move(d)), b_(std::move(b)), f_(std::move(f))
  {
  }

  explicit Task(size_t numDecisionVars)
    : Task(Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>::Zero(0, numDecisionVars), Eigen::Matrix<double, Eigen::Dynamic, 1>::Zero(0), Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic>::Zero(0, numDecisionVars), Eigen::Matrix<double, Eigen::Dynamic, 1>::Zero(0))
  {
  }

  Task operator+(const Task& rhs) const
  {
    return { concatenateMatrices(a_, rhs.a_), concatenateVectors(b_, rhs.b_), concatenateMatrices(d_, rhs.d_),
             concatenateVectors(f_, rhs.f_) };
  }

  Task operator*(double rhs) const
  {  
    return {a_.cols() > 0 ? rhs * a_ : a_,
            b_.cols() > 0 ? rhs * b_ : b_,
            d_.cols() > 0 ? rhs * d_ : d_,
            f_.cols() > 0 ? rhs * f_ : f_};  
  }

  Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> a_, d_;
  Eigen::Matrix<double, Eigen::Dynamic, 1> b_, f_;

  static Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> concatenateMatrices(Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> m1, Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> m2)
  {
    if (m1.cols() <= 0)
    {
      return m2;
    }
    else if (m2.cols() <= 0)
    {
      return m1;
    }
    assert(m1.cols() == m2.cols());
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> res(m1.rows() + m2.rows(), m1.cols());
    res << m1, m2;
    return res;
  }

  static Eigen::Matrix<double, Eigen::Dynamic, 1> concatenateVectors(const Eigen::Matrix<double, Eigen::Dynamic, 1> & v1, const Eigen::Matrix<double, Eigen::Dynamic, 1> & v2)
  {
    if (v1.cols() <= 0)
    {
      return v2;
    }
    else if (v2.cols() <= 0)
    {
      return v1;
    }
    assert(v1.cols() == v2.cols());
    Eigen::Matrix<double, Eigen::Dynamic, 1>  res(v1.rows() + v2.rows());
    res << v1, v2;
    return res;
  }
};



}