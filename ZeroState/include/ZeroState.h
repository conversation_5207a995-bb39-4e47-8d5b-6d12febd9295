
#pragma once

#include <Eigen/Dense>
#include <utility>
#include <vector>
#include <string>
#include <iostream>
#include <pinocchio/algorithm/centroidal.hpp>
#include <pinocchio/algorithm/crba.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/rnea.hpp>
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/kinematics.hpp"
#include "pinocchio/algorithm/kinematics-derivatives.hpp"
#include "pinocchio/algorithm/center-of-mass.hpp"

#include "DataPackage/include/DataPackage.h"



class ZeroState {
public:

    ZeroState()
    {
        std::string file_path = "../StateMachine/config/ZERO.yaml";
        std::cout<<"cc: "<<file_path<<std::endl;

        YAML::Node task_config = YAML::LoadFile(file_path);

        auto data = task_config["zero_joint_position"];
        int N = data.size();  // 期望向量长度
        zero_pose.resize(N);
    
        for (int i = 0; i < N; ++i) {
            zero_pose(i) = data[i].as<double>();
        }

        zero_totalTime = task_config["zero_totalTime"].as<double>();
    }

    int actuatedDofNum;

    Eigen::VectorXd zero_pose;
    Eigen::VectorXd init_pose;
    bool If_first_Zero{true};
    double t_Zero, dt_Zero;
    double zero_totalTime;

    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_actual;
    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_dot_actual;

    Eigen::Matrix<double, Eigen::Dynamic,1> motor_pos;


    void GetDataFromPackage(DataPackage &data);
    void SetMotorZero(DataPackage &data);
    void ThirdpolyVector(const Eigen::VectorXd& p0, const Eigen::VectorXd& p0_dot,
        const Eigen::VectorXd& p1, const Eigen::VectorXd& p1_dot,
        double totalTime, double currentTime,
        Eigen::VectorXd& pd, Eigen::VectorXd& pd_dot);


};



