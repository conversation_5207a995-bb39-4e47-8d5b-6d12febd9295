/*
 * This file is part of broccoli
 * Copyright (C) 2020 Chair of Applied Mechanics, Technical University of Munich
 * https://www.mw.tum.de/am/
 */

#pragma once

#include "SignalSource.hpp"
#include <random>

namespace broccoli {
namespace control {
    /*!
     * \brief Deterministic Gaussian Noise signal generated by a pseudo random number generator.
     * \ingroup broccoli_control_sources
     * The output-values are normal-distributed with zero mean and the specified standard deviation.
     * \tparam <PERSON>alar The scalar output data type
     */
    template <typename Scalar = double>
    class GaussianNoise : public SignalSource<GaussianNoise<Scalar>> {
    public:
        /*!
         * \brief Constructs new Gaussian Noise Source
         * \param standardDeviation Standard deviation of the noise signal
         */
        explicit GaussianNoise(const double& standardDeviation)
            : SignalSource<GaussianNoise<Scalar>>(true)
            , m_value(Scalar(0))
            , m_normalDistribution(Scalar(0), standardDeviation)
        {
        }

        /*!
         * \brief Copy constructor
         * \param other Object to be copied
         */
        GaussianNoise(const GaussianNoise& other)
            : SignalSource<GaussianNoise<Scalar>>(true)
            , m_value(Scalar(0))
            , m_normalDistribution(Scalar(0), other.m_normalDistribution.stddev())
        {
        }

        /*!
         * \brief Clears the history of generated values
         * The standard deviation / normal distribution is achieved by keeping track
         * of already generated samples and their distribution. This resets the stored
         * history.
         * \returns A reference to this instance
         */
        GaussianNoise<Scalar>& reset()
        {
            m_normalDistribution.reset();
            return *this;
        }

        //! \copydoc SignalBase::value()
        const Scalar& value() const
        {
            return m_value;
        }

        //! \copydoc SignalBase::sampleTime()
        double sampleTime() const
        {
            // time won't change
            return 0.0;
        }

        //! \copydoc SignalSource::computeOutput()
        GaussianNoise<Scalar>& computeOutput(const double& time)
        {
            if (this->isActive()) {
                m_value = m_normalDistribution(m_pseudoRandomness);
            } else {
                m_value = Scalar(0);
            }
            (void)time;
            return *this;
        }

    private:
        //! The signal value
        double m_value;

        //! Normal Distribution object
        std::normal_distribution<Scalar> m_normalDistribution;

        //! Pseudo randomness generator based on linear congruential engine
        std::minstd_rand m_pseudoRandomness;
    };
} // namespace control
} // namespace broccoli
