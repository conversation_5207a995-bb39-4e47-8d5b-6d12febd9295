
#include <chrono>
#include <cstdint>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <iostream>
#include <memory>
#include <mutex>
#include <new>
#include <string>
#include <thread>

#include <mujoco/mujoco.h>
#include "glfw_adapter.h"
#include "simulate.h"
#include "array_safety.h"
#include "DataPackage/include/DataPackage.h"
// #include "GLFW_callbacks.h"
#include "mujoco_interface.h"
#include <spdlog/spdlog.h>
#include <spdlog/sinks/basic_file_sink.h> // 用于文件输出
// #include "WholeBodyControl/include/WBCSolver.h"
#include "GaitPlanner/include/GaitPlanner.h"
#include "StateEstimator/include/StateEstimator.h"
#include "StateMachine/include/statemachinemanager_rl.h"
#include "DataLogger/include/DataLogger.h"
#include "Joystick/include/joystick_int.h"
#include "StateEstimator/include/basicfunction.h"

// for rl
#include <torch/script.h>
#define DATALOG


#define MUJOCO_PLUGIN_DIR "mujoco_plugin"

extern "C" {
#if defined(_WIN32) || defined(__CYGWIN__)
  #include <windows.h>
#else
  #if defined(__APPLE__)
    #include <mach-o/dyld.h>
  #endif
  #include <sys/errno.h>
  #include <unistd.h>
#endif
}

namespace {
namespace mj = ::mujoco;
namespace mju = ::mujoco::sample_util;

// constants
const double syncMisalign = 0.1;        // maximum mis-alignment before re-sync (simulation seconds)
const double simRefreshFraction = 0.7;  // fraction of refresh available for simulation
const int kErrorLength = 1024;          // load error string length

// model and data
mjModel* m = nullptr;
mjData* d = nullptr;

using Seconds = std::chrono::duration<double>;




//------------------------------------------- simulation -------------------------------------------


mjModel* LoadModel(const char* file, mj::Simulate& sim) {
  // this copy is needed so that the mju::strlen call below compiles
  char filename[mj::Simulate::kMaxFilenameLength];
  mju::strcpy_arr(filename, file);

  // make sure filename is not empty
  if (!filename[0]) {
    return nullptr;
  }

  // load and compile
  char loadError[kErrorLength] = "";
  mjModel* mnew = 0;
  if (mju::strlen_arr(filename)>4 &&
      !std::strncmp(filename + mju::strlen_arr(filename) - 4, ".mjb",
                    mju::sizeof_arr(filename) - mju::strlen_arr(filename)+4)) {
    mnew = mj_loadModel(filename, nullptr);
    if (!mnew) {
      mju::strcpy_arr(loadError, "could not load binary model");
    }
  } else {
    mnew = mj_loadXML(filename, nullptr, loadError, kErrorLength);
    // remove trailing newline character from loadError
    if (loadError[0]) {
      int error_length = mju::strlen_arr(loadError);
      if (loadError[error_length-1] == '\n') {
        loadError[error_length-1] = '\0';
      }
    }
  }

  mju::strcpy_arr(sim.load_error, loadError);

  if (!mnew) {
    std::printf("%s\n", loadError);
    return nullptr;
  }

  // compiler warning: print and pause
  if (loadError[0]) {
    // mj_forward() below will print the warning message
    std::printf("Model compiled, but simulation warning (paused):\n  %s\n", loadError);
    sim.run = 0;
  }

  return mnew;
}



void update_camera(mjvCamera* cam, mjData* data) {
    // 设置相机类型为自由相机
    cam->type = mjCAMERA_FREE;

    // 设置相机的目标点为基座的位置
    cam->lookat[0] = data->xpos[3]; // X 坐标
    cam->lookat[1] = data->xpos[4]; // Y 坐标
    cam->lookat[2] = data->xpos[5]; // Z 坐标
}

// 在仿真循环中调用此函数
void print_mouse_force(const mjModel* model, const mjData* data) {
  mjtNum total_force[3] = {0, 0, 0};
  for (int body_id = 0; body_id < model->nbody; body_id++) {
      int offset = body_id * 6;
      total_force[0] += data->xfrc_applied[offset];
      total_force[1] += data->xfrc_applied[offset + 1];
      total_force[2] += data->xfrc_applied[offset + 2];
  }
  mjtNum magnitude = mju_norm3(total_force);
  // 打印三维力和标量
  // printf("Total mouse force vector: [Fx=%.2f N, Fy=%.2f N, Fz=%.2f N]\n", 
  //   total_force[0], total_force[1], total_force[2]);
  // printf("Total mouse force magnitude: %.2f N\n", magnitude);
  
}

// simulate in background thread (while rendering in main thread)
void PhysicsLoop(mj::Simulate& sim, mjvCamera* cam) {


  // cpu-sim syncronization point
  std::chrono::time_point<mj::Simulate::Clock> syncCPU;
  mjtNum syncSim = 0;

  DataPackage package;
  package.init();

  StateEstimator state_estimator(package);

  Joystick joystick;
  joystick.init();

  DataLogger Logger;



#ifdef DATALOG
    std::ofstream foutData;
    foutData.open("datacollection_sim_rl.txt", std::ios::out);
    // Eigen::VectorXd dataL = Eigen::VectorXd::Zero(34 + 6 * motorNum);
    Eigen::VectorXd dataL = Eigen::VectorXd::Zero(400);
#endif

  // spdlog::info("DataLogger");

  StateMachineManager_RL manager;
  // spdlog::info("StateMachineManager");
  manager.init(package);
  // spdlog::info("manager.init");
  // 设置日志级别
  spdlog::set_level(spdlog::level::info); // 只记录INFO级别及以上的日志
  // WBC_Solver::WeightedWBCSolver wbc(package);
  spdlog::info("wbc load success!");

  MujocoInterface MujocoInterface(package); // data interface for Mujoco
  spdlog::info("interface load success!");


  spdlog::info("model load success!");

  cam->distance = 3;
  cam->elevation = 0;
  cam->azimuth = 180;
  
  // run until asked to exit
  while (!sim.exitrequest.load()) {
    // if (sim.droploadrequest.load()) {

    if (sim.uiloadrequest.load()) {
      sim.uiloadrequest.fetch_sub(1);
      sim.LoadMessage(sim.filename);
      mjModel* mnew = LoadModel(sim.filename, sim);
      mjData* dnew = nullptr;
      if (mnew) dnew = mj_makeData(mnew);
      if (dnew) {
        sim.Load(mnew, dnew, sim.filename);

        mj_deleteData(d);
        mj_deleteModel(m);

        m = mnew;
        d = dnew;
        // ********************************
        // init_cmd(d);
        // ********************************

        mj_forward(m, d);

        // allocate ctrlnoise
        // free(ctrlnoise);
        // ctrlnoise = static_cast<mjtNum*>(malloc(sizeof(mjtNum)*m->nu));
        // mju_zero(ctrlnoise, m->nu);
      } else {
        sim.LoadMessageClear();
      }
    }
    
    // sleep for 1 ms or yield, to let main thread run
    //  yield results in busy wait - which has better timing but kills battery life
    if (sim.run && sim.busywait) {
      std::this_thread::yield();
    } else {
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    { // todo 控制变量的生命周期
      // lock the sim mutex
      
      const std::unique_lock<std::recursive_mutex> lock(sim.mtx);

      // run only if model is present
      if (m) {
        // running
        if (sim.run) {
          bool stepped = false;

          // ************ test ****************


          joystick.GetDataFromPackage(package);
          joystick.run();
          joystick.SetDataToPackage(package);


          MujocoInterface.GetDataFromSim(m,d);
          MujocoInterface.SetDataToPackage(package);


          state_estimator.GetDataFromPackage(package);
          // state_estimator.Step(m, d, package);
          state_estimator.Step(package);
          state_estimator.SetDataToPackage(package);


          manager.GetDataFromPackage(package);


          manager.run(package);

          manager.SetDataToPackage(package);  


          if (package.CurrentState == State::IDLE)
          {
            MujocoInterface.SetTorque_zero(package,d);
          }


          if (package.CurrentState == State::ZERO)
          {
            MujocoInterface.SetTorque_zero(package,d);
          }

          if (package.CurrentState == State::NLP)
          {
            MujocoInterface.SetTorque(package,d);
            // std::cout<<"1: "<<std::endl;
          }

          double sim_time = d->time;
          
          double x1= d->xanchor[36];
          double y1= d->xanchor[37];
          double z1= d->xanchor[38];

          print_mouse_force(m, d);

          mj_step(m, d);

          double x2= d->xanchor[36];
          double y2= d->xanchor[37];
          double z2= d->xanchor[38];

          double vx=(x2-x1)/package.control_period;
          double vy=(y2-y1)/package.control_period;
          double vz=(z2-z1)/package.control_period;

          Eigen::Vector3d velocity(vx, vy, vz);
          
          Logger.SaveDataToFile(package, sim_time, d, velocity, state_estimator);

          update_camera(cam, d);


#ifdef DATALOG
        dataL[0] = 0;
        //   dataL.segment(1, 289) = package.input_nlp;
        //   dataL.segment(290, 19) = package.output_nlp;
        //   dataL.segment(309, 19) = package.motor_pos;
        //   dataL.segment(328, 19) = package.motor_vel;
        //   dataL.segment(347, 19) = package.motor_torque;

          dataL.block(1, 0, 289, 1) = package.input_nlp;
          dataL.block(290, 0, 19, 1) = package.output_nlp;
          dataL.block(309, 0, 19, 1) = package.motor_pos;
          dataL.block(328, 0, 19, 1) = package.motor_vel;
          dataL.block(347, 0, 19, 1) = package.motor_torque;


        //   dataL.block(25 , 0, motorNum, 1) = package.generalized_q_desired.tail(motorNum);
        //   dataL.block(25 + 1 * motorNum, 0, motorNum, 1) = package.generalized_q_dot_desired.tail(motorNum);
        //   dataL.block(25 + 2 * motorNum, 0, motorNum, 1) = package.motor_Torque_desire;  //only for real robot
        //   dataL.block(25 + 3 * motorNum, 0, motorNum, 1) = package.motor_pos;
        //   dataL.block(25 + 4 * motorNum, 0, motorNum, 1) = package.motor_vel;
        //   dataL.block(25 + 5 * motorNum, 0, motorNum, 1) = package.motor_torque;
        //   dataL.block(25 + 6 * motorNum, 0, 3, 1) = package.imu_lin_acc;
        //   dataL.block(25 + 6 * motorNum+3, 0, 3, 1) = package.vel_base_obser;
        //   dataL[31 + 6 * motorNum] = package.contact_force[2];
        //   dataL[32 + 6 * motorNum] = package.contact_force[8];
        //   dataL[33 + 6 * motorNum] = package.FootNum;
          
          // Write to file


        //   dataL[0] = elapsed_log.count();
        //   dataL.block(1, 0, 6, 1) = package.generalized_q_actual.head(6);
        //   dataL.block(7, 0, 6, 1) = package.generalized_q_dot_actual.head(6);
        //   dataL.block(13, 0, 6, 1) = package.generalized_q_desired.head(6);
        //   dataL.block(19, 0, 6, 1) = package.generalized_q_dot_desired.head(6);
        //   dataL.block(25 , 0, motorNum, 1) = package.generalized_q_desired.tail(motorNum);
        //   dataL.block(25 + 1 * motorNum, 0, motorNum, 1) = package.generalized_q_dot_desired.tail(motorNum);
        //   dataL.block(25 + 2 * motorNum, 0, motorNum, 1) = package.motor_Torque_desire;  //only for real robot
        //   dataL.block(25 + 3 * motorNum, 0, motorNum, 1) = package.motor_pos;
        //   dataL.block(25 + 4 * motorNum, 0, motorNum, 1) = package.motor_vel;
        //   dataL.block(25 + 5 * motorNum, 0, motorNum, 1) = package.motor_torque;
        //   dataL.block(25 + 6 * motorNum, 0, 3, 1) = package.imu_lin_acc;
        //   dataL.block(25 + 6 * motorNum+3, 0, 3, 1) = package.vel_base_obser;


          dataLog(dataL, foutData);
#endif


        }

        // paused
        else {
          // run mj_forward, to update rendering and joint sliders
          mj_forward(m, d);
          sim.speed_changed = true;
        }
      }
    }  // release std::lock_guard<std::mutex>
  }
  // ****************************
  // mujocolcm.joinLCMThread();
  // ****************************
}
}  // namespace



//-------------------------------------- physics_thread --------------------------------------------

void PhysicsThread(mj::Simulate* sim, const char* filename, mjvCamera* cam) {


  // request loadmodel if file given (otherwise drag-and-drop)
  if (filename != nullptr) {
    sim->LoadMessage(filename);
    m = LoadModel(filename, *sim);
    if (m) d = mj_makeData(m);
    if (d) {
      // ********************************

      mju_copy(d->qpos, m->key_qpos, m->nq*1); // set ini pos in Mujoco
        // ********************************

      mj_forward(m, d);
      // ********************************
      sim->Load(m, d, filename);
      mj_forward(m, d);

      // allocate ctrlnoise
      // free(ctrlnoise);
      // ctrlnoise = static_cast<mjtNum*>(malloc(sizeof(mjtNum)*m->nu));
      // mju_zero(ctrlnoise, m->nu);
    } else {
      sim->LoadMessageClear();
    }
  }





  PhysicsLoop(*sim, cam);

  // delete everything we allocated

  // free(ctrlnoise);
  mj_deleteData(d);
  mj_deleteModel(m);
}

//------------------------------------------ main --------------------------------------------------


//**************************
// run event loop
int main(int argc, char** argv) {


  // print version, check compatibility
  std::printf("MuJoCo version %s\n", mj_versionString());
  if (mjVERSION_HEADER!=mj_version()) {
    mju_error("Headers and library have different versions");
  }

  // scan for libraries in the plugin directory to load additional plugins
  // scanPluginLibraries();

  mjvCamera cam;
  mjv_defaultCamera(&cam);

  mjvOption opt;
  mjv_defaultOption(&opt);

  mjvPerturb pert;
  mjv_defaultPerturb(&pert);






  // simulate object encapsulates the UI
  auto sim = std::make_unique<mj::Simulate>(
      std::make_unique<mj::GlfwAdapter>(),
      &cam, &opt, &pert, /* is_passive = */ false
  );




  std::string file_path = "../MujocoInterface/config/mini_phybot_mimic.yaml";
  YAML::Node config = YAML::LoadFile(file_path);
  std::string env_path = config["env_path"].as<std::string>();
  std::thread physicsthreadhandle(&PhysicsThread, sim.get(), env_path.c_str(), &cam);

  // start simulation UI loop (blocking call)
  sim->RenderLoop();
  physicsthreadhandle.join();

  return 0;
}
