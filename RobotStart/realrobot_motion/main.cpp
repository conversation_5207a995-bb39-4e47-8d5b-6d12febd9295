#include"../MotorList/include/MotorList.hpp"
#include"../MotorList/include/realtime_controller.hpp"
#include <fstream>  // 追加: 用于写入文件



int main()
{

    int mode = 1;  //1为参考轨迹，2为脚部正弦
    std::string config_yaml = "../MotorList/config/Motorlist.yaml";
    double start_time = 5;
    double arm_cycle_time = 12;
    double R_tri = 0.1;

    DataPackage package;
    package.init();


    std::vector<std::string> motor_name_list;
    Eigen::VectorXd P_control_vector, D_control_vector, direction_vector;

    loadMotorConfig(config_yaml, motor_name_list, P_control_vector, D_control_vector, direction_vector);
    int motorNum = motor_name_list.size();

    // std::cout << "Loaded motor count: " << motorNum << std::endl;
    // for (int i = 0; i < motorNum; ++i) {
    //     std::cout << motor_name_list[i] << " -> P_control: " << P_control_vector(i) 
    //     << " -> D_control: " << D_control_vector(i) 
    //     << " -> direction: " << dir_control_vector(i) 
    //     << std::endl;
    // }

    MotorList motorlist;

    period_info P_info;

    // motorlist.Init(config_yaml, motorNum, motor_name_list);
    motorlist.Init(config_yaml, motorNum, motor_name_list, package);
    std::cout << "Motorlist init complete! " << std::endl;

    YAML::Node config = YAML::LoadFile(config_yaml);
    std::string Joint_file_path = config["Joint_file_path"].as<std::string>();
    Eigen::MatrixXd ref_joint_pos = readCSV(Joint_file_path, true);

    // ref_joint_pos.col(2) *= -1;
    // ref_joint_pos.col(4) *= -1;
    // ref_joint_pos.col(6) *= -1;
    // ref_joint_pos.col(3) *= -1;

    // for (int i = 0; i < ref_joint_pos.cols(); ++i) {
    //     ref_joint_pos.col(i) *= direction_vector(i);
    // }


    Eigen::VectorXd pos_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd vel_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd tor_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd init_pos_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd init_vel_actual = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd pos_desire = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd vel_desire = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd tor_desire = Eigen::VectorXd::Zero(motorNum);

    Eigen::VectorXd ones_vector = Eigen::VectorXd::Ones(motorNum);
    Eigen::VectorXd zero_vector = Eigen::VectorXd::Zero(motorNum);
    Eigen::VectorXd delta_vector = Eigen::VectorXd::Zero(motorNum);

    // 保存数据的容器（使用 vector 存储每次循环的数据）
    std::vector<std::vector<double>> pos_actual_data;
    std::vector<std::vector<double>> vel_actual_data;
    std::vector<std::vector<double>> pos_desire_data;
    std::vector<std::vector<double>> vel_desire_data;
    
    // 保存表头
    std::vector<std::string> header = {"Time"};
    for (const auto& motor_name : motor_name_list) {
        header.push_back(motor_name);
    }

    auto record_start_time = std::chrono::steady_clock::now();

    // 缓慢启动
    auto start_start_time = std::chrono::steady_clock::now();
    motorlist.GetStates(init_pos_actual, init_vel_actual, tor_actual, direction_vector);
    
    while(1){

        motorlist.GetStates(pos_actual, vel_actual, tor_actual, direction_vector);

        // 获取当前时间
        auto start_now_time = std::chrono::steady_clock::now();
        std::chrono::duration<double> elapsed_start = start_now_time - start_start_time;

        double t_start = elapsed_start.count();

        if(mode == 1){
            // 参考轨迹
            ThirdpolyVector(init_pos_actual, zero_vector, ref_joint_pos.row(0).transpose(), zero_vector,
                            start_time, t_start, pos_desire, vel_desire);
            // ThirdpolyVector(init_pos_actual, zero_vector, init_pos_actual, zero_vector,
            //                 start_time, t_start, pos_desire, vel_desire);

        }
        // pos_desire = direction_vector.array() * pos_desire.array();
        // vel_desire = direction_vector.array() * vel_desire.array();

        // delta_vector = pos_actual * (1000 - start)/1000;
        motorlist.SetCommands(pos_desire, zero_vector, zero_vector, 0.1 * P_control_vector, 0.1 *D_control_vector, direction_vector);
        // std::cout<<"pos_actual: "<<pos_actual<<std::endl;

        auto record_now_time = std::chrono::steady_clock::now();
        std::chrono::duration<double> elapsed_record = record_now_time - record_start_time;
        // 将当前时间和数据添加到内存中的数据容器
        std::vector<double> pos_actual_row = {elapsed_record.count()};
        for (int i = 0; i < pos_actual.size(); i++) {
            pos_actual_row.push_back(pos_actual[i]);
        }
        pos_actual_data.push_back(pos_actual_row);

        std::vector<double> vel_actual_row = {elapsed_record.count()};
        for (int i = 0; i < vel_actual.size(); i++) {
            vel_actual_row.push_back(vel_actual[i]);
        }
        vel_actual_data.push_back(vel_actual_row);

        std::vector<double> pos_desire_row = {elapsed_record.count()};
        for (int i = 0; i < pos_desire.size(); i++) {
            pos_desire_row.push_back(pos_desire[i]);
        }
        pos_desire_data.push_back(pos_desire_row);

        std::vector<double> vel_desire_row = {elapsed_record.count()};
        for (int i = 0; i < vel_desire.size(); i++) {
            vel_desire_row.push_back(vel_desire[i]);
        }
        vel_desire_data.push_back(vel_desire_row);


        std::this_thread::sleep_for(std::chrono::milliseconds(2));

        // 如果需要退出循环（比如设置一个最大时间或者某种条件）
        if (t_start >= start_time) break;  
    }
    // std::this_thread::sleep_for(std::chrono::milliseconds(10000));
    for(int i =0; i<6000; ++i){
        motorlist.GetStates(pos_actual, vel_actual, tor_actual, direction_vector);

        auto record_now_time = std::chrono::steady_clock::now();
        std::chrono::duration<double> elapsed_record = record_now_time - record_start_time;
        // 将当前时间和数据添加到内存中的数据容器
        std::vector<double> pos_actual_row = {elapsed_record.count()};
        for (int i = 0; i < pos_actual.size(); i++) {
            pos_actual_row.push_back(pos_actual[i]);
        }
        pos_actual_data.push_back(pos_actual_row);

        std::vector<double> vel_actual_row = {elapsed_record.count()};
        for (int i = 0; i < vel_actual.size(); i++) {
            vel_actual_row.push_back(vel_actual[i]);
        }
        vel_actual_data.push_back(vel_actual_row);

        std::vector<double> pos_desire_row = {elapsed_record.count()};
        for (int i = 0; i < pos_desire.size(); i++) {
            pos_desire_row.push_back(pos_desire[i]);
        }
        pos_desire_data.push_back(pos_desire_row);

        std::vector<double> vel_desire_row = {elapsed_record.count()};
        for (int i = 0; i < vel_desire.size(); i++) {
            vel_desire_row.push_back(vel_desire[i]);
        }
        vel_desire_data.push_back(vel_desire_row);

    }

    float RAD_test = 0;
    auto gait_start_time = std::chrono::steady_clock::now();
    periodic_task_init(&P_info);
    int all_test = 0;


    // 写入文件
    auto write_data_to_file = [](const std::string& file_name, const std::vector<std::vector<double>>& data, const std::vector<std::string>& header) {
        std::ofstream file(file_name);
        // 写入表头
        for (size_t i = 0; i < header.size(); i++) {
            file << header[i];
            if (i < header.size() - 1) file << ",";
        }
        file << std::endl;

        // 写入数据
        for (const auto& row : data) { 
            for (size_t i = 0; i < row.size(); i++) {
                file << row[i];
                if (i < row.size() - 1) file << ",";
            }
            file << std::endl;
        }
    };

    // 最后一次性写入文件
    write_data_to_file("/home/<USER>/Documents/PhybotSofware/MotorList/data_3/pos_actual.csv", pos_actual_data, header);
    write_data_to_file("/home/<USER>/Documents/PhybotSofware/MotorList/data_3/vel_actual.csv", vel_actual_data, header);
    write_data_to_file("/home/<USER>/Documents/PhybotSofware/MotorList/data_3/pos_desire.csv", pos_desire_data, header);
    write_data_to_file("/home/<USER>/Documents/PhybotSofware/MotorList/data_3/vel_desire.csv", vel_desire_data, header);



    return 0;
}


