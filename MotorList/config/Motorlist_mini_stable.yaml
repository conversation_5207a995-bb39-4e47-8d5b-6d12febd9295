DriverVersion: 0.0.0

Name: Robot
PC_IP: *************
Broadcast_IP: *************

Base_Node:
  - Name: Main_Control_Board
    DeviceType: Main_board_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 100
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: ************
    Port: 15100
    Up_Data_Speed: 1000


  - Name: left_hip_pitch
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 151
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15151
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      Motor_Heart_Beat: 0 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000               
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950
      P_control: 290
      D_control: 30
      direction: 1

    child:
      - Name: left_hip_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 152
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15152
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
          P_control: 300
          D_control: 32
          direction: 1  

      - Name: left_hip_yaw
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 153
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15153
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
          P_control: 300
          D_control: 30
          direction: -1

  - Name: left_knee
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 154
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15154
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      Motor_Heart_Beat: 0 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950
      P_control: 300
      D_control: 32
      direction: 1

    child:
      - Name: left_ankle_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 155
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15155
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
          P_control: 300
          D_control: 30
          direction: -1

      - Name: left_ankle_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 156
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950

          P_control: 70
          D_control: 7
          direction: 1

  - Name: right_hip_pitch
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 201
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15201
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      Motor_Heart_Beat: 0 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950
      P_control: 290
      D_control: 30
      direction: -1
      

    child:
      - Name: right_hip_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 202
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15202
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950

          P_control: 300
          D_control: 32
          direction: 1

      - Name: right_hip_yaw
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 203
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15203
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
          P_control: 300
          D_control: 30
          direction: -1

  - Name: right_knee 
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 204
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15204
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      Motor_Heart_Beat: 0 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950
      P_control: 300
      D_control: 32
      direction: -1

    child:
      - Name: right_ankle_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 205
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15205
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950

          P_control: 300
          D_control: 30
          direction: 1
      
      - Name: right_ankle_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 206
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
          P_control: 70
          D_control: 7
          direction: 1


  - Name: waist_yaw
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 4
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: ***********
    Port: 15004
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      # Motor_Heart_Beat: 10 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950

      P_control: 300
      D_control: 30
      direction: 1


  - Name: left_shoulder_pitch
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 51
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: ************
    Port: 15051
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      Motor_Heart_Beat: 0 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 20000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 20000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950
      
      P_control: 300
      D_control: 30
      direction: 1

    child:
      - Name: left_shoulder_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 52
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: ************
        Port: 15052
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      
          P_control: 300
          D_control: 30
          direction: 1

      - Name: left_elbow_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 54
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      
          P_control: 300
          D_control: 30
          direction: 1

  - Name: right_shoulder_pitch
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 101
    CH: Head_Flag_CH0 
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15101
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      Motor_Heart_Beat: 0 #X10ms
      # Motor_CMD_I_To_Force_K: 1.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 20000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 20000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950
      
      P_control: 300
      D_control: 30
      direction: -1

    child:
      - Name: right_shoulder_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 102
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_ETH_OR_CAN
        IP: *************
        Port: 15102
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      
          P_control: 300
          D_control: 30
          direction: 1

      - Name: right_elbow_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 104
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          Motor_Heart_Beat: 0 #X10ms
          # Motor_CMD_I_To_Force_K: 1.0

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      
          P_control: 300
          D_control: 30
          direction: -1

