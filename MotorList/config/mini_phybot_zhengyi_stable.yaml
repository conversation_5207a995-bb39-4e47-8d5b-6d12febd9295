DriverVersion: 0.0.0

Name: Robot
PC_IP: *************
Broadcast_IP: *************

Base_Node:
  - Name: Main_Control_Board
    DeviceType: Main_board_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 100
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: ************
    Port: 15100
    Up_Data_Speed: 1000

    child:
      #CH2
      - Name: left_shoulder_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 51
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      - Name: left_shoulder_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 52
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950

      - Name: left_elbow_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 54
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      # - Name: left_elbow_yaw
      #   DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
      #   Id: 55
      #   CH: Head_Flag_Switch_CH2
      #   IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
      #   Up_Data_Speed: 100

      #   DeviceConfig:
      #     Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      #     Motor_EN_In_Init: true
      #     Set_Zero_In_Init: false
      #     Set_Config_In_Init: true
      #     Set_Pid_Config_In_Init: false
      #     # Zero
      #     Motor_Set_Zero_Offest: 0.0

      #     # Config
      #     Motor_Mod: Motor_MIT_Control
      #     # Motor_Heart_Beat: 10 #X10ms
      #     # Motor_CMD_I_To_Force_K: 1.0

      #     # Pid_Config
      #     Electricity_D_P: 100000
      #     Electricity_D_I: 20000
      #     Electricity_D_OUT_MAX: 0.950
      #     Electricity_D_Integration_MAX: 0.950
      #     Electricity_Q_P: 100000
      #     Electricity_Q_I: 20000
      #     Electricity_Q_OUT_MAX: 0.950
      #     Electricity_Q_Integration_MAX: 0.950

      #     Speed_P: 250000
      #     Speed_I: 500
      #     Speed_D: 0
      #     Speed_OUT_MAX: 0.950
      #     Speed_Integration_MAX: 0.950

      #     Post_Speed_Limit: 20.0
      #     Post_P: 100000
      #     Post_I: 0
      #     Post_OUT_MAX: 0.950
      #     Post_Integration_MAX: 0.950
      
      #CH1
      - Name: right_shoulder_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 101
        CH: Head_Flag_Switch_CH1
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      - Name: right_shoulder_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 102
        CH: Head_Flag_Switch_CH1
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950

      - Name: right_elbow_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 104
        CH: Head_Flag_Switch_CH1
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      # - Name: right_elbow_yaw
      #   DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
      #   Id: 105
      #   CH: Head_Flag_Switch_CH1
      #   IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
      #   Up_Data_Speed: 100

      #   DeviceConfig:
      #     Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      #     Motor_EN_In_Init: true
      #     Set_Zero_In_Init: false
      #     Set_Config_In_Init: true
      #     Set_Pid_Config_In_Init: false
      #     # Zero
      #     Motor_Set_Zero_Offest: 0.0

      #     # Config
      #     Motor_Mod: Motor_MIT_Control
      #     # Motor_Heart_Beat: 10 #X10ms
      #     # Motor_CMD_I_To_Force_K: 1.0

      #     # Pid_Config
      #     Electricity_D_P: 100000
      #     Electricity_D_I: 20000
      #     Electricity_D_OUT_MAX: 0.950
      #     Electricity_D_Integration_MAX: 0.950
      #     Electricity_Q_P: 100000
      #     Electricity_Q_I: 20000
      #     Electricity_Q_OUT_MAX: 0.950
      #     Electricity_Q_Integration_MAX: 0.950

      #     Speed_P: 250000
      #     Speed_I: 500
      #     Speed_D: 0
      #     Speed_OUT_MAX: 0.950
      #     Speed_Integration_MAX: 0.950

      #     Post_Speed_Limit: 20.0
      #     Post_P: 100000
      #     Post_I: 0
      #     Post_OUT_MAX: 0.950
      #     Post_Integration_MAX: 0.950
     
      #CH2
      - Name: waist_yaw
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 4
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      
      - Name: neck_yaw
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 3
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 100

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          DI_I_MAX: 10.0
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.4

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
     
      # - Name: neck_pitch
      #   DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
      #   Id: 2
      #   CH: Head_Flag_Switch_CH2
      #   IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
      #   Up_Data_Speed: 100

      #   DeviceConfig:
      #     Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      #     Motor_EN_In_Init: true
      #     Set_Zero_In_Init: false
      #     Set_Config_In_Init: true
      #     Set_Pid_Config_In_Init: false
      #     # Zero
      #     Motor_Set_Zero_Offest: 0.0

      #     # Config
      #     # DI_I_MAX: 10.0
      #     Motor_Mod: Motor_MIT_Control
      #     # Motor_Heart_Beat: 10 #X10ms
      #     # Motor_CMD_I_To_Force_K: 1.0

      #     # Pid_Config
      #     Electricity_D_P: 100000
      #     Electricity_D_I: 20000
      #     Electricity_D_OUT_MAX: 0.950
      #     Electricity_D_Integration_MAX: 0.950
      #     Electricity_Q_P: 100000
      #     Electricity_Q_I: 20000
      #     Electricity_Q_OUT_MAX: 0.950
      #     Electricity_Q_Integration_MAX: 0.950

      #     Speed_P: 250000
      #     Speed_I: 500
      #     Speed_D: 0
      #     Speed_OUT_MAX: 0.950
      #     Speed_Integration_MAX: 0.950

      #     Post_Speed_Limit: 20.0
      #     Post_P: 100000
      #     Post_I: 0
      #     Post_OUT_MAX: 0.950
      #     Post_Integration_MAX: 0.950
 
  - Name: left_hip_pitch
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 201
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15151
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      # Motor_Heart_Beat: 10 #X10ms
      Motor_CMD_I_To_Force_K: 0.2
      DI_I_MAX: 70.0

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950

    child: 
      - Name: left_hip_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 152
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: true
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.29

          # Pid_Config
          Electricity_D_P: 500000
          Electricity_D_I: 10000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 500000
          Electricity_Q_I: 10000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

      - Name: left_hip_yaw
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 153
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      - Name: left_knee
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 204
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: true
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.29

          # Pid_Config
          Electricity_D_P: 500000
          Electricity_D_I: 10000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 500000
          Electricity_Q_I: 10000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950
      - Name: left_ankle_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 155
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      - Name: left_ankle_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 156
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          DI_I_MAX: 10.0
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.4

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
 
  - Name: right_hip_pitch
    DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
    Id: 151
    CH: Head_Flag_CH0
    IO_Mod: Main_CMD_IO_Mod_ETH_To_CAN
    IP: *************
    Port: 15201
    Up_Data_Speed: 1000

    DeviceConfig:
      Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
      Motor_EN_In_Init: true
      Set_Zero_In_Init: false
      Set_Config_In_Init: true
      Set_Pid_Config_In_Init: false
      # Zero
      Motor_Set_Zero_Offest: 0.0

      # Config
      Motor_Mod: Motor_MIT_Control
      # Motor_Heart_Beat: 10 #X10ms
      Motor_CMD_I_To_Force_K: 0.20

      # Pid_Config
      Electricity_D_P: 100000
      Electricity_D_I: 10000
      Electricity_D_OUT_MAX: 0.950
      Electricity_D_Integration_MAX: 0.950
      Electricity_Q_P: 100000
      Electricity_Q_I: 10000
      Electricity_Q_OUT_MAX: 0.950
      Electricity_Q_Integration_MAX: 0.950

      Speed_P: 250000
      Speed_I: 500
      Speed_D: 0
      Speed_OUT_MAX: 0.950
      Speed_Integration_MAX: 0.950

      Post_Speed_Limit: 20.0
      Post_P: 100000
      Post_I: 0
      Post_OUT_MAX: 0.950
      Post_Integration_MAX: 0.950

    child: 
      - Name: right_hip_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 202
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: true
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.29

          # Pid_Config
          Electricity_D_P: 500000
          Electricity_D_I: 10000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 500000
          Electricity_Q_I: 10000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

      - Name: right_hip_yaw
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 203
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      - Name: right_knee
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 154
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: true
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.29

          # Pid_Config
          Electricity_D_P: 500000
          Electricity_D_I: 10000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 500000
          Electricity_Q_I: 10000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950
      - Name: right_ankle_pitch
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 205
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.16

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
      - Name: right_ankle_roll
        DeviceType: Motor_Device # Motor_Device IMU_Device Key_Device Main_board_Device Custom_Device
        Id: 206
        CH: Head_Flag_Switch_CH2
        IO_Mod: Main_CMD_IO_Mod_CAN_ONLY
        Up_Data_Speed: 1000

        DeviceConfig:
          Save_All_Config: false #不可常true 仅保存参数的时候开一次 否则快速损耗flash寿命
          Motor_EN_In_Init: true
          Set_Zero_In_Init: false
          Set_Config_In_Init: true
          Set_Pid_Config_In_Init: false
          # Zero
          Motor_Set_Zero_Offest: 0.0

          # Config
          DI_I_MAX: 10.0
          Motor_Mod: Motor_MIT_Control
          # Motor_Heart_Beat: 10 #X10ms
          Motor_CMD_I_To_Force_K: 0.4

          # Pid_Config
          Electricity_D_P: 100000
          Electricity_D_I: 20000
          Electricity_D_OUT_MAX: 0.950
          Electricity_D_Integration_MAX: 0.950
          Electricity_Q_P: 100000
          Electricity_Q_I: 20000
          Electricity_Q_OUT_MAX: 0.950
          Electricity_Q_Integration_MAX: 0.950

          Speed_P: 250000
          Speed_I: 500
          Speed_D: 0
          Speed_OUT_MAX: 0.950
          Speed_Integration_MAX: 0.950

          Post_Speed_Limit: 20.0
          Post_P: 100000
          Post_I: 0
          Post_OUT_MAX: 0.950
          Post_Integration_MAX: 0.950
            
# std::map<std::string, u32> IO_Mod_Map = {
# {"Main_CMD_IO_Mod_ETH_To_CAN", Main_CMD_IO_Mod_ETH_To_CAN},
# {"Main_CMD_IO_Mod_ETH_OR_CAN", Main_CMD_IO_Mod_ETH_OR_CAN},
# {"Main_CMD_IO_Mod_CAN_ONLY", Main_CMD_IO_Mod_CAN_ONLY}};


# # enum Motor_CONTROL_MOD
# # {
# # Motor_Current_Control = 10,
# # Motor_MIT_Control = 11,
# # Motor_Pos_Control = 12,
# # Motor_Force_Control = 13,
# # Motor_Speed_Control = 14,

# # Motor_MOD_END = 100,
# # };