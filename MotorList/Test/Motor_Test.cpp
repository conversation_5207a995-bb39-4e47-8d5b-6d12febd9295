#include"../include/MotorList.hpp"
#include"../include/realtime_controller.hpp"
#include <fstream>  // 追加: 用于写入文件

int main()
{
    int motorNum = 21;
    std::vector<std::string> motor_name_list(motorNum);

    // motor_name_list[0]  = "waist_yaw";
    // motor_name_list[1]  = "waist_roll";

    motor_name_list[0]  = "left_shoulder_pitch";
    motor_name_list[1]  = "left_shoulder_roll";
    motor_name_list[2]  = "left_shoulder_yaw";
    motor_name_list[3]  = "left_elbow_pitch";
    motor_name_list[4]  = "left_elbow_yaw";

    motor_name_list[5]  = "right_shoulder_pitch";
    motor_name_list[6]  = "right_shoulder_roll";
    motor_name_list[7]  = "right_shoulder_yaw";
    // motor_name_list[10] = "right_elbow_pitch";
    motor_name_list[8] = "right_elbow_yaw";

    motor_name_list[9] = "left_hip_pitch";
    motor_name_list[10] = "left_hip_roll";
    motor_name_list[11] = "left_hip_yaw";
    // motor_name_list[15] = "left_knee";
    motor_name_list[12] = "left_ankle_pitch";
    motor_name_list[13] = "left_ankle_roll";
    motor_name_list[14] = "left_toe";

    motor_name_list[15] = "right_hip_pitch";
    motor_name_list[16] = "right_hip_roll";
    motor_name_list[17] = "right_hip_yaw";
    // motor_name_list[22] = "right_knee";
    motor_name_list[18] = "right_ankle_pitch";
    motor_name_list[19] = "right_ankle_roll";
    motor_name_list[20] = "right_toe";

    
    std::string config_yaml = "./config/Motorlist.yaml";
    MotorList motorlist;

    period_info P_info;

    motorlist.Init(config_yaml, motorNum, motor_name_list);
    std::cout << "Motorlist init complete! " << std::endl;
    Eigen::VectorXd pos,vel,tor = Eigen::VectorXd::Zero(motorNum);
    
    periodic_task_init(&P_info);

    ofstream file("/home/<USER>/Robotic/PhybotSofware/MotorList/data/motorlist.csv");
    for(int i = 0; i < motor_name_list.size(); i++){
        file << motor_name_list[i];
        if(i < motor_name_list.size() - 1 )
            file << "," ;
    }
    file << endl;
    

    while(1){
        motorlist.GetStates(pos, vel, tor);
        pos = motorlist.SmoothToZero(pos);
        motorlist.SetCommands(pos);
        wait_rest_of_period(&P_info);

        for(int i = 0; i < motor_name_list.size(); i++)
        {
            std::cout << motor_name_list[i];
            if(i < motor_name_list.size() - 1 )
                std::cout << "," ;
        }
        std::cout << std::endl;

        for(int i = 0 ; i < pos.size();i++){
            std::cout << pos[i];
            if(i < pos.size() - 1 )
                std::cout << "," ;
        }
        std::cout << std::endl;

        for(int i = 0; i < pos.size(); i++)
        {
            file << pos[i];
            if(i < pos.size() - 1 )
                file << "," ;
        }
        file << endl;


        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

}
