#include "../include/MotorList.hpp"


MotorList::MotorList(){}

MotorList::~MotorList(){}

void MotorList::Init(std::string path, int num, std::vector<std::string>& motor_name, const DataPackage &DataPackage)
{
    MotorNum = num;
    motor.reserve(MotorNum);
    devices.reserve(MotorNum);
    qpos_cur.resize(MotorNum);
    qvel_cur.resize(MotorNum);
    qtor_cur.resize(MotorNum);


    lowpass_.resize(MotorNum);

    for (int i = 0; i < MotorNum; i++) {
        lowpass_[i] = new LowPassFilter(10.0, 0.707, DataPackage.control_period, 1);
    }


    // for (int i = 0; i < MotorNum; i++) {
    //     lowpass_[i] = new LowPassFilter(10.0, 0.707, 0.01, 1);
    // }


    motor_lowpass.resize(MotorNum);

    Robot_Ctrl->Add_Device_Type("Main_board_Device", Main_board_Device_Init, Main_board_Device_CallBack_F, Main_board_Device_Delete_F);

    std::string path_1 = "/home/<USER>/文档/PhybotSofware_small/MotorList/config/mini_phybot_zhengyi_stable.yaml";
    if (Robot_Ctrl->Init_TOP(path_1) != 0)
    {
        cerr << "Init_ERR" << endl;
    }

    for (int i = 0; i < MotorNum; i++) 
    {
    std::shared_ptr<Device_Struct> dev = Robot_Ctrl->Get_Device_For_Name(motor_name[i]);
    devices.push_back(dev);
    motor.push_back(static_cast<Motor*>(Robot_Ctrl->Get_Control_Class(dev)));
    }


    for(int i = 0; i < MotorNum; i++){
        motor[i]->Send_MIT_PD_Control_Data(devices[i], 0, 0, 0, 0, 0);
    }
    Robot_Ctrl->Send_Buff_Data();
    std::this_thread::sleep_for(std::chrono::milliseconds(5));

    for(int i = 0; i < MotorNum; i++){
        motor[i]->Send_MIT_PD_Control_Data(devices[i], 0, 0, 0, 0, 0);
    }
    Robot_Ctrl->Send_Buff_Data();
    std::this_thread::sleep_for(std::chrono::milliseconds(5));

    for(int i = 0; i < MotorNum; i++){
        motor[i]->Send_MIT_PD_Control_Data(devices[i], 0, 0, 0, 0, 0);
    }
    Robot_Ctrl->Send_Buff_Data();
    std::this_thread::sleep_for(std::chrono::milliseconds(5));





}

void MotorList::Enable(){} //在yaml文件中直接定义


//延时函数写在main函数中
void MotorList::SetCommands(Eigen::VectorXd pos_cmd, Eigen::VectorXd vel_cmd, Eigen::VectorXd tor_cmd, Eigen::VectorXd p_gain_cmd, Eigen::VectorXd d_gain_cmd, Eigen::VectorXd dir)
{
    for(int i = 0; i < MotorNum ; i++)
    {
        motor[i]->Send_MIT_PD_Control_Data(devices[i], pos_cmd[i]*dir[i], vel_cmd[i]*dir[i], tor_cmd[i]*dir[i], p_gain_cmd[i], d_gain_cmd[i]);
    }
    Robot_Ctrl->Send_Buff_Data();
}

void MotorList::SetCommands(Eigen::VectorXd& pos_cmd, Eigen::VectorXd& vel_cmd, Eigen::VectorXd& tor_cmd, Eigen::VectorXd p_gain_cmd, Eigen::VectorXd d_gain_cmd, Eigen::VectorXd dir, DataPackage &data)
{


    // Eigen::VectorXd q(1);
    // for (int i = 0; i < MotorNum; i++) {
    //     q(0) = data.motor_Pos_desire(i);
    //     motor_lowpass(i) = lowpass_[i]->mFilter(q)(0);
    // }
    // data.motor_Pos_desire = motor_lowpass;


    pos_cmd = data.motor_Pos_desire;
    vel_cmd = data.motor_Vel_desire;
    tor_cmd = data.motor_Torque_desire;

    Eigen::VectorXd motor_torque_coff = Eigen::VectorXd::Zero(24);

    // motor_torque_coff<<1,1,1,0.5,1,1,
    //                     1,1,1,0.5,1,1,
    //                     1,1,
    //                     1,1,1,1,1,
    //                     1,1,1,1,1;



    // pos_cmd.setZero();
    // vel_cmd.setZero();
    // tor_cmd.setZero();
    
    for(int i = 0; i < MotorNum ; i++)
    {
        motor[i]->Send_MIT_PD_Control_Data(devices[i], pos_cmd[i]*dir[i], 1*vel_cmd[i]*dir[i], 1*tor_cmd[i]*dir[i], p_gain_cmd[i], d_gain_cmd[i]);
    }
    Robot_Ctrl->Send_Buff_Data();
}

//延时函数写在main函数中
void MotorList::SetCommands(Eigen::VectorXd pos_cmd)
{
    for(int i = 0; i < MotorNum ; i++)
    {
        motor[i]->Send_MIT_PD_Control_Data(devices[i], pos_cmd[i], 0, 0, 2, 1);
    }
    Robot_Ctrl->Send_Buff_Data();
}

void MotorList::GetStates(Eigen::VectorXd &pos, Eigen::VectorXd &vel, Eigen::VectorXd &tor, Eigen::VectorXd dir)
{
    float P ,V ,F;
    for(int i = 0; i < MotorNum; i++){
        motor[i]->Get_Motor_FB_Data(devices[i], &P, &V, &F);
        pos[i] = P * dir[i];
        vel[i] = V * dir[i];
        tor[i] = F * dir[i];
    }

}

void MotorList::GetStates(Eigen::VectorXd &pos, Eigen::VectorXd &vel, Eigen::VectorXd &tor, Eigen::VectorXd dir, DataPackage &data, int filtertype)
{

    float P ,V ,F;
    for(int i = 0; i < MotorNum; i++){
        motor[i]->Get_Motor_FB_Data(devices[i], &P, &V, &F);
        // std::cout << "P" <<P<< std::endl;

        pos[i] = P * dir[i];
        vel[i] = V * dir[i];
        tor[i] = F * dir[i];
    }

    switch (filtertype) {
        case (0): {
            break;
        }
        case (1): {

            Eigen::VectorXd q(1);
            for (int i = 0; i < MotorNum; i++) {
                q(0) = vel(i);
                motor_lowpass(i) = lowpass_[i]->mFilter(q)(0);
            }
            vel = motor_lowpass;
            break;
        }
        default: {
            std::cout << "no such filter!" << std::endl;
            break;
        }
    }

    data.motor_pos = pos;
    data.motor_vel = vel;
    data.motor_torque = tor;

}

Eigen::VectorXd MotorList::SmoothToZero(Eigen::VectorXd initial_pos) {

    Eigen::VectorXd a0 = initial_pos;
    Eigen::VectorXd a1 = Eigen::VectorXd::Zero(MotorNum);
    Eigen::VectorXd a2 = (-3.0 / (duration * duration)) * initial_pos;
    Eigen::VectorXd a3 = (2.0 / (duration * duration * duration)) * initial_pos;

    Eigen::VectorXd positions = a0 + a1 * dt + a2 * dt * dt + a3 * dt * dt * dt;

    if(dt < duration)
        dt += 0.002;
    

    return positions;
}

void MotorList::Disable(){} //在yaml文件中直接定义

Eigen::MatrixXd readCSV(const std::string& filename, bool skipHeader) {
    std::ifstream file(filename);
    std::string line;
    std::vector<std::vector<double>> data;

    if (!file.is_open()) {
        throw std::runtime_error("Failed to open file: " + filename);
    }

    // 可选跳过表头
    if (skipHeader && std::getline(file, line)) {
        // 表头已跳过
    }

    while (std::getline(file, line)) {
        std::stringstream ss(line);
        std::string value;
        std::vector<double> row;

        while (std::getline(ss, value, ',')) {
            try {
                row.push_back(std::stod(value));
            } catch (...) {
                row.push_back(0.0);  // 可以自定义处理方式
            }
        }

        if (!row.empty())
            data.push_back(row);
    }

    if (data.empty()) {
        throw std::runtime_error("No data found in file: " + filename);
    }

    int rows = data.size();
    int cols = data[0].size();
    Eigen::MatrixXd mat(rows, cols);

    for (int i = 0; i < rows; ++i)
        mat.row(i) = Eigen::VectorXd::Map(&data[i][0], cols);

    return mat;
}

void interpolateJointPosVel(
    const Eigen::MatrixXd& arm_joint_pos, // 行：时间，列：关节
    double t_arm, double T_arm_total,     // 当前时间和总时长
    Eigen::VectorXd& arm_JointPos_desire,  
    Eigen::VectorXd& arm_JointVel_desire
) {
    int time_steps = arm_joint_pos.rows(); // 时间步数
    int joint_num = arm_joint_pos.cols();  // 关节数

    // 检查维度一致性
    assert(arm_JointPos_desire.size() == joint_num && "arm_JointPos_desire size mismatch");
    assert(arm_JointVel_desire.size() == joint_num && "arm_JointVel_desire size mismatch");

    double t_norm = t_arm / T_arm_total;

    double index_f = t_norm * (time_steps - 1);
    int idx = static_cast<int>(std::floor(index_f));
    double frac = index_f - idx;

    // 边界处理
    if (idx >= time_steps - 1) {
        idx = time_steps - 2;
        frac = 1.0;
    } else if (idx < 0) {
        idx = 0;
        frac = 0.0;
    }

    double dt_steps = T_arm_total / (time_steps - 1); // 单个时间步长

    for (int j = 0; j < joint_num; ++j) {
        double p0 = arm_joint_pos(idx, j);
        double p1 = arm_joint_pos(idx + 1, j);

        // 插值位置
        double pos = (1 - frac) * p0 + frac * p1;
        arm_JointPos_desire(j) = pos;

        // 速度 = 差分 / dt
        double vel = (p1 - p0) / dt_steps;

        // 边界速度设为 0
        if (t_norm <= 0.0 || t_norm >= 1.0) {
            vel = 0.0;
        }

        arm_JointVel_desire(j) = vel;
    }
}

void parseNode(const YAML::Node& node,
               std::vector<std::string>& names,
               std::vector<double>& P_values,
               std::vector<double>& D_values,
               std::vector<double>& dir_values)
{
    std::string name = node["Name"].as<std::string>();

    // std::cout << "-----" << std::endl;
    // std::cout << "Parsing node: " << name << std::endl;
    // std::cout << node << std::endl;

    if (name == "Main_Control_Board") return; // 跳过主板

    names.push_back(name);

    double P = 0.0;
    double D = 0.0;
    double dir = 0.0;
    if (node["DeviceConfig"] && node["DeviceConfig"]["P_control"]) {
        P = node["DeviceConfig"]["P_control"].as<double>();
        D = node["DeviceConfig"]["D_control"].as<double>();
        dir = node["DeviceConfig"]["direction"].as<double>();
    }
    P_values.push_back(P);
    D_values.push_back(D);
    dir_values.push_back(dir);

    // 递归读取子节点
    if (node["child"] && node["child"].IsSequence()) {
        for (const auto& child_node : node["child"]) {
            parseNode(child_node, names, P_values, D_values, dir_values);
        }
    }
}

void loadMotorConfig(const std::string& yaml_path,
                     std::vector<std::string>& motor_name_list,
                     Eigen::VectorXd& P_control_vector,
                     Eigen::VectorXd& D_control_vector,
                     Eigen::VectorXd& dir_control_vector)
{
    YAML::Node config = YAML::LoadFile(yaml_path);
    const YAML::Node& base_nodes = config["Base_Node"];

    std::vector<std::string> names;
    std::vector<double> P_values, D_values, dir_values;

    if (!base_nodes || !base_nodes.IsSequence()) {
        std::cerr << "'Base_Node' not found or is not a sequence!" << std::endl;
        return;
    }

    for (const auto& node : base_nodes) {
        parseNode(node, names, P_values, D_values, dir_values);
    }

    int motorNum = names.size();
    motor_name_list = names;
    P_control_vector = Eigen::VectorXd::Zero(motorNum);
    D_control_vector = Eigen::VectorXd::Zero(motorNum);
    dir_control_vector = Eigen::VectorXd::Zero(motorNum);

    for (int i = 0; i < motorNum; ++i) {
        P_control_vector(i) = P_values[i];
        D_control_vector(i) = D_values[i];
        dir_control_vector(i) = dir_values[i];
    }

    // std::cout << "Loaded motor count: " << motorNum << std::endl;
}

void ThirdpolyVector(
    const Eigen::VectorXd& p0, const Eigen::VectorXd& p0_dot,
    const Eigen::VectorXd& p1, const Eigen::VectorXd& p1_dot,
    double totalTime, double currentTime,
    Eigen::VectorXd& pd, Eigen::VectorXd& pd_dot
) {
    int dim = p0.size();

    if (p0_dot.size() != dim || p1.size() != dim || p1_dot.size() != dim ||
        pd.size() != dim || pd_dot.size() != dim) {
        throw std::runtime_error("ThirdpolyVector: Vector size mismatch.");
    }

    if (currentTime < 0) {
        pd = p0;
        pd_dot.setZero();
    } else if (currentTime <= totalTime) {
        for (int i = 0; i < dim; ++i) {
            double a0 = p0(i);
            double a1 = p0_dot(i);
            double m = p1(i) - p0(i) - p0_dot(i) * totalTime;
            double n = p1_dot(i) - p0_dot(i);
            double a2 = 3 * m / (totalTime * totalTime) - n / totalTime;
            double a3 = -2 * m / (totalTime * totalTime * totalTime) + n / (totalTime * totalTime);

            pd(i) = a3 * std::pow(currentTime, 3) + a2 * std::pow(currentTime, 2) + a1 * currentTime + a0;
            pd_dot(i) = 3 * a3 * currentTime * currentTime + 2 * a2 * currentTime + a1;
        }
    } else {
        pd = p1;
        pd_dot.setZero();
    }
}
