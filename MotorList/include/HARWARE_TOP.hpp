#ifndef HARWARE_TOP_H_
#define HARWARE_TOP_H_
#include "syst.hpp"
#include <yaml-cpp/yaml.h>

using namespace std;

typedef struct
{
    shared_ptr<string> Name;
    shared_ptr<string> DeviceType;
    u8 Head_Flag_CHX;
    uint8_t None0[108];

    void *Device_Private_Class;
    int (*Device_Private_CallBack)(void *, volatile u8 *);
    void (*DeviceType_Delete)(void *);

    uint8_t None1[68];

    u32 IS_Online;
    u32 Have_New_Msg;
} Device_Struct;

class Robot_Hardware
{
public:
    Robot_Hardware(void);
    ~Robot_Hardware(void);

    int Add_Device_Type(string Device_Type_Name, int (Init_F)(shared_ptr<Device_Struct>, YAML::Node *), int (CallBack_F)(void *, volatile u8 *), void (Delete_F)(void *));
    int Init_TOP(string File);
    int OTA_GO(string folder);
    shared_ptr<Device_Struct> Get_Device_For_Name(string Name);
    void *Get_Control_Class(shared_ptr<Device_Struct> Device_P);
    int Send_Buff_Data(void);
    int REBOOT_F(shared_ptr<Device_Struct> Device_P);

    int UDP_Broadcast_Send(u8 *Data, shared_ptr<Device_Struct> Device);
    int UDP_Nomal_Send(u8 *Data, shared_ptr<Device_Struct> Device);
    void CAN_Msg_To_UDP_Msg(shared_ptr<Device_Struct> Device, u8 *CAN_Msg);
    int Wait_FB(shared_ptr<Device_Struct> Device, int Msg_Num, int time);

private:
    class Robot;
    Robot *One_Robot;
};

#endif
