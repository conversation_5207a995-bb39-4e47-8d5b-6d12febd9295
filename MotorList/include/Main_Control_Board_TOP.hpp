#ifndef Main_Control_Board_TOP_H_
#define Main_Control_Board_TOP_H_
#include "HARWARE_TOP.hpp"

using namespace std;

#define Main_board_Device_Init  [](shared_ptr<Device_Struct> Device, YAML::Node *Node) -> int\
                                {\
                                    Main_B *One_Main_B = new Main_B();\
                                    Device->Device_Private_Class = (void *)One_Main_B;\
                                    if (Node != nullptr)\
                                        return One_Main_B->Get_Main_B_Device_Data_From_Yaml_And_Init(Device, *Node);\
                                    else\
                                        return 0;\
                                }\

#define Main_board_Device_CallBack_F    [](void *Device_Private_Class, volatile u8 *Msg) -> int\
                                        {\
                                            return ((Main_B *)Device_Private_Class)->Main_B_Top_Frame_Analyze(Msg);\
                                        }\

#define Main_board_Device_Delete_F  [](void *Device_Private_Class)\
                                    {\
                                        delete ((Main_B *)Device_Private_Class);\
                                    }\

typedef struct
{
    float ACC[3];
    float GYR[3];
    float ROLL;
    float PITCH;
    float YAW;
} IMU_O;

class Main_B : private Robot_Hardware
{
public:
    typedef struct
    {
        IMU_O IMU_O_1;
    } Main_B_Private_Struct;

    shared_ptr<Main_B_Private_Struct> Main_B_Private_Data = make_shared<Main_B_Private_Struct>();

    int IMU_EN(shared_ptr<Device_Struct> Device_P, bool EN);
    int Get_IMU_1_Data(shared_ptr<Device_Struct> Device_P, IMU_O *data);

    int Get_Main_B_Device_Data_From_Yaml_And_Init(shared_ptr<Device_Struct> Device, YAML::Node One_Node);
    int Main_B_Top_Frame_Analyze(volatile u8 *Can_Frame);
};


#endif
