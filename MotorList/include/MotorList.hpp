#ifndef MOTORLIST_H_
#define MOTORLIST_H_
#include <iostream>
#include <fstream>
#include <eigen3/Eigen/Dense>
#include <cmath>
#include <yaml-cpp/yaml.h> //使用yaml输入电机相关信息
#include <vector>
#include <string>
#include "syst.hpp"
#include "HARWARE_TOP.hpp"
#include "Motor_TOP.hpp"
#include "Main_Control_Board_TOP.hpp"
#include"DataPackage/include/DataPackage.h"
#include "LowPassFilter/include/LowPassFilter.h"


class MotorList{
    public:
    MotorList();
    ~MotorList();
    void Init(std::string path, int num, std::vector<std::string>& motor_name, const DataPackage &DataPackage);
    void Enable();
    void SetCommands(Eigen::VectorXd pos_cmd, Eigen::VectorXd vel_cmd, Eigen::VectorXd tor_cmd, Eigen::VectorXd p_gain_cmd, Eigen::VectorXd d_gain_cmd, Eigen::VectorXd dir);
    void SetCommands(Eigen::VectorXd& pos_cmd, Eigen::VectorXd& vel_cmd, Eigen::VectorXd& tor_cmd, Eigen::VectorXd p_gain_cmd, Eigen::VectorXd d_gain_cmd, Eigen::VectorXd dir, DataPackage &data);
    void SetCommands(Eigen::VectorXd pos_cmd);
    void GetStates(Eigen::VectorXd &pos, Eigen::VectorXd &vel, Eigen::VectorXd &tor, Eigen::VectorXd dir);
    void GetStates(Eigen::VectorXd &pos, Eigen::VectorXd &vel, Eigen::VectorXd &tor, Eigen::VectorXd dir, DataPackage &data, int filtertype);
    Eigen::VectorXd SmoothToZero(Eigen::VectorXd initial_pos);
    void Disable();

    int MotorNum = 0;
    Robot_Hardware *Robot_Ctrl = new Robot_Hardware();
    std::vector<std::shared_ptr<Device_Struct>> devices;
    std::vector<Motor*> motor;
    Eigen::VectorXd qpos_cur;
    Eigen::VectorXd qvel_cur;
    Eigen::VectorXd qtor_cur;
    double duration =1.0;
    double dt =0.002;
    std::vector<LowPassFilter *> lowpass_;
    Eigen::VectorXd motor_lowpass;
};

Eigen::MatrixXd readCSV(const std::string& filename, bool skipHeader);
void interpolateJointPosVel(
    const Eigen::MatrixXd& arm_joint_pos, // 行：时间，列：关节
    double t_arm, double T_arm_total,     // 当前时间和总时长
    Eigen::VectorXd& arm_JointPos_desire,  
    Eigen::VectorXd& arm_JointVel_desire
);

void loadMotorConfig(const std::string& yaml_path,
                     std::vector<std::string>& motor_name_list,
                     Eigen::VectorXd& P_control_vector,
                     Eigen::VectorXd& D_control_vector,
                     Eigen::VectorXd& dir_control_vector);

void parseNode(const YAML::Node& node,
               std::vector<std::string>& names,
               std::vector<double>& P_values,
               std::vector<double>& D_values,
               std::vector<double>& dir_values);

void ThirdpolyVector(
    const Eigen::VectorXd& p0, const Eigen::VectorXd& p0_dot,
    const Eigen::VectorXd& p1, const Eigen::VectorXd& p1_dot,
    double totalTime, double currentTime,
    Eigen::VectorXd& pd, Eigen::VectorXd& pd_dot);

#endif