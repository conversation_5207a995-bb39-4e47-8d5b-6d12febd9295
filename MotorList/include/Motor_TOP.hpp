#ifndef MOTOR_TOP_H_
#define MOTOR_TOP_H_
#include "HARWARE_TOP.hpp"

using namespace std;

class Motor
{
public:
    int Motor_EN(shared_ptr<Device_Struct> Device_P, int EN);
    int Set_Mod(shared_ptr<Device_Struct> Device_P, int Mod);
    int Set_Zero(shared_ptr<Device_Struct> Device_P, float offest);
    int Send_Current_Control_Data(shared_ptr<Device_Struct> Device_P, float Current_A);
    int Send_Force_Control_Data(shared_ptr<Device_Struct> Device_P, float Force_N);
    int Send_Speed_Control_Data(shared_ptr<Device_Struct> Device_P, float Speed_Rad_S, float Force_N, float Force_MAX_N);
    int Send_Pos_Control_Data(shared_ptr<Device_Struct> Device_P, float Rad, float Speed_Rad_S, float Force_N, float Speed_MAX_Rad_S, float Force_MAX_N);
    int Send_MIT_PD_Control_Data(shared_ptr<Device_Struct> Device_P, float Rad, float Speed_Rad_S, float Force_N, float P_N_Rad, float D_N_Rad_s);
    int Get_Motor_FB_Data(shared_ptr<Device_Struct> Device_P, float *P, float *V, float *F);
    int Get_Motor_FB_Data(shared_ptr<Device_Struct> Device_P, float *P, float *V, float *F,uint8_t *temp,bool *error);
};

#endif
