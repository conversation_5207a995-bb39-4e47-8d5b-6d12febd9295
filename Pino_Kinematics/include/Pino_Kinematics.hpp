#ifndef Pino_Kinematics_H_
#define Pino_Kinematics_H_
#include<iostream>
#include<fstream>
#include<math.h>
#include "DataPackage/include/DataPackage.h"
// #include "/home/<USER>/Documents/PhybotSofware/DataPackage/include/DataPackage.h"
#include<Eigen/Dense>
#include<sys/time.h>
#include "pinocchio/parsers/urdf.hpp"
#include "pinocchio/algorithm/jacobian.hpp"
#include "pinocchio/algorithm/kinematics.hpp"
#include "pinocchio/algorithm/frames.hpp"
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/rnea.hpp"
#include "pinocchio/algorithm/crba.hpp"
#include "pinocchio/algorithm/centroidal.hpp"
#include "pinocchio/algorithm/center-of-mass.hpp"
#include "pinocchio/algorithm/aba.hpp"
#include "yaml-cpp/yaml.h"

class Pino_Kinematics {

public:
    std::string urdf_path;
    std::string file_path;
    pinocchio::JointIndex leg_l1_joint, leg_l2_joint, leg_l3_joint, leg_l4_joint, leg_l5_joint, leg_l6_joint;
    pinocchio::JointIndex leg_r1_joint, leg_r2_joint, leg_r3_joint, leg_r4_joint, leg_r5_joint, leg_r6_joint;    
    pinocchio::Model model_biped_fixed;
    pinocchio::Data data_biped_fixed_zero;
    pinocchio::Data data_biped_fixed;
    int model_fixed_nv;
    Eigen::Matrix<double,6,-1> J_r, J_l;    
    Eigen::VectorXd q_fixed;
    Eigen::Matrix4d feet_r_T_W_desire, feet_l_T_W_desire;
    Eigen::VectorXd q_fixed_desire, q_dot_fixed_desire;
    Eigen::Matrix4d base_T_actual;
    Eigen::Matrix4d base_T_desire;

    Eigen::VectorXd generalized_q_actual;
    Eigen::VectorXd generalized_q_dot_actual;
    Eigen::Vector3d base_Pos_desire, base_LinVel_desire;
    Eigen::Vector3d base_Pos_actual, base_LinVel_actual;
    Eigen::Vector3d feet_r_Pos_W_desire, feet_l_Pos_W_desire;
    Eigen::Vector3d feet_r_LinVel_W_desire, feet_l_LinVel_W_desire;

    Eigen::Matrix<double, 5, 1> arm_l_JointPos_desire, arm_r_JointPos_desire;
    Eigen::Matrix<double, 5, 1> arm_l_JointVel_desire, arm_r_JointVel_desire;
    
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_desired;
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_dot_desired;

    double feet_l_OmegaZ_W_desire, feet_r_OmegaZ_W_desire;
    double feet_l_EulerZ_W_desire, feet_r_EulerZ_W_desire;
    double base_EulerZ_desire, base_OmegaZ_desire;
    Eigen::Matrix<double, 3,1> global_angular_vel;   
    double feet_l_EulerY_W_desire, feet_r_EulerY_W_desire;
    double feet_l_OmegaY_W_desire, feet_r_OmegaY_W_desire;
    Eigen::Vector3d feet_r_EulerZYX_W_actual, feet_l_EulerZYX_W_actual;
    Eigen::Vector3d feet_r_EulerZYX_W_desire, feet_l_EulerZYX_W_desire;
    Eigen::Vector3d feet_r_OmegaZYX_W_desire, feet_l_OmegaZYX_W_desire;


    int IndexLegStart, IndexLegLength;
    int IndexArmStart, IndexArmLength;
    int IndexWaistStart, IndexWaistLength;

    pinocchio::Model model_pino;
    pinocchio::Data data_IK;
    Eigen::Vector3d feet_r_Pos_W_IK, feet_l_Pos_W_IK;

    double waist_pos_desire{0};

    Eigen::Matrix4d base_T_IK;

    Eigen::VectorXd theta_array_0_L;
    Eigen::VectorXd theta_array_0_R;

    Pino_Kinematics();

    Eigen::Matrix3d rotation_matrix(const std::string& axis, double angle);
    double calculate_signed_angle_2d(const Eigen::Vector2d& a, const Eigen::Vector2d& b);
    Eigen::VectorXd compute_joint_initial_angles(const std::string& leg_name);
    Eigen::VectorXd compute_joint_angles(const Eigen::Matrix4d& T_W_base, const Eigen::Matrix4d& T_W_1, const std::string& leg_name);

    void compute_dq_fixed_desire(State state);

    void compute_generalized_q_dq_desired(State state);

    void GetDataFromPackage(DataPackage &DataPackage);
    void SetDataToPackage(DataPackage &DataPackage);

    Eigen::Matrix<double, 3, 3> getRotationMatrixFromZyxEulerAngles(const Eigen::Matrix<double, 3, 1>& eulerAngles);
    Eigen::Matrix<double, 3, 1> getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles);



};

#endif