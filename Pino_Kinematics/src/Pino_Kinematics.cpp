#include "../../Pino_Kinematics/include/Pino_Kinematics.hpp"
#include <Eigen/Dense>
#include <cmath>
#include <vector>
#include <iostream>

Pino_Kinematics::Pino_Kinematics() {
    file_path = "../Pino_Kinematics/config/IK_FOOT.yaml";
    // file_path = "../config/IK_FOOT.yaml";
    YAML::Node config = YAML::LoadFile(file_path);

    // std::cout << "YAML content:\n" << YAML::Dump(config) << std::endl;

// try {
//     YAML::Node config = YAML::LoadFile(file_path);
//     if(!config["fixed_robot_path"]) {
//         throw std::runtime_error("Missing 'fixed_robot_path' in config file");
//     }
//     std::string robot_path = config["fixed_robot_path"].as<std::string>();
// } catch (const YAML::Exception& e) {
//     std::cerr << "YAML Error: " << e.what() << std::endl;
//     throw;
// }

    // pinocchio::JointModelFreeFlyer root_joint;
    pinocchio::urdf::buildModel(config["fixed_robot_path"].as<std::string>(),model_biped_fixed);
    data_biped_fixed=pinocchio::Data(model_biped_fixed);
    data_biped_fixed_zero=pinocchio::Data(model_biped_fixed);
    model_fixed_nv=model_biped_fixed.nv;

    Eigen::VectorXd q_fixed_zero = Eigen::VectorXd::Zero(model_biped_fixed.nq);
    pinocchio::forwardKinematics(model_biped_fixed, data_biped_fixed_zero, q_fixed_zero);
    pinocchio::updateFramePlacements(model_biped_fixed, data_biped_fixed_zero);

    leg_l1_joint=model_biped_fixed.getJointId("left_hip_pitch");
    leg_l2_joint=model_biped_fixed.getJointId("left_hip_roll");
    leg_l3_joint=model_biped_fixed.getJointId("left_hip_yaw");
    leg_l4_joint=model_biped_fixed.getJointId("left_knee");
    leg_l5_joint=model_biped_fixed.getJointId("left_ankle_pitch");
    leg_l6_joint=model_biped_fixed.getJointId("left_ankle_roll");
    leg_r1_joint=model_biped_fixed.getJointId("right_hip_pitch");
    leg_r2_joint=model_biped_fixed.getJointId("right_hip_roll");
    leg_r3_joint=model_biped_fixed.getJointId("right_hip_yaw");
    leg_r4_joint=model_biped_fixed.getJointId("right_knee");
    leg_r5_joint=model_biped_fixed.getJointId("right_ankle_pitch");
    leg_r6_joint=model_biped_fixed.getJointId("right_ankle_roll");

    generalized_q_desired.setZero(model_fixed_nv+6);
    generalized_q_dot_desired.setZero(model_fixed_nv+6);

    theta_array_0_L = Eigen::VectorXd(config["theta_array_0_L"].size());
    int idx = 0;
    for (const auto& value : config["theta_array_0_L"]) {
        theta_array_0_L(idx++) = value.as<double>();
    }

    theta_array_0_R = Eigen::VectorXd(config["theta_array_0_R"].size());
    idx = 0;
    for (const auto& value : config["theta_array_0_R"]) {
        theta_array_0_R(idx++) = value.as<double>();
    }


}

void Pino_Kinematics::compute_dq_fixed_desire(State state){
    
    q_fixed = generalized_q_actual.segment(6, model_fixed_nv);

    pinocchio::SE3 T_base_world(base_T_actual);

    Eigen::VectorXd foot_vel_desire(12);

    foot_vel_desire.setZero();

    if(state == State::STANDING){
        foot_vel_desire.segment<3>(0) = feet_l_LinVel_W_desire - base_LinVel_desire;
        foot_vel_desire.segment<3>(6) = feet_r_LinVel_W_desire - base_LinVel_desire;
    }else{
        foot_vel_desire.segment<3>(0) = feet_l_LinVel_W_desire - generalized_q_dot_actual.segment<3>(0);
        foot_vel_desire.segment<3>(6) = feet_r_LinVel_W_desire - generalized_q_dot_actual.segment<3>(0);
        foot_vel_desire[5] = feet_l_OmegaZ_W_desire - global_angular_vel[2];
        foot_vel_desire[11] = feet_r_OmegaZ_W_desire - global_angular_vel[2];
    }
    

    const double damp = 5e-3;

    Eigen::MatrixXd JL(6,model_fixed_nv);
    Eigen::MatrixXd JR(6,model_fixed_nv);
    Eigen::MatrixXd JCompact(12,model_fixed_nv);
    JL.setZero();
    JR.setZero();
    JCompact.setZero();

    pinocchio::JointIndex J_Idx_l, J_Idx_r;

    J_Idx_l = leg_l6_joint;
    J_Idx_r = leg_r6_joint;

    // std::cout << "code come to forwardKinematics " << std::endl;

    pinocchio::forwardKinematics(model_biped_fixed,data_biped_fixed,q_fixed);
    pinocchio::computeJointJacobian(model_biped_fixed,data_biped_fixed,q_fixed,J_Idx_l,JL);  // JL in joint frame
    pinocchio::computeJointJacobian(model_biped_fixed,data_biped_fixed,q_fixed,J_Idx_r,JR);  // JR in joint frame

    JCompact.block(0,0,6,model_fixed_nv)=JL;
    JCompact.block(6,0,6,model_fixed_nv)=JR;
    Eigen::Matrix<double,12,12> JJt;
    JJt.noalias() = JCompact * JCompact.transpose();
    JJt.diagonal().array() += damp;
    q_dot_fixed_desire.noalias() =  JCompact.transpose() * JJt.ldlt().solve(foot_vel_desire);

    // std::cout << "code come to q_dot_fixed_desire.noalias() " << std::endl;
}

Eigen::Matrix3d Pino_Kinematics::rotation_matrix(const std::string& axis, double angle) {
    Eigen::Matrix3d R;
    double c = std::cos(angle);
    double s = std::sin(angle);

    if (axis == "x") {
        R << 1, 0, 0,
             0, c, -s,
             0, s, c;
    } else if (axis == "y") {
        R << c, 0, s,
             0, 1, 0,
            -s, 0, c;
    } else if (axis == "z") {
        R << c, -s, 0,
             s, c, 0,
             0, 0, 1;
    } else {
        throw std::invalid_argument("Axis must be 'x', 'y', or 'z'.");
    }
    return R;
}


double Pino_Kinematics::calculate_signed_angle_2d(const Eigen::Vector2d& a, const Eigen::Vector2d& b) {
    double dot_product = a.dot(b);
    double norm_a = a.norm();
    double norm_b = b.norm();

    double cos_theta = dot_product / (norm_a * norm_b);
    cos_theta = std::clamp(cos_theta, -1.0, 1.0);  
    double angle = std::acos(cos_theta);

    double cross_product = a(0) * b(1) - a(1) * b(0);  
    if (cross_product < 0) {
        angle = -angle;
    }

    return angle;
}

Eigen::VectorXd Pino_Kinematics::compute_joint_initial_angles(const std::string& leg_name) {

    double theta2_q0;
    Eigen::Vector3d axis;
    Eigen::Vector2d d42, d54;

    if (leg_name == "L") {

        theta2_q0 = Eigen::AngleAxisd(data_biped_fixed_zero.liMi[leg_l2_joint].rotation()).angle();
        axis = Eigen::AngleAxisd(data_biped_fixed_zero.liMi[leg_l2_joint].rotation()).axis();
        d42 = Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_l2_joint].translation()[0], data_biped_fixed_zero.oMi[leg_l2_joint].translation()[2]) - Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_l4_joint].translation()[0], data_biped_fixed_zero.oMi[leg_l4_joint].translation()[2]);
        d54 = Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_l4_joint].translation()[0], data_biped_fixed_zero.oMi[leg_l4_joint].translation()[2]) - Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_l5_joint].translation()[0], data_biped_fixed_zero.oMi[leg_l5_joint].translation()[2]);

    } else if (leg_name == "R") {

        theta2_q0 = Eigen::AngleAxisd(data_biped_fixed_zero.liMi[leg_r2_joint].rotation()).angle();
        axis = Eigen::AngleAxisd(data_biped_fixed_zero.liMi[leg_r2_joint].rotation()).axis();
        d42 = Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_r2_joint].translation()[0], data_biped_fixed_zero.oMi[leg_r2_joint].translation()[2]) - Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_r4_joint].translation()[0], data_biped_fixed_zero.oMi[leg_r4_joint].translation()[2]);
        d54 = Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_r4_joint].translation()[0], data_biped_fixed_zero.oMi[leg_r4_joint].translation()[2]) - Eigen::Vector2d(data_biped_fixed_zero.oMi[leg_r5_joint].translation()[0], data_biped_fixed_zero.oMi[leg_r5_joint].translation()[2]);


    } else {
        throw std::invalid_argument("leg_name must be 'L' or 'R'.");
    }

    // std::cout << "Theta2_q0 (angle of rotation): " << theta2_q0 << std::endl;
    // std::cout << "Axis (rotation axis): [" << axis[0] << ", " << axis[1] << ", " << axis[2] << "]" << std::endl;
    // std::cout << "d42 (vector from leg_r4_joint to leg_r2_joint in XZ-plane): [" << d42[0] << ", " << d42[1] << "]" << std::endl;
    // std::cout << "d54 (vector from leg_r5_joint to leg_r4_joint in XZ-plane): [" << d54[0] << ", " << d54[1] << "]" << std::endl;


    // leg-l:0-6, leg-r:7-13
    double theta1_q0 = 0;

    if (axis.x() < 0) {
        theta2_q0 = -theta2_q0;
    }

    double theta3_q0 = 0;

    // in z-x plane
    double theta4_q0 = calculate_signed_angle_2d(d54, d42);
    double theta5_q0 = calculate_signed_angle_2d(Eigen::Vector2d(0, 1), d54);

    double theta6_q0 = 0;  // ankle joints in the same y plane

    Eigen::VectorXd theta_array_q0(6);
    theta_array_q0 << theta1_q0, theta2_q0, theta3_q0, theta4_q0, theta5_q0, theta6_q0;

    // std::cout << "theta_array_q0: " << std::endl;
    // std::cout << theta_array_q0.transpose() << std::endl;

    return theta_array_q0;
}

Eigen::VectorXd Pino_Kinematics::compute_joint_angles(const Eigen::Matrix4d& T_W_base, const Eigen::Matrix4d& T_W_1, const std::string& leg_name) {

    // YAML::Node config = YAML::LoadFile(file_path);

    // std::cout << "Pino_Kinematics " << std::endl;

    Eigen::VectorXd theta_array_0;

    Eigen::Matrix4d T_base_7 = Eigen::Matrix4d::Identity();  
    double d5, r0, x0, d2, d3;
    double ankle_offset_y, ankle_offset_z;

    if (leg_name == "L") {
        // 读取左腿的关节角度数组
        theta_array_0 = theta_array_0_L;
        // theta_array_0 = Eigen::VectorXd(config["theta_array_0_L"].size());
        // int idx = 0;
        // for (const auto& value : config["theta_array_0_L"]) {
        //     theta_array_0(idx++) = value.as<double>();
        // }
        T_base_7.block<3, 3>(0, 0) = data_biped_fixed_zero.oMi[leg_l1_joint].rotation();
        T_base_7.block<3, 1>(0, 3) = data_biped_fixed_zero.oMi[leg_l1_joint].translation();
        d5 = data_biped_fixed_zero.liMi[leg_l2_joint].translation()[1];
        r0 = -data_biped_fixed_zero.liMi[leg_l6_joint].translation()[2];
        x0 = -data_biped_fixed_zero.liMi[leg_l6_joint].translation()[0];

        // d2 = data_biped_fixed_zero.liMi[leg_l5_joint].translation().norm(); 
        // d3 = (data_biped_fixed_zero.liMi[leg_l3_joint].translation() + data_biped_fixed_zero.liMi[leg_l4_joint].translation()).norm(); 
        ankle_offset_y = -data_biped_fixed_zero.liMi[leg_l6_joint].translation()[1];
        ankle_offset_z = -data_biped_fixed_zero.liMi[leg_l6_joint].translation()[2];

        Eigen::Vector3d t5 = data_biped_fixed_zero.liMi[leg_l5_joint].translation();
        d2 = Eigen::Vector2d(t5(0), t5(2)).norm();

        Eigen::Vector3d t3 = data_biped_fixed_zero.liMi[leg_l3_joint].translation();
        Eigen::Vector3d t4 = data_biped_fixed_zero.liMi[leg_l4_joint].translation();
        Eigen::Vector3d sum = t3 + t4;
        d3 = Eigen::Vector2d(sum(0), sum(2)).norm();


    } else if (leg_name == "R") {
        // 读取右腿的关节角度数组
        theta_array_0 = theta_array_0_R;
        // theta_array_0 = Eigen::VectorXd(config["theta_array_0_R"].size());
        // int idx = 0;
        // for (const auto& value : config["theta_array_0_R"]) {
        //     theta_array_0(idx++) = value.as<double>();
        // }
        T_base_7.block<3, 3>(0, 0) = data_biped_fixed_zero.oMi[leg_r1_joint].rotation();
        T_base_7.block<3, 1>(0, 3) = data_biped_fixed_zero.oMi[leg_r1_joint].translation();
        d5 = data_biped_fixed_zero.liMi[leg_r2_joint].translation()[1];
        r0 = -data_biped_fixed_zero.liMi[leg_r6_joint].translation()[2];
        x0 = -data_biped_fixed_zero.liMi[leg_r6_joint].translation()[0];
        // d2 = data_biped_fixed_zero.liMi[leg_r5_joint].translation().norm(); 
        // d3 = (data_biped_fixed_zero.liMi[leg_r3_joint].translation() + data_biped_fixed_zero.liMi[leg_r4_joint].translation()).norm(); 
        ankle_offset_y = -data_biped_fixed_zero.liMi[leg_r6_joint].translation()[1];
        ankle_offset_z = -data_biped_fixed_zero.liMi[leg_r6_joint].translation()[2];

        Eigen::Vector3d t5 = data_biped_fixed_zero.liMi[leg_r5_joint].translation();
        d2 = Eigen::Vector2d(t5(0), t5(2)).norm();

        Eigen::Vector3d t3 = data_biped_fixed_zero.liMi[leg_r3_joint].translation();
        Eigen::Vector3d t4 = data_biped_fixed_zero.liMi[leg_r4_joint].translation();
        Eigen::Vector3d sum = t3 + t4;
        d3 = Eigen::Vector2d(sum(0), sum(2)).norm();

    } else {
        throw std::invalid_argument("leg_name must be 'L' or 'R'.");
    }

    Eigen::Matrix4d T_1_base = T_W_1.inverse() * T_W_base;

    Eigen::Matrix4d T_1_7 = T_1_base * T_base_7;

    Eigen::Vector4d hip_7(0, d5, 0, 1);
    Eigen::Vector4d hip_1 = T_1_7 * hip_7;

    double xh = hip_1[0];
    double yh = hip_1[1];
    double zh = hip_1[2];

    double theta1;
    double theta1_1, theta1_2;
    const double epsilon = 1e-9;
    if (std::abs(yh) < epsilon && std::abs(zh) < epsilon) {
        theta1 = 0.0; // 处理yh和zh同时接近零的情况
    } else {
        // theta1 = -std::atan2(yh, zh);
        theta1_1 = atan2(zh, yh) + acos(ankle_offset_y/(sqrt(yh * yh + zh * zh)));
        theta1_2 = atan2(zh, yh) - acos(ankle_offset_y/(sqrt(yh * yh + zh * zh)));

        if(-sin(theta1_1)*yh+cos(theta1_1)*zh>=ankle_offset_z){
            theta1 = theta1_1;
        }else if(-sin(theta1_2)*yh+cos(theta1_2)*zh>=ankle_offset_z){
            theta1 = theta1_2;
        }else{
            theta1 = 0;
            std::cout<<"theta1 has no solution"<<std::endl;
        }
    }

    // double r = std::sqrt(yh * yh + zh * zh) - r0;
    double r = -sin(theta1)*yh+cos(theta1)*zh - ankle_offset_z;

    double s = xh - x0; 

    // 防止除零并确保D在有效范围
    double denominator_D = 2 * d2 * d3 + epsilon;
    double D = (r * r + s * s - d2 * d2 - d3 * d3) / denominator_D;
    D = std::clamp(D, -1.0, 1.0);

    double sqrt_term_theta3 = std::sqrt(std::max(1.0 - D * D, 0.0));
    double theta3 = -std::atan2(sqrt_term_theta3, D);

    // 计算alpha3时防止除零和asin参数越界
    double denominator_alpha3 = std::sqrt(r * r + s * s) + epsilon;
    double sin_alpha3 = (d3 * -std::sin(theta3)) / denominator_alpha3;
    sin_alpha3 = std::clamp(sin_alpha3, -1.0, 1.0);
    double alpha3 = std::asin(sin_alpha3);

    double theta2 = alpha3 + std::atan2(s, r);

    double theta1_q = theta1 + theta_array_0[5];
    double theta2_q = theta2 + theta_array_0[4];
    double theta3_q = theta3 + theta_array_0[3];

    // 求 hip_yaw: theta4; hip_roll: theta5; hip_pitch: theta6
    Eigen::Matrix3d R_1_7 = T_1_7.block<3, 3>(0, 0);
    Eigen::Matrix3d R_1_4 = rotation_matrix("x", theta1_q) * rotation_matrix("y", theta2_q) * rotation_matrix("y", theta3_q);
    Eigen::Matrix3d R_4_7 = R_1_4.transpose() * R_1_7;

    double r11 = R_4_7(0, 0);
    double r12 = R_4_7(0, 1);
    double r21 = R_4_7(1, 0);
    double r22 = R_4_7(1, 1);
    double r31 = R_4_7(2, 0);
    double r32 = R_4_7(2, 1);
    double r33 = R_4_7(2, 2);

    // 确保r32在有效范围内
    r32 = std::clamp(r32, -1.0, 1.0);

    double theta5, theta4, theta6;
    if (r32 < 0.99999) {
        double sqrt_term_theta5 = std::sqrt(std::max(1.0 - r32 * r32, 0.0)) + epsilon;
        theta5 = std::atan2(r32, sqrt_term_theta5);
        theta4 = std::atan2(-r12, r22);
        theta6 = std::atan2(-r31, r33);
    } else {
        theta5 = std::atan2(r32, epsilon); // 处理接近1的情况
        double theta46_denominator = r11 + epsilon;
        double theta46 = std::atan2(r21, theta46_denominator);
        theta4 = 0.5 * theta46;
        theta6 = 0.5 * theta46;
    }

    double theta4_q = theta4 + theta_array_0[2];
    double theta5_q = theta5 + theta_array_0[1];
    double theta6_q = theta6 + theta_array_0[0];

    Eigen::VectorXd theta_array(6);
    theta_array << -theta6_q, -theta5_q, -theta4_q, -theta3_q, -theta2_q, -theta1_q;

    return theta_array;
}

void Pino_Kinematics::compute_generalized_q_dq_desired(State state){

    base_T_actual = Eigen::Matrix4d::Identity();
    feet_r_T_W_desire = Eigen::Matrix4d::Identity();
    feet_l_T_W_desire = Eigen::Matrix4d::Identity();
    base_T_desire = Eigen::Matrix4d::Identity();

    if(state == State::STANDING){
        base_T_IK.setIdentity();
        base_T_IK.block<3, 1>(0, 3)  = base_Pos_desire;
    }else{
        base_T_IK.setIdentity();
        base_T_IK.block<3, 1>(0, 3) = generalized_q_actual.segment<3>(0);  
        base_T_IK.block<3, 3>(0, 0) = getRotationMatrixFromZyxEulerAngles(generalized_q_actual.segment<3>(3));
    }


    base_T_actual.block<3, 1>(0, 3) = generalized_q_actual.segment<3>(0);  
    base_T_desire.block<3, 1>(0, 3)  = base_Pos_desire;
    base_T_actual.block<3, 3>(0, 0) = getRotationMatrixFromZyxEulerAngles(generalized_q_actual.segment<3>(3));
    
    feet_r_T_W_desire.block<3, 1>(0, 3) = feet_r_Pos_W_desire; 
    feet_l_T_W_desire.block<3, 1>(0, 3) = feet_l_Pos_W_desire;  

    feet_r_EulerZYX_W_desire = Eigen::Vector3d(feet_r_EulerZ_W_desire, feet_r_EulerY_W_desire, 0);
    feet_l_EulerZYX_W_desire = Eigen::Vector3d(feet_l_EulerZ_W_desire, feet_l_EulerY_W_desire, 0);

    feet_r_T_W_desire.block<3, 3>(0, 0) = getRotationMatrixFromZyxEulerAngles(feet_r_EulerZYX_W_desire); 
    feet_l_T_W_desire.block<3, 3>(0, 0) = getRotationMatrixFromZyxEulerAngles(feet_l_EulerZYX_W_desire); 

    // std::cout << "feet_l_Pos_W_desire:  " <<feet_l_Pos_W_desire<< std::endl;   

    compute_dq_fixed_desire(state);

    // std::cout << "code come to after compute_dq_fixed_desire " << std::endl; 

    //base:0-5,left leg:6-12,right leg:13-19,waist:20-21,left arm:22-26,right arm:27-31

    generalized_q_desired.segment<3>(0) = base_Pos_desire;
    generalized_q_desired.segment<3>(3).setZero();
    generalized_q_desired[3] = base_EulerZ_desire;
    generalized_q_desired.segment(IndexLegStart, 6) = compute_joint_angles(base_T_IK, feet_l_T_W_desire, "L");
    // generalized_q_desired.segment<6>(6) << -0.824104, -0.145594, -0.379475, 1.37024, -0.619576, -0.00545543;
    // generalized_q_desired[IndexLegStart + IndexLegLength/2 - 1] = 0;
    generalized_q_desired.segment(IndexLegStart + IndexLegLength/2, 6) = compute_joint_angles(base_T_IK, feet_r_T_W_desire, "R");
    // generalized_q_desired.segment<6>(13) << -0.824104, 0.145594, 0.379475, 1.37024, -0.619576, 0.00545543;
    // generalized_q_desired[IndexLegStart + IndexLegLength - 1] = 0;
    generalized_q_desired.segment(IndexWaistStart, IndexWaistLength).setZero();
    generalized_q_desired[IndexWaistStart] = waist_pos_desire;
    generalized_q_desired.segment(IndexArmStart, IndexArmLength/2) = arm_l_JointPos_desire;
    generalized_q_desired.segment(IndexArmStart + IndexArmLength/2, IndexArmLength/2) = arm_r_JointPos_desire;
    // generalized_q_desired.segment(IndexArmStart, IndexArmLength/2).setZero();
    // generalized_q_desired.segment(IndexArmStart + IndexArmLength/2, IndexArmLength/2).setZero();

    generalized_q_dot_desired.segment(0, 3) = base_LinVel_desire;
    generalized_q_dot_desired.segment(3, 3).setZero();
    generalized_q_dot_desired[3] = base_OmegaZ_desire;
    // generalized_q_dot_desired.segment(6, 14).setZero();

    // generalized_q_dot_desired.segment(IndexLegStart, model_fixed_nv).setZero();
    generalized_q_dot_desired.segment(IndexLegStart, model_fixed_nv) = q_dot_fixed_desire;
    generalized_q_dot_desired.segment(IndexWaistStart, IndexWaistLength).setZero();
    generalized_q_dot_desired.segment(IndexArmStart, IndexArmLength/2) = arm_l_JointVel_desire;
    generalized_q_dot_desired.segment(IndexArmStart + IndexArmLength/2, IndexArmLength/2) = arm_r_JointVel_desire;

    // std::cout << "code come to after compute_generalized_q_dq_desired " << std::endl;  

    // pinocchio::forwardKinematics(model_biped_fixed, data_biped_fixed, generalized_q_desired.tail(26));
    // pinocchio::updateFramePlacements(model_biped_fixed, data_biped_fixed);
    // std::cout<<"data_biped_fixed.oMi[5]:"<<data_biped_fixed.oMi[5]<<std::endl;
    // std::cout<<"data_biped_fixed.oMi[12]:"<<data_biped_fixed.oMi[12]<<std::endl;

    Eigen::Matrix<double, Eigen::Dynamic, 1> generalized_q_IK = generalized_q_actual;
    generalized_q_IK.tail(model_fixed_nv) = generalized_q_desired.tail(model_fixed_nv);
    pinocchio::forwardKinematics(model_pino, data_IK, generalized_q_IK);
    pinocchio::updateFramePlacements(model_pino, data_IK);
    // std::cout<<"data_IK.oMi[left_ankle_roll].translation():"<<data_IK.oMi[model_pino.getJointId("left_ankle_roll")].translation().transpose()<<std::endl;
    // std::cout<<"data_IK.oMi[right_ankle_roll].translation():"<<data_IK.oMi[model_pino.getJointId("right_ankle_roll")].translation().transpose()<<std::endl;
    feet_l_Pos_W_IK = data_IK.oMi[model_pino.getJointId("left_ankle_roll")].translation();
    feet_r_Pos_W_IK = data_IK.oMi[model_pino.getJointId("right_ankle_roll")].translation();

    Eigen::Matrix4d base_T_initial = Eigen::Matrix4d::Identity();
    Eigen::Matrix4d feet_r_T_W_initial = Eigen::Matrix4d::Identity();
    Eigen::Matrix4d feet_l_T_W_initial = Eigen::Matrix4d::Identity();

    base_T_initial.block<3, 1>(0, 3) = Eigen::Vector3d(0.03, 0, 0.8);
    feet_r_T_W_initial.block<3, 1>(0, 3) = Eigen::Vector3d(0, -0.15, 0.058);
    feet_l_T_W_initial.block<3, 1>(0, 3) = Eigen::Vector3d(0, 0.15, 0.058);

    Eigen::MatrixXd right_q = compute_joint_angles(base_T_initial, feet_r_T_W_initial, "R");
    Eigen::MatrixXd left_q = compute_joint_angles(base_T_initial, feet_l_T_W_initial, "L");

    std::cout << "left_q:" << left_q << std::endl;  
    std::cout << "right_q:" << right_q << std::endl;  

}


void Pino_Kinematics::GetDataFromPackage(DataPackage &DataPackage)
{

    generalized_q_actual = DataPackage.generalized_q_actual;
    generalized_q_dot_actual = DataPackage.generalized_q_dot_actual;
    base_Pos_desire = DataPackage.base_Pos_desire;
    base_LinVel_desire = DataPackage.base_LinVel_desire;
    feet_r_Pos_W_desire = DataPackage.feet_r_Pos_W_desire;
    feet_l_Pos_W_desire = DataPackage.feet_l_Pos_W_desire;
    feet_r_LinVel_W_desire = DataPackage.feet_r_LinVel_W_desire;
    feet_l_LinVel_W_desire = DataPackage.feet_l_LinVel_W_desire;

    arm_l_JointPos_desire = DataPackage.arm_l_JointPos_desire;
    arm_r_JointPos_desire = DataPackage.arm_r_JointPos_desire;
    arm_l_JointVel_desire = DataPackage.arm_l_JointVel_desire;
    arm_r_JointVel_desire = DataPackage.arm_r_JointVel_desire;

    base_EulerZ_desire = DataPackage.base_EulerZ_desire;
    base_OmegaZ_desire = DataPackage.base_OmegaZ_desire;
    feet_l_EulerZ_W_desire = DataPackage.feet_l_EulerZ_W_desire;
    feet_l_OmegaZ_W_desire = DataPackage.feet_l_OmegaZ_W_desire;
    feet_r_EulerZ_W_desire = DataPackage.feet_r_EulerZ_W_desire;
    feet_r_OmegaZ_W_desire = DataPackage.feet_r_OmegaZ_W_desire;

    global_angular_vel = DataPackage.global_angular_vel;

    IndexLegStart = DataPackage.IndexLegStart;
    IndexLegLength = DataPackage.IndexLegLength;
    IndexArmStart = DataPackage.IndexArmStart;
    IndexArmLength = DataPackage.IndexArmLength;
    IndexWaistStart = DataPackage.IndexWaistStart;
    IndexWaistLength = DataPackage.IndexWaistLength;

    model_pino = DataPackage.robot_model;
    data_IK = DataPackage.robot_data;

    feet_l_EulerY_W_desire = DataPackage.feet_l_EulerY_W_desire;
    feet_r_EulerY_W_desire = DataPackage.feet_r_EulerY_W_desire;
    feet_l_OmegaY_W_desire = DataPackage.feet_l_OmegaY_W_desire;
    feet_r_OmegaY_W_desire = DataPackage.feet_r_OmegaY_W_desire;
    feet_r_EulerZYX_W_actual = DataPackage.feet_r_EulerZYX_W_actual;
    feet_l_EulerZYX_W_actual = DataPackage.feet_l_EulerZYX_W_actual;

    waist_pos_desire = DataPackage.waist_pos_desire;
    
}
void Pino_Kinematics::SetDataToPackage(DataPackage &DataPackage)
{
    DataPackage.generalized_q_desired = generalized_q_desired;
    DataPackage.generalized_q_dot_desired = generalized_q_dot_desired;

    DataPackage.feet_l_Pos_W_IK = feet_l_Pos_W_IK;
    DataPackage.feet_r_Pos_W_IK = feet_r_Pos_W_IK;

}

Eigen::Matrix<double, 3, 3> Pino_Kinematics::getRotationMatrixFromZyxEulerAngles(const Eigen::Matrix<double, 3, 1>& eulerAngles) 
{
    const double z = eulerAngles(0);
    const double y = eulerAngles(1);
    const double x = eulerAngles(2);

    const double c1 = cos(z);
    const double c2 = cos(y);
    const double c3 = cos(x);
    const double s1 = sin(z);
    const double s2 = sin(y);
    const double s3 = sin(x);

    const double s2s3 = s2 * s3;
    const double s2c3 = s2 * c3;

    // clang-format off
    Eigen::Matrix<double, 3, 3> rotationMatrix;
    rotationMatrix << c1 * c2,      c1 * s2s3 - s1 * c3,       c1 * s2c3 + s1 * s3,
                      s1 * c2,      s1 * s2s3 + c1 * c3,       s1 * s2c3 - c1 * s3,
                          -s2,                  c2 * s3,                   c2 * c3;
    // clang-format on
    return rotationMatrix;
}

Eigen::Matrix<double, 3, 1> Pino_Kinematics::getGlobalAngularVelocityFromEulerAnglesZyxDerivatives( const Eigen::Matrix<double, 3, 1>& eulerAngles, const Eigen::Matrix<double, 3, 1>& derivativesEulerAngles) 
{
    const double sz = sin(eulerAngles(0));
    const double cz = cos(eulerAngles(0));
    const double sy = sin(eulerAngles(1));
    const double cy = cos(eulerAngles(1));
    const double dz = derivativesEulerAngles(0);
    const double dy = derivativesEulerAngles(1);
    const double dx = derivativesEulerAngles(2);
    return {-sz * dy + cy * cz * dx, cz * dy + cy * sz * dx, dz - sy * dx};
}