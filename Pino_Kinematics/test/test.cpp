#include <iostream>
#include <random>
#include <fstream>  
#include <Eigen/Dense>
#include <pinocchio/algorithm/kinematics.hpp>
#include <pinocchio/algorithm/model.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/joint-configuration.hpp>
#include <chrono>  // 用于计时
// #include "Pino_Kinematics/include/Pino_Kinematics.hpp"  
#include "/home/<USER>/Documents/PhybotSofware/Pino_Kinematics/include/Pino_Kinematics.hpp"  

// 保存Eigen矩阵到CSV文件
void saveMatrixToCSV(const Eigen::MatrixXd& matrix, const std::string& filename) {
    std::ofstream file(filename);
    if (file.is_open()) {
        for (int i = 0; i < matrix.rows(); ++i) {
            for (int j = 0; j < matrix.cols(); ++j) {
                file << matrix(i, j);
                if (j < matrix.cols() - 1) file << ",";
            }
            file << "\n";
        }
        file.close();
        std::cout << "Data saved to " << filename << std::endl;
    } else {
        std::cerr << "Error opening file for writing: " << filename << std::endl;
    }
}

// 保存单一列的执行时间到CSV文件
void saveTimeToCSV(const Eigen::VectorXd& time_data, const std::string& filename) {
    std::ofstream file(filename);
    if (file.is_open()) {
        for (int i = 0; i < time_data.size(); ++i) {
            file << time_data(i) << "\n";
        }
        file.close();
        std::cout << "Time data saved to " << filename << std::endl;
    } else {
        std::cerr << "Error opening file for writing: " << filename << std::endl;
    }
}

int main() {
    // 加载机器人模型
    Pino_Kinematics pino;
    pinocchio::Model model;
    pinocchio::urdf::buildModel("/home/<USER>/Documents/PhybotSofware/RobotModel/phybot_v8/urdf/phybot_v8_26.urdf", model);

    pinocchio::Data data0(model);
    pinocchio::Data data1(model);
    Eigen::VectorXd q = Eigen::VectorXd::Zero(model.nq); // 初始化全零配置
    // std::cout<<model.nq<<std::endl;

    // 前向运动学
    pinocchio::forwardKinematics(model, data0, q);
    pinocchio::updateFramePlacements(model, data0);

    std::cout<<""<< data0.oMi[6].translation()<<std::endl;

    // 计算初始关节角度（左腿）
    Eigen::VectorXd theta_array_0_L = pino.compute_joint_initial_angles("L");

    // 计算初始关节角度（右腿）
    Eigen::VectorXd theta_array_0_R = pino.compute_joint_initial_angles("R");

    // 设置终端输出精度为16位
    std::cout << std::fixed << std::setprecision(16); // 保证16位精度输出

    // 打印左腿的初始关节角度
    std::cout << "theta_array_0_L: ";
    for (int i = 0; i < theta_array_0_L.size(); ++i) {
        std::cout << theta_array_0_L[i] << " ";
    }
    std::cout << std::endl;

    // 打印右腿的初始关节角度
    std::cout << "theta_array_0_R: ";
    for (int i = 0; i < theta_array_0_R.size(); ++i) {
        std::cout << theta_array_0_R[i] << " ";
    }
    std::cout << std::endl;


    // 设置左腿关节角度范围
    double q_ranges_L[6][2] = {
        {-M_PI / 2, M_PI / 2},  // q1
        {-0.5, 1},              // q2
        {-M_PI / 2, M_PI / 2},  // q3
        {0.08, M_PI / 2},       // q4
        {-M_PI / 2, M_PI / 4},  // q5
        {-M_PI / 2, M_PI / 2}   // q6
    };

    // 设置右腿关节角度范围
    double q_ranges_R[6][2] = {
        {-M_PI / 2, M_PI / 2},  // q1
        {-1, 0.5},              // q2
        {-M_PI / 2, M_PI / 2},  // q3
        {0.08, M_PI / 2},       // q4
        {-M_PI / 2, M_PI / 4},  // q5
        {-M_PI / 2, M_PI / 2}   // q6
    };

    const int num_iterations = 50; // 迭代次数
    Eigen::MatrixXd q_values_L(num_iterations, 6); // 存储左腿关节角度
    Eigen::MatrixXd q_values_R(num_iterations, 6); // 存储右腿关节角度
    Eigen::MatrixXd theta_values_L(num_iterations, 6); // 存储左腿逆解结果
    Eigen::MatrixXd theta_values_R(num_iterations, 6); // 存储右腿逆解结果
    Eigen::MatrixXd error_L(num_iterations, 6); // 存储左腿误差
    Eigen::MatrixXd error_R(num_iterations, 6); // 存储右腿误差
    Eigen::VectorXd computation_times(num_iterations); // 存储每次计算的时间

    // 随机数生成器
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis;

    // 迭代过程
    for (int i = 0; i < num_iterations; ++i) {
        // 随机生成左腿的关节角度
        for (int j = 0; j < 6; ++j) {
            dis.param(std::uniform_real_distribution<>::param_type(q_ranges_L[j][0], q_ranges_L[j][1]));
            q(j) = dis(gen); // 左腿对应 q[0] 到 q[5]
        }

        // 随机生成右腿的关节角度
        for (int j = 0; j < 6; ++j) {
            dis.param(std::uniform_real_distribution<>::param_type(q_ranges_R[j][0], q_ranges_R[j][1]));
            q(7 + j) = dis(gen); // 右腿对应 q[7] 到 q[12]
        }

        // 前向运动学计算
        pinocchio::forwardKinematics(model, data1, q);
        pinocchio::updateFramePlacements(model, data1);

        // std::cout << "model.njoints" << model.njoints << std::endl;

        // // 遍历所有关节并打印其相对位姿
        // for (pinocchio::JointIndex i = 0; i < model.njoints; ++i) {
        //     const std::string& joint_name = model.names[i];          // 获取关节名称
        //     const pinocchio::SE3& joint_transform = pino.data_biped_fixed_zero.liMi[i];     // 获取关节变换矩阵

        //     // 打印关节名称和变换矩阵
        //     std::cout << i << " Joint " << joint_name << " transform:\n";
        //     std::cout << joint_transform << "\n";

        //     // 打印关节配置
        //     if (i < q.size()) {
        //         std::cout << "q[" << i << "]: " << q[i] << "\n";
        //     } else {
        //         std::cout << "q[" << i << "]: Out of bounds for q\n";
        //     }
        // }

        // 提取左腿和右腿的旋转矩阵和位移
        Eigen::Matrix3d R_base_ankle_roll_L = data1.oMi[6].rotation();
        Eigen::Vector3d t_base_ankle_roll_L = data1.oMi[6].translation();

        Eigen::Matrix3d R_base_ankle_roll_R = data1.oMi[13].rotation();
        Eigen::Vector3d t_base_ankle_roll_R = data1.oMi[13].translation();

        // std::cout<<data1.oMi[14].translation()<<std::endl;

        // 左腿的位姿矩阵
        Eigen::Matrix4d T_W_ankle_roll_L = Eigen::Matrix4d::Identity();
        T_W_ankle_roll_L.block<3, 3>(0, 0) = R_base_ankle_roll_L; // 设置旋转矩阵
        T_W_ankle_roll_L.block<3, 1>(0, 3) = t_base_ankle_roll_L; // 设置平移向量

        // 右腿的位姿矩阵
        Eigen::Matrix4d T_W_ankle_roll_R = Eigen::Matrix4d::Identity();
        T_W_ankle_roll_R.block<3, 3>(0, 0) = R_base_ankle_roll_R; // 设置旋转矩阵
        T_W_ankle_roll_R.block<3, 1>(0, 3) = t_base_ankle_roll_R; // 设置平移向量

        Eigen::Matrix4d T_W_base = Eigen::Matrix4d::Identity();

        // 记录计算时间
        auto start_time = std::chrono::high_resolution_clock::now(); // 记录开始时间

        // 计算左腿的逆解
        Eigen::VectorXd theta_array_for_q_L = pino.compute_joint_angles(T_W_base, T_W_ankle_roll_L, "L");

        // 计算右腿的逆解
        Eigen::VectorXd theta_array_for_q_R = pino.compute_joint_angles(T_W_base, T_W_ankle_roll_R, "R");

        auto end_time = std::chrono::high_resolution_clock::now(); // 记录结束时间
        std::chrono::duration<double> duration = end_time - start_time; // 计算时间差
        computation_times(i) = duration.count(); // 保存计算时间

        // 存储左腿和右腿的关节角度
        q_values_L.row(i) = q.head(6).transpose(); // 存储左腿关节角度
        q_values_R.row(i) = q.segment(7, 6).transpose(); // 存储右腿关节角度

        // 存储逆解结果
        theta_values_L.row(i) = theta_array_for_q_L.transpose(); // 左腿逆解结果
        theta_values_R.row(i) = theta_array_for_q_R.transpose(); // 右腿逆解结果

        // 计算误差
        error_L.row(i) = q_values_L.row(i) - theta_values_L.row(i); // 左腿误差
        error_R.row(i) = q_values_R.row(i) - theta_values_R.row(i); // 右腿误差
    }

    // 保存左腿和右腿的关节角度到CSV文件
    saveMatrixToCSV(q_values_L, "q_values_L.csv");
    saveMatrixToCSV(q_values_R, "q_values_R.csv");

    // 保存左腿和右腿的逆解结果到CSV文件
    saveMatrixToCSV(theta_values_L, "theta_values_L.csv");
    saveMatrixToCSV(theta_values_R, "theta_values_R.csv");

    // 保存左腿和右腿的误差到CSV文件
    saveMatrixToCSV(error_L, "error_L.csv");
    saveMatrixToCSV(error_R, "error_R.csv");

    // 保存时间数据到CSV文件
    saveTimeToCSV(computation_times, "computation_times.csv");



    // 左腿的位姿矩阵
    Eigen::Matrix4d T_W_ankle_roll_L = Eigen::Matrix4d::Identity();
    T_W_ankle_roll_L.block<3, 1>(0, 3) = Eigen::Vector3d(0, 0.15, 0.058); // 设置平移向量
    // 右腿的位姿矩阵
    Eigen::Matrix4d T_W_ankle_roll_R = Eigen::Matrix4d::Identity();
    T_W_ankle_roll_R.block<3, 1>(0, 3) = Eigen::Vector3d(0, -0.15, 0.058); // 设置平移向量

    Eigen::Matrix4d T_W_base = Eigen::Matrix4d::Identity();
    T_W_base.block<3, 1>(0, 3) = Eigen::Vector3d(0, 0, 0.8);  // 设置平移向量
    // 计算左腿的逆解
    Eigen::VectorXd theta_array_for_q_L = pino.compute_joint_angles(T_W_base, T_W_ankle_roll_L, "L");
    // 计算右腿的逆解
    Eigen::VectorXd theta_array_for_q_R = pino.compute_joint_angles(T_W_base, T_W_ankle_roll_R, "R");
    // 保存左腿和右腿的逆解结果到CSV文件
    saveMatrixToCSV(theta_array_for_q_L, "initial_theta_values_L.csv");
    saveMatrixToCSV(theta_array_for_q_R, "initial_theta_values_R.csv");


    return 0;
}
