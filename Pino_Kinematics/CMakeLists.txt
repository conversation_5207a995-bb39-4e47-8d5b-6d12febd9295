cmake_minimum_required(VERSION 3.10)

set(CMAKE_CXX_STANDARD 17)
add_compile_options(-std=c++17)

project(BacisFuction)
message(STATUS "Project source directory: ${PROJECT_SOURCE_DIR}")

include_directories(${PROJECT_SOURCE_DIR}/../DataPackage/include)
include_directories(${PROJECT_SOURCE_DIR}/../StateMachine/include)
include_directories(${PROJECT_SOURCE_DIR})

include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/mujoco)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/glfw)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/yaml/include)
include_directories(${PROJECT_SOURCE_DIR}/../MujocoInterface/include)


include_directories(${PROJECT_SOURCE_DIR}/../DataLogger/include)
include_directories(${PROJECT_SOURCE_DIR}/../DataPackage/include)
include_directories(${PROJECT_SOURCE_DIR}/../StateMachine/include)
include_directories(${PROJECT_SOURCE_DIR}/../StateMachine)
include_directories(${PROJECT_SOURCE_DIR}/../MotorList/include)
include_directories(${PROJECT_SOURCE_DIR}/../LowPassFilter/include)
include_directories(${PROJECT_SOURCE_DIR}/../ZeroState/include)

include_directories(${PROJECT_SOURCE_DIR}/include)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/eigen3)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/boost)

include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/pinocchio/include)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/yaml/include)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/urdfdom/include)
# include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/libtorch/include)

link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/pinocchio/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/yaml/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/urdfdom/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/boost/lib)
# link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/libtorch/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/console_bridge/lib)
# link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/tinyspline/lib)


file(GLOB SOURCES 

    ${PROJECT_SOURCE_DIR}/../DataPackage/src/*.cpp
    ${PROJECT_SOURCE_DIR}/test/*.cpp
    ${PROJECT_SOURCE_DIR}/src/*.cpp

)
# add_library(DataPackage SHARED ${dir_Gait_HLIP})

# target_link_libraries(DataPackage pinocchio yaml-cpp)

add_executable(test ${SOURCES})

target_link_libraries(test  PUBLIC 
pinocchio_default 
pinocchio_parsers
yaml-cpp
# tinysplinecxx 
urdfdom_model )