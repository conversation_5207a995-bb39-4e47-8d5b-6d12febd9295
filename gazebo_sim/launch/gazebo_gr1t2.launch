<launch>
    <arg name="wname" default="stairs"/>
    <arg name="rname" default="gr1t2"/>
    <param name="robot_name" type="str" value="$(arg rname)"/>
    <param name="ros_namespace" type="str" value="/$(arg rname)_gazebo/"/>
    <arg name="robot_path" value="(find $(arg rname)_description)"/>
    <arg name="dollar" value="$"/>

    <arg name="paused" default="true"/>
    <arg name="use_sim_time" default="true"/>
    <arg name="gui" default="true"/>
    <arg name="headless" default="false"/>
    <arg name="debug" default="false"/>
    
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
        <arg name="world_name" value="$(find rl_sim)/worlds/$(arg wname).world"/>
        <arg name="debug" value="$(arg debug)"/>
        <arg name="gui" value="$(arg gui)"/>
        <arg name="paused" value="$(arg paused)"/>
        <arg name="use_sim_time" value="$(arg use_sim_time)"/>
        <arg name="headless" value="$(arg headless)"/>
    </include>

    <!-- Load the URDF into the ROS Parameter Server -->
    <param name="robot_description" textfile="$(find gr1t2_description)/urdf/GR1T2_simple.urdf"/>

    <!-- Run a python script to the send a service call to gazebo_ros to spawn a URDF robot -->
    <!-- Set trunk and joint positions at startup -->
    <node pkg="gazebo_ros" type="spawn_model" name="urdf_spawner" respawn="false" output="screen"
          args="-urdf -z 1.0 -model $(arg rname)_gazebo -param robot_description"/>

    <!-- Load joint controller configurations from YAML file to parameter server -->
    <rosparam file="$(arg dollar)$(arg robot_path)/config/robot_control.yaml" command="load"/>

    <!-- load the controllers -->
    <node pkg="controller_manager" type="spawner" name="controller_spawner" respawn="false"
          output="screen" ns="/$(arg rname)_gazebo" args="joint_state_controller
          l_hip_roll_controller l_hip_yaw_controller l_hip_pitch_controller l_knee_pitch_controller l_ankle_pitch_controller
          r_hip_roll_controller r_hip_yaw_controller r_hip_pitch_controller r_knee_pitch_controller r_ankle_pitch_controller "/>

    <!-- convert joint states to TF transforms for rviz, etc -->
    <node pkg="robot_state_publisher" type="robot_state_publisher" name="robot_state_publisher"
          respawn="false" output="screen">
        <remap from="/joint_states" to="/$(arg rname)_gazebo/joint_states"/>
    </node>

</launch>
