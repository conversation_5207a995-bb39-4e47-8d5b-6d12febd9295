gr1t1:
  stand_model_name: "stand_model_jit.pt"
  walk_model_name: "walk_model_jit.pt"
  dt: 0.001
  decimation: 20
  num_observations: 39
  clip_obs: 100.0
  clip_actions_lower: [-0.4391, -1.0491, -2.0991, -0.4391, -1.3991,
                       -1.1391, -1.0491, -2.0991, -0.4391, -1.3991]
  clip_actions_upper: [1.1391, 1.0491, 1.0491, 2.2691, 0.8691,
                       0.4391, 1.0491, 1.0491, 2.2691, 0.8691]
  rl_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
          57.0, 43.0, 114.0, 114.0, 15.3]
  rl_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
          5.7, 4.3, 11.4, 11.4, 1.5]
  fixed_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
             57.0, 43.0, 114.0, 114.0, 15.3]
  fixed_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
             5.7, 4.3, 11.4, 11.4, 1.5]
  num_of_dofs: 10
  action_scale: 1.0
  lin_vel_scale: 1.0
  ang_vel_scale: 1.0
  dof_pos_scale: 1.0
  dof_vel_scale: 1.0
  commands_scale: [1.0, 1.0, 1.0]
  torque_limits: [60.0, 45.0, 130.0, 130.0, 16.0,
                  60.0, 45.0, 130.0, 130.0, 16.0]
  default_dof_pos: [0.0, 0.0, -0.2618, 0.5236, -0.2618, 
                    0.0, 0.0, -0.2618, 0.5236, -0.2618]
  joint_controller_names: ["l_hip_roll_controller", "l_hip_yaw_controller", "l_hip_pitch_controller", "l_knee_pitch_controller", "l_ankle_pitch_controller", 
                           "r_hip_roll_controller", "r_hip_yaw_controller", "r_hip_pitch_controller", "r_knee_pitch_controller", "r_ankle_pitch_controller"]

gr1t2:
  stand_model_name: "stand_model_jit.pt"
  walk_model_name: "walk_model_jit.pt"
  dt: 0.001
  decimation: 20
  num_observations: 39
  clip_obs: 100.0
  clip_actions_lower: [-0.4391, -1.0491, -2.0991, -0.4391, -1.3991,
                       -1.1391, -1.0491, -2.0991, -0.4391, -1.3991]
  clip_actions_upper: [1.1391, 1.0491, 1.0491, 2.2691, 0.8691,
                       0.4391, 1.0491, 1.0491, 2.2691, 0.8691]
  rl_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
          57.0, 43.0, 114.0, 114.0, 15.3]
  rl_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
          5.7, 4.3, 11.4, 11.4, 1.5]
  fixed_kp: [57.0, 43.0, 114.0, 114.0, 15.3, 
             57.0, 43.0, 114.0, 114.0, 15.3]
  fixed_kd: [5.7, 4.3, 11.4, 11.4, 1.5, 
             5.7, 4.3, 11.4, 11.4, 1.5]
  num_of_dofs: 10
  action_scale: 1.0
  lin_vel_scale: 1.0
  ang_vel_scale: 1.0
  dof_pos_scale: 1.0
  dof_vel_scale: 1.0
  commands_scale: [1.0, 1.0, 1.0]
  torque_limits: [60.0, 45.0, 130.0, 130.0, 16.0,
                  60.0, 45.0, 130.0, 130.0, 16.0]
  default_dof_pos: [0.0, 0.0, -0.2618, 0.5236, -0.2618, 
                    0.0, 0.0, -0.2618, 0.5236, -0.2618]
  joint_controller_names: ["l_hip_roll_controller", "l_hip_yaw_controller", "l_hip_pitch_controller", "l_knee_pitch_controller", "l_ankle_pitch_controller", 
                           "r_hip_roll_controller", "r_hip_yaw_controller", "r_hip_pitch_controller", "r_knee_pitch_controller", "r_ankle_pitch_controller"]