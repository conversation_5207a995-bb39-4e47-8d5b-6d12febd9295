#ifndef DATAPACKAGE_H
#define DATAPACKAGE_H

#include <eigen3/Eigen/Dense>
#include <vector>
#include "yaml-cpp/yaml.h"

#include "pinocchio/parsers/urdf.hpp"
 
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/kinematics.hpp"

#include "StateMachine/include/fsmlist.h"

#include "device/Imu_hipnuc/linux/HipnucReader.h"
// #include "/home/<USER>/Documents/PhybotSofware/StateMachine/include/fsmlist.h"

class DataPackage
{
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW


    DataPackage();
    void UpdateAfterSetData();
    void init();
    void getIMUdata(HipnucReader reader);
    // data
    // dimension of the robot dim = motorNum + floatingNum

    pinocchio::Model robot_model;
    pinocchio::Data robot_data;
    int generalizedCoordinatesNum;
    int actuatedDofNum;
    // control cycle time
    double control_period;

    int IndexLegStart, IndexLegLength;
    int IndexArmStart, IndexArmLength;
    int IndexWaistStart, IndexWaistLength;
    /**
     * @brief q_a
     *     joint position sense
     * qa = [q_float, q_joint] for floating base robot and mobile robot
     * qa = [q_joint] for fixed robot
     */

    // only pinocchio use
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_actual;
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_desired;
 
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_dot_actual;
    Eigen::Matrix<double, Eigen::Dynamic,1> generalized_q_dot_desired;


    // from sensors 
    Eigen::Matrix<double, Eigen::Dynamic,1> motor_torque;
    Eigen::Matrix<double, Eigen::Dynamic,1> motor_vel;
    Eigen::Matrix<double, Eigen::Dynamic,1> motor_pos;
    Eigen::Matrix<double, 3,1> imu_lin_acc;     
    Eigen::Quaternion<double> imu_quat;      
    Eigen::Matrix<double, 4,1>  baseQuat;      
    Eigen::Matrix<double, 3,1> imu_rpy;  
    Eigen::Matrix<double, 3,1> imu_zyx;     
    Eigen::Matrix<double, 3,1> imu_xyz; 
    Eigen::Matrix<double, 3,1> imu_angular_vel;      

    //from state estimator
    Eigen::Matrix<double, Eigen::Dynamic,1> contact_force;      
    Eigen::Matrix<double, 3,1> base_lin_vel;     
    Eigen::Matrix<double, 3,1> base_pos;     
    std::vector<Eigen::VectorXd> contact_force_history;
    std::vector<double> speed_forward_history;
    double speed_forward_avg{0};

    Eigen::Vector3d base_lin_vel_local;




    Eigen::Matrix<double, Eigen::Dynamic,1> q_desire;    // joint position command

    Eigen::Matrix<double, Eigen::Dynamic,1> q_dot_desire;    // joint velocity command

    Eigen::Matrix<double, Eigen::Dynamic,1> q_dot_dot_desire;      // joint acceleration command

    Eigen::Matrix<double, Eigen::Dynamic,1> torq_desire;    // joint torque command

    Eigen::Matrix<double, Eigen::Dynamic,1> motor_Pos_desire;    
    Eigen::Matrix<double, Eigen::Dynamic,1> motor_Vel_desire;    
    Eigen::Matrix<double, Eigen::Dynamic,1> motor_Torque_desire;  



    Eigen::VectorXd q_p_gain_a;
    Eigen::VectorXd q_d_gain_a;
    // 
    Eigen::VectorXd q_p_gain_c;
    Eigen::VectorXd q_d_gain_c;

    int foot_contact_size;
    // std::vector<std::string> contactNames3DoF { "leg_l_f1_link", "leg_r_f1_link", "leg_l_f2_link", "leg_r_f2_link" };
    // std::vector<std::string> contactNames3DoF { "leg_l_f1_link", "leg_r_f1_link", "leg_l_f2_link", "leg_r_f2_link" };
    std::vector<std::string> contactNames3DoF;
//   std::vector<std::string> contactNames3DoF{"l_foot_toe_1", "r_foot_toe_1", "l_foot_heel_1", "r_foot_heel_1", "l_foot_toe_2", "r_foot_toe_2", "l_foot_heel_2", "r_foot_heel_2"};

    std::vector<int> endEffectorFrameIndices;
    std::vector<bool> contact_flag;
    std::string robot_model_path;

    // states and key variables for Gait_HLIP
    double T_START{0},t{0};
    int FootNum{1};
    Eigen::Matrix<double, 3, 1> base_Pos_actual, base_LinVel_actual;
    Eigen::Matrix<double, 3, 1> base_Pos_desire, base_LinVel_desire;
    Eigen::Matrix<double, 3, 1> feet_r_Pos_W_actual, feet_l_Pos_W_actual;
    Eigen::Matrix<double, 3, 3> feet_r_Rot_W_actual, feet_l_Rot_W_actual;
    Eigen::Matrix<double, 3, 1> hip_r_Pos_W_actual, hip_l_Pos_W_actual;



    Eigen::Matrix<double, 3, 1> feet_r_LinVel_W_actual, feet_l_LinVel_W_actual;
    Eigen::Matrix<double, 3, 1> feet_r_Pos_W_desire, feet_l_Pos_W_desire;
    Eigen::Matrix<double, 3, 1> feet_r_LinVel_W_desire, feet_l_LinVel_W_desire;
    Eigen::Matrix<double, 3, 1> feet_r_Pos_PhaseStart_W, feet_l_Pos_PhaseStart_W;

    Eigen::Vector3d feet_r_AngVel_W_actual, feet_l_AngVel_W_actual;

    Eigen::Vector3d base_Pos_PhaseStart;
    double swingFactor{0}, swingR{0}, swingL{0};

    Eigen::Vector3d feet_r_Pos_W_IK, feet_l_Pos_W_IK;
    // std::array<bool, 8> contact_flag { true, true, true, true, true, true, true, true };

    Eigen::Matrix<double, 5, 1> arm_l_JointPos_GaitStart, arm_r_JointPos_GaitStart;
    Eigen::Matrix<double, 5, 1> arm_l_JointPos_desire, arm_r_JointPos_desire;
    Eigen::Matrix<double, 5, 1> arm_l_JointVel_desire, arm_r_JointVel_desire;
    bool IfFirstStand{true};
    bool IfWalkStart{false};
    bool IfGaitEnd{false};
    bool IfStand2Walk{false};
    bool IfCanStop{false};
    bool IfFly2Wlak{false};
    Eigen::Vector3d feet_l_Pos_StandStart_W, feet_r_Pos_StandStart_W;
    Eigen::Vector3d base_Pos_StandStart_W;


    // states and key variables for Joystick
    double gait_vx_desire{0}, gait_vy_desire{0};
    double js_vx_desire{0};
    double js_vy_desire{0};
    double js_vyaw_desire{0};
    double acc_max;
    
    int motionState;
    int legState;

    // State CurrentState{State::ZERO};
    // State NextState{State::ZERO};
    State CurrentState{State::IDLE};
    State NextState{State::IDLE};

    double gait_OmegaZ_desire{0};
    double feet_l_EulerZ_W_actual, feet_r_EulerZ_W_actual;
    double feet_r_EulerZ_PhaseStart_W, feet_l_EulerZ_PhaseStart_W;
    double base_EulerZ_PhaseStart;
    double feet_l_OmegaZ_W_desire, feet_r_OmegaZ_W_desire;
    double feet_l_EulerZ_W_desire, feet_r_EulerZ_W_desire;
    double feet_l_EulerY_W_desire, feet_r_EulerY_W_desire;
    double feet_l_OmegaY_W_desire, feet_r_OmegaY_W_desire;
    Eigen::Vector3d feet_r_EulerZYX_W_actual, feet_l_EulerZYX_W_actual;

    double base_EulerZ_desire, base_OmegaZ_desire;
    Eigen::Matrix<double, 3,1> global_angular_vel;   

    Eigen::Vector2d u_B, u_W;
    Eigen::Vector2d P_B, V_B;
    Eigen::Vector2d P_W, V_W;
    double vx_Final{0};
    double vy_Final{0};
    double py_ref,vy_ref;

    bool isWalking;
    bool If_first_Zero{true};

    double stand_up_pos{0};
    double stand_forward_pos{0};
    double stand_left_pos{0};
    double stand_yaw_pos{0};

    double waist_pos_desire{0};
    Eigen::Vector3d vel_base_obser;
    Eigen::Vector3d vel_base_estim;
    double stand_up_vel;


    // for rl
    int dim;
    double dt;
    Eigen::Matrix<double, Eigen::Dynamic, 1> q_a;
    Eigen::Matrix<double, Eigen::Dynamic, 1> q_dot_a;
    Eigen::Matrix<double, Eigen::Dynamic, 1> q_ddot_a;
    Eigen::Matrix<double, Eigen::Dynamic, 1> tau_a;
    Eigen::Matrix<double, 6, Eigen::Dynamic> ft_sensor;
    Eigen::Matrix<double, 18, Eigen::Dynamic> imu_sensor;
    std::vector<Eigen::MatrixXd> task_desired_value;
    std::vector<bool> task_desired_contact_state;
    Eigen::Matrix<double, Eigen::Dynamic, 1> q_c;
    Eigen::Matrix<double, Eigen::Dynamic, 1> q_dot_c;
    Eigen::Matrix<double, Eigen::Dynamic, 1> q_ddot_c;
    Eigen::Matrix<double, Eigen::Dynamic, 1> tau_c;
    Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> var_temp;
    Eigen::VectorXd q_factor;
    Eigen::VectorXd q_dot_factor;
    Eigen::Matrix3d xyz_R_init;
    Eigen::Matrix3d NED_R_YPR0;
    Eigen::Matrix3d NED_R_YPRa;
    bool imu_init_flag = 0;
    Eigen::VectorXd q_waist_c = Eigen::VectorXd::Ones(1);
    Eigen::Matrix<double, 1, 1> q_a_Waist;
    Eigen::Matrix<double, 1, 1> q_dot_a_Waist;
    Eigen::Matrix<double, 1, 1> tau_a_Waist;
    Eigen::Matrix<double, 1, 1> q_dot_c_Waist;
    Eigen::Matrix<double, 1, 1> q_ddot_c_Waist;
    Eigen::Matrix<double, 1, 1> tau_c_Waist;
    Eigen::VectorXd q_factor_Waist = Eigen::VectorXd::Ones(1);
    Eigen::VectorXd q_dot_factor_Waist = Eigen::VectorXd::Ones(1);
    Eigen::Matrix<double, 8, 1> q_a_Arm;
    Eigen::Matrix<double, 8, 1> q_dot_a_Arm;
    Eigen::Matrix<double, 8, 1> tau_a_Arm;
    Eigen::Matrix<double, 8, 1> q_c_Arm;
    Eigen::Matrix<double, 8, 1> q_dot_c_Arm;
    Eigen::Matrix<double, 8, 1> q_ddot_c_Arm;
    Eigen::Matrix<double, 8, 1> tau_c_Arm;
    Eigen::VectorXd q_factor_Arm = Eigen::VectorXd::Ones(8);
    Eigen::VectorXd q_dot_factor_Arm = Eigen::VectorXd::Ones(8);

    // NLP
    bool nlp_init = false;
    double frequency;
    double foot_height;
    double root_height;
    double root_pitch_joint;
    double waist_yaw_joint;
    double choose_mode;
    Eigen::VectorXd default_dof_pos;
    Eigen::VectorXd plan_pos;
    Eigen::VectorXd output_nlp; 
    Eigen::VectorXd input_nlp; 

};

#endif // DATAPACKAGE_H
