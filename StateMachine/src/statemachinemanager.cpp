#include "StateMachine/include/statemachinemanager.h"

void StateMachineManager::GetDataFromPackage(DataPackage &DataPackage)
{
    NextState = DataPackage.NextState;

    // IfWalkStart = DataPackage.IfWalkStart;
    // IfGaitEnd = DataPackage.IfGaitEnd;
    // IfStand2Walk = DataPackage.IfStand2Walk;
    // IfCanStop = DataPackage.IfCanStop;
    // std::cout<< "GetDataFromPackage IfWalkStart:"<<IfWalkStart<<std::endl;

}
void StateMachineManager::SetDataToPackage(DataPackage &DataPackage)
{
    DataPackage.CurrentState = CurrentState;

    // DataPackage.IfWalkStart = IfWalkStart;
    // DataPackage.IfStand2Walk = IfStand2Walk;
    // std::cout<< "SetDataToPackage IfWalkStart:"<<IfWalkStart<<std::endl;

}
void StateMachineManager::run(DataPackage &DataPackage)
{
    if(CurrentState==NextState){

    }else if(CurrentState == State::ZERO){

        if(NextState==State::STANDING){
            CurrentState = NextState;
        }


    }else if(CurrentState == State::STANDING){

        if(NextState==State::WALKING){
            CurrentState = NextState;
            DataPackage.IfWalkStart = true;
            DataPackage.IfStand2Walk = true;
        }else if(NextState == State::ZERO){
            DataPackage.If_first_Zero = true;
            CurrentState = NextState;
        }

    }else if(CurrentState == State::WALKING){

        if(NextState==State::STANDING){
            if(DataPackage.IfGaitEnd && DataPackage.IfCanStop){
                CurrentState = NextState;
            }
        }else if(NextState == State::FLYING){
            if(DataPackage.IfGaitEnd){
                CurrentState = NextState;
            }
        // }else if(NextState == State::ZERO){
        //     DataPackage.If_first_Zero = true;
        //     CurrentState = NextState;
        }

    }else if(CurrentState == State::FLYING){

        if(NextState==State::WALKING){                
            
            if(DataPackage.IfGaitEnd){
                CurrentState = NextState;
                DataPackage.IfFly2Wlak = true;
            }
        }

    }

    handleEvent(CurrentState, Event::RUN, DataPackage);

}