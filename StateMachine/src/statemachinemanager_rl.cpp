#include "StateMachine/include/statemachinemanager_rl.h"

void StateMachineManager_RL::GetDataFromPackage(DataPackage &DataPackage)
{
    NextState = DataPackage.NextState;


}
void StateMachineManager_RL::SetDataToPackage(DataPackage &DataPackage)
{
    DataPackage.CurrentState = CurrentState;

}
void StateMachineManager_RL::run(DataPackage &DataPackage)
{
    // std::cout << "run" << std::endl;
    // std::cout << "CurrentState:" << CurrentState << std::endl;
    // std::cout << "NextState:" << NextState << std::endl;
    if(CurrentState == State::IDLE){
        if(NextState==State::ZERO){
            CurrentState = NextState;
        }
    }else if(CurrentState == State::ZERO){
        if(NextState==State::NLP){
            CurrentState = NextState;
        }
    }
    handleEvent(CurrentState, Event::RUN, DataPackage);
}