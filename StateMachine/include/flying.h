#include "StateMachine/include/statemacine.h"
#include "WholeBodyControl/include/WBCSolver.h"
#include "GaitPlanner/include/GaitPlanner.h"
#include "Pino_Kinematics/include/Pino_Kinematics.hpp"



class Flying : public StateMachine {
public:
    Flying():wbc(nullptr){
        // 默认初始化状态并打印
        setCurrentState(State::FLYING);
        printCurrentState();
    }

    void start(Event event, DataPackage& data) override {
        // std::cout << "Handling Event1 in state " << static_cast<int>(currentState) << std::endl;
                // 如果 wbc 尚未被创建，则创建它
        if (wbc == nullptr) {
            wbc = std::make_unique<WBC_Solver::WeightedWBCSolver>(data, toString(State::FLYING));
            gaitplanner = std::make_unique<GaitPlanner>(data, toString(State::FLYING));
            pino = std::make_unique<Pino_Kinematics>();
        }
         

    }

    void run(Event event, DataPackage& data) override {

         
        
        gaitplanner->GetDataFromPackage(data);

        gaitplanner->Fly();

        gaitplanner->SetDataToPackage(data);

        pino->GetDataFromPackage(data);

        pino->compute_generalized_q_dq_desired(State::FLYING);

        pino->SetDataToPackage(data);
        
        // WBC_Solver::WeightedWBCSolver wbc(data);
        wbc->GetDataFromPackage(data);

        wbc->Step();

        wbc->SetDataToPackage(data);

    }




    void exit(Event event, DataPackage& data) override {


    }


private: 
    std::unique_ptr<WBC_Solver::WeightedWBCSolver> wbc;  // 使用 unique_ptr 延迟创建
    std::unique_ptr<GaitPlanner> gaitplanner;  // 使用 unique_ptr 延迟创建
    std::unique_ptr<Pino_Kinematics> pino; 

};