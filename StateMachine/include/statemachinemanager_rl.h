#include "StateMachine/include/statemacine.h"
#include "StateMachine/include/zero.h"
#include "StateMachine/include/nlp.h"

class StateMachineManager_RL {
public:

    State CurrentState{State::IDLE};
    State NextState{State::IDLE};
    
    StateMachineManager_RL() {

        auto ZeroHandler = std::make_shared<Zero>();

        std::cout <<"111" << std::endl;
        auto NLPHandler = std::make_shared<NLP>();
        std::cout <<"333" << std::endl;

        registerHandler(State::ZERO, ZeroHandler);
        std::cout <<"44" << std::endl;
        registerHandler(State::NLP, NLPHandler);
        std::cout <<"55" << std::endl;

    }

    void registerHandler(State state, std::shared_ptr<StateMachine> handler) {
        handlers[state] = handler;
    }

    void handleEvent(State state, Event event, DataPackage& data) {
        auto handler = handlers.find(state);
        if (handler != handlers.end()) {
            if (event == Event::START) {
                handler->second->start(event, data);
            } else if (event == Event::RUN) {
                handler->second->run(event, data);
            } else if (event == Event::EXIT) {
                handler->second->exit(event, data);
            } 
        } else {
            // std::cout << "No handler for state: " << static_cast<int>(state) << std::endl;
        }
    }

    void init(DataPackage& data) {
        handleEvent(State::ZERO, Event::START, data);  // 自动触发 start 事件
        handleEvent(State::NLP, Event::START, data);  // 自动触发 start 事件
    }
    void GetDataFromPackage(DataPackage &DataPackage);
    void SetDataToPackage(DataPackage &DataPackage);
    void run(DataPackage &DataPackage);

private:
    std::unordered_map<State, std::shared_ptr<StateMachine>> handlers;
};