#include "StateMachine/include/statemacine.h"
#include "WholeBodyControl/include/WBCSolver.h"
#include "GaitPlanner/include/GaitPlanner.h"
#include "Pino_Kinematics/include/Pino_Kinematics.hpp"
#include "ZeroState/include/ZeroState.h"


class Zero : public StateMachine {
public:
    Zero():zerostate(nullptr){
        // 默认初始化状态并打印
        setCurrentState(State::ZERO);
        printCurrentState();
    }

    void start(Event event, DataPackage& data) override {
        // std::cout << "Handling Event1 in state " << static_cast<int>(currentState) << std::endl;
                // 如果 wbc 尚未被创建，则创建它
        if (zerostate == nullptr) {
            zerostate = std::make_unique<ZeroState>();

        }
         
    }

    void run(Event event, DataPackage& data) override {

        zerostate->GetDataFromPackage(data);
        zerostate->SetMotorZero(data);

    }




    void exit(Event event, DataPackage& data) override {


    }


private: 
    std::unique_ptr<ZeroState> zerostate;  // 使用 unique_ptr 延迟创建

};