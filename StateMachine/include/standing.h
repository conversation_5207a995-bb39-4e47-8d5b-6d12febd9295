#include "StateMachine/include/statemacine.h"
#include "WholeBodyControl/include/WBCSolver.h"
#include "GaitPlanner/include/GaitPlanner.h"
#include "Pino_Kinematics/include/Pino_Kinematics.hpp"


class Standing : public StateMachine {
public:
    Standing():wbc(nullptr){
        // 默认初始化状态并打印
        setCurrentState(State::STANDING);
        printCurrentState();
    }

    void start(Event event, DataPackage& data) override {
        // std::cout << "Handling Event1 in state " << static_cast<int>(currentState) << std::endl;
                // 如果 wbc 尚未被创建，则创建它
        if (wbc == nullptr) {
            // std::cout << "code come to start " << std::endl;
            wbc = std::make_unique<WBC_Solver::WeightedWBCSolver>(data, toString(State::STANDING));
            // std::cout << "code come to wbc " << std::endl;
            gaitplanner = std::make_unique<GaitPlanner>(data, toString(State::STANDING));
            // std::cout << "code come to gaitplanner " << std::endl;
            pino = std::make_unique<Pino_Kinematics>();
            // std::cout << "code come to pino " << std::endl;
        }
         

    }

    void run(Event event, DataPackage& data) override {

         
        // std::cout << "code come to gaitplanner " << std::endl;
        // auto record_start_time = std::chrono::steady_clock::now();

        gaitplanner->GetDataFromPackage(data);

        gaitplanner->Stand();

        gaitplanner->SetDataToPackage(data);

        // auto record_now_time1 = std::chrono::steady_clock::now();
        // std::chrono::duration<double> elapsed_record1 = record_now_time1 - record_start_time1;
        // std::cout<< "gaitplanner time:"<<elapsed_record1.count()<<std::endl;

        // std::cout << "code come to pino " << std::endl;

        auto record_start_time1 = std::chrono::steady_clock::now();

        pino->GetDataFromPackage(data);

        // std::cout << "code come to compute_generalized_q_dq_desired " << std::endl;

        pino->compute_generalized_q_dq_desired(State::STANDING);

        // std::cout << "code come to SetDataToPackage " << std::endl;

        pino->SetDataToPackage(data);
        // auto record_now_time = std::chrono::steady_clock::now();
        // std::chrono::duration<double> elapsed_record = record_now_time - record_start_time;
        // std::cout<< "wbc time:"<<elapsed_record.count()<<std::endl;
        auto record_now_time1 = std::chrono::steady_clock::now();
        std::chrono::duration<double> elapsed_record1 = record_now_time1 - record_start_time1;
        std::cout<< "pino time:"<<elapsed_record1.count()<<std::endl;
        
        // std::cout << "code come to wbc " << std::endl;
        // auto record_start_time = std::chrono::steady_clock::now();

        wbc->GetDataFromPackage(data);

        wbc->Step();

        wbc->SetDataToPackage(data);

  

    }




    void exit(Event event, DataPackage& data) override {


    }


private: 
    std::unique_ptr<WBC_Solver::WeightedWBCSolver> wbc;  // 使用 unique_ptr 延迟创建
    std::unique_ptr<GaitPlanner> gaitplanner;  // 使用 unique_ptr 延迟创建
    std::unique_ptr<Pino_Kinematics> pino; 

};