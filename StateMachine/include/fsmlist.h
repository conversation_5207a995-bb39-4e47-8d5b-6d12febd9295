#pragma once 

#include <iostream>

// 定义状态枚举
enum class State {
IDLE,
STANDING,
WALKING,
FLYING,
ZERO,
NLP
};

// 定义事件枚举
enum class Event {
START,
RUN,
EXIT
};

inline std::ostream& operator<<(std::ostream& os, const State& state) {
    switch (state) {
        case State::IDLE:     os << "IDLE"; break;
        case State::STANDING: os << "STANDING"; break;
        case State::WALKING:  os << "WALKING"; break;
        case State::FLYING:   os << "FLYING"; break;
        case State::ZERO:   os << "ZERO"; break;
        case State::NLP:   os << "NLP"; break;
        default:              os << "Unknown State"; break;
    }
    return os;
}