#pragma once
#include "StateEstimator/include/basicfunction.h"
#include "StateMachine/include/statemacine.h"
#include "GaitPlanner/include/GaitPlanner.h"
// #include "GaitPlanner/include/GaitRL.h"
#include "GaitPlanner/include/GaitMimic.h"
#include "DataPackage/include/DataPackage.h"
// #include "NlpModel/include/NlpModel.h"
#include "NlpModel/include/NlpMimicModel.h"


class NLPMimic : public StateMachine {
private: 
    GaitMimic* gaitMimic_ = nullptr;
    int count = 0;
    int frame_count = 0;
    int fre = 5;  // decimation

public:
    explicit NLPMimic(GaitMimic* gaitMimic = nullptr) : gaitMimic_(gaitMimic) {  
        setCurrentState(State::NLP);
        printCurrentState();
        count = 0;
        frame_count = 0;
    }
    
    void start(Event event, DataPackage& data) override {
        if (gaitMimic_ == nullptr) {
            gaitMimic_ = new GaitMimic();
            gaitMimic_->init(0.01, &data);
        }
        gaitMimic_->robotdata = &data;
        std::cout << "NLPMimic model load complete" << std::endl;
    }
    
    void run(Event event, DataPackage& data) override {
        if (count == 0) {
            count = 1;
        }
        else {
            count++; 
        }
        // refresh states of the outter simulation loop
        Eigen::Matrix3d Rb_w = Eigen::Matrix3d::Identity();
        basicfunction::Euler_ZYXToMatrix(Rb_w, data.imu_zyx);
        Eigen::Vector3d gravity_vector = -Rb_w.transpose().col(2);

        // refresh the policy obs of the inner control loop
        if (count % fre == 0) {
            // base_ang_vel in the local frame
            gaitMimic_->inputdata_nlp.segment(4, 3) = data.imu_angular_vel * gaitMimic_->obs_scales_ang_vel;
            // projected_gravity
            gaitMimic_->inputdata_nlp.segment(7, 3) = gravity_vector;
            // cur dof pos
            // std::cout << "motor_pos: " << data.motor_pos << std::endl;
            gaitMimic_->inputdata_nlp.segment(10, 21) = (data.motor_pos - gaitMimic_->default_dof_pos) * gaitMimic_->obs_scales_dof_pos;
            // cur dof vel
            gaitMimic_->inputdata_nlp.segment(31, 21) = data.motor_vel * gaitMimic_->obs_scales_dof_vel;
            // last action
            gaitMimic_->inputdata_nlp.segment(52, 21) = gaitMimic_->action_last;

            // read the task motion target from the motion lib
            frame_count++;  // the first frame is the initial state
            int frame_id = frame_count % gaitMimic_->num_motion_frames;
            // root_commands derived from the offline motion frames
            gaitMimic_->inputdata_nlp.segment(0, 4) = gaitMimic_->motion_frames.row(frame_id).segment(0, 4);
            gaitMimic_->inputdata_nlp.segment(73, gaitMimic_->task_obs_num) = gaitMimic_->motion_frames.row(frame_id).segment(4, gaitMimic_->task_obs_num);
            
            // write the history obs inputdata_nlp
            gaitMimic_->inputdata_nlp.segment(73+gaitMimic_->task_obs_num, 73*gaitMimic_->long_hist_len) = gaitMimic_->history_obs_buf;

            // update the history buffer with the latest obs
            gaitMimic_->history_obs_buf.segment(0, 73*(gaitMimic_->long_hist_len-1)) = gaitMimic_->history_obs_buf.segment(73, 73*(gaitMimic_->long_hist_len-1));
            gaitMimic_->history_obs_buf.segment(73*(gaitMimic_->long_hist_len-1), 73) = gaitMimic_->inputdata_nlp.segment(0, 73);
        }
        // policy inference 
        if (count % fre == 0) {
            gaitMimic_->nlp_mimic_model->set_inputdata(gaitMimic_->inputdata_nlp);
            gaitMimic_->nlp_mimic_model->act_interface();
            gaitMimic_->outputdata_nlp = gaitMimic_->nlp_mimic_model->get_outputdata();
            basicfunction::clip(gaitMimic_->outputdata_nlp, -gaitMimic_->action_clip, gaitMimic_->action_clip);
            gaitMimic_->action_last = gaitMimic_->outputdata_nlp;
        }

        // get the desired motor position from the policy with decimaiton frq
        Eigen::VectorXd dof_pos_desire = (gaitMimic_->outputdata_nlp * gaitMimic_->action_scales + gaitMimic_->default_dof_pos);
        // refresh data package
        // motor list
        data.motor_Pos_desire = dof_pos_desire;
        data.motor_Vel_desire.setZero();
        data.motor_Torque_desire.setZero();

        data.input_nlp = gaitMimic_->inputdata_nlp;
        data.output_nlp = dof_pos_desire;
    }

    void exit(Event event, DataPackage& data) override {
    }

    int getRunCount() const {
        return count;
    }
};