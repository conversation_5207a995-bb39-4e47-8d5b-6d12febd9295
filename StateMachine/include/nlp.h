#pragma once
#include "StateEstimator/include/basicfunction.h"
#include "StateMachine/include/statemacine.h"
#include "GaitPlanner/include/GaitPlanner.h"
#include "GaitPlanner/include/GaitRL.h"
#include "DataPackage/include/DataPackage.h"
#include "NlpModel/include/NlpModel.h"

class NLP : public StateMachine {
public:
    explicit NLP(GaitRL* gaitRL = nullptr) : gaitRL_(gaitRL) {  
        setCurrentState(State::NLP);
        printCurrentState();
        count = 0; 
    }
    
    void start(Event event, DataPackage& data) override {
        if (gaitRL_ == nullptr) {

            gaitRL_ = new GaitRL();
            gaitRL_->init(0.01, &data);
            if(gaitRL_) {
                gaitRL_->init(0.01, &data);
            }
        }
        auto robotdata = gaitRL_->robotdata;
        robotdata = &data;
        std::cout << "NLP load complete" << std::endl;

    }
    

    void run(Event event, DataPackage& data) override {
        if (count == 0) {
            count = 1; 
        } else {
            count++; 
        }

        Eigen::Matrix3d Rb_w = Eigen::Matrix3d::Identity();
        basicfunction::Euler_ZYXToMatrix(Rb_w, data.imu_zyx);
        Eigen::Vector3d gravity_vector = -Rb_w.transpose().col(2);  

        // Eigen::Matrix3d Rb_w = Eigen::Matrix3d::Identity();
        // basicfunction::Euler_ZYXToMatrix(Rb_w, data.imu_zyx);
        // Eigen::Vector3d gravity_vector = -Rb_w.transpose().col(2);  

        // Eigen::Vector3d gravity_vector = basicfunction::calculateGravityFromQuaternion(data.imu_quat);

        if (count % fre == 0) {
            gaitRL_->CPGHopfOscillator();

            gaitRL_->UpdateGaitGeneratorPattern(gaitRL_->command,data.choose_mode);
            gaitRL_->inputdata_nlp.segment(0, 3) = data.base_lin_vel_local * gaitRL_->obs_scales_lin_vel;


            gaitRL_->command[0] = data.js_vx_desire * gaitRL_->command_scales[0];
            gaitRL_->command[1] = data.js_vy_desire * gaitRL_->command_scales[1];
            gaitRL_->command[2] = data.js_vyaw_desire * gaitRL_->command_scales[2];

            gaitRL_->inputdata_nlp.segment(3, 3) = data.imu_angular_vel * gaitRL_->obs_scales_ang_vel;
            gaitRL_->inputdata_nlp.segment(6, 3) = gravity_vector;
            gaitRL_->inputdata_nlp(9) = data.js_vx_desire * gaitRL_->command_scales[0];
            gaitRL_->inputdata_nlp(10) = data.js_vy_desire * gaitRL_->command_scales[1];
            gaitRL_->inputdata_nlp(11) = data.js_vyaw_desire * gaitRL_->command_scales[2];
            gaitRL_->inputdata_nlp(12) = data.frequency;
            gaitRL_->inputdata_nlp(13) = data.foot_height;
            gaitRL_->inputdata_nlp(14) = data.root_height;
            gaitRL_->inputdata_nlp(15) = data.root_pitch_joint;
            gaitRL_->inputdata_nlp(16) = data.waist_yaw_joint;
            gaitRL_->inputdata_nlp(17) = data.choose_mode; 

            // std::cout << "choose_mode: " << data.choose_mode << std::endl;

            gaitRL_->inputdata_nlp.segment(18, 4) = gaitRL_->CPGGetXNorm();
            gaitRL_->inputdata_nlp.segment(22, 4) = gaitRL_->CPGGetYNorm();


            gaitRL_->inputdata_nlp.segment(26, 12) = (data.motor_pos.segment(0, 12) - gaitRL_->default_dof_pos.head(12)) * gaitRL_->obs_scales_dof_pos;
            gaitRL_->inputdata_nlp.segment(38, 1) = (data.motor_pos.segment(12, 1) - gaitRL_->default_dof_pos.segment(12, 1)) * gaitRL_->obs_scales_dof_pos;
            gaitRL_->inputdata_nlp.segment(39, 6) = (data.motor_pos.segment(13, 6) - gaitRL_->default_dof_pos.segment(13, 6)) * gaitRL_->obs_scales_dof_pos;

            gaitRL_->inputdata_nlp.segment(45, 12) = data.motor_vel.segment(0, 12) * gaitRL_->obs_scales_dof_vel;
            gaitRL_->inputdata_nlp.segment(57, 1) = data.motor_vel.segment(12, 1) * gaitRL_->obs_scales_dof_vel;
            gaitRL_->inputdata_nlp.segment(58, 6) = data.motor_vel.segment(13, 6) * gaitRL_->obs_scales_dof_vel;

            gaitRL_->inputdata_nlp.segment(64, 19) = gaitRL_->action_last;

        }




        if (count % fre == 0) {
            gaitRL_->history_pos_obs.segment(0, 76) = gaitRL_->history_pos_obs.segment(19, 76);
            gaitRL_->history_pos_obs.segment(76, 19) = gaitRL_->inputdata_nlp.segment(26, 19);

            gaitRL_->history_vel_obs.segment(0, 76) = gaitRL_->history_vel_obs.segment(19, 76);
            gaitRL_->history_vel_obs.segment(76, 19) = gaitRL_->inputdata_nlp.segment(45, 19);

            gaitRL_->history_encoder_obs.segment(80, 80*19) = gaitRL_->history_encoder_obs.segment(0, 80*19);
            gaitRL_->history_encoder_obs.segment(0, 80) = gaitRL_->inputdata_nlp.segment(3, 80);


            gaitRL_->inputdata_nlp.segment(83, 95) = gaitRL_->history_pos_obs;
            gaitRL_->inputdata_nlp.segment(178, 95) = gaitRL_->history_vel_obs;

            // gaitRL_->history_encoder_obs.segment(gaitRL_->unit_history_obs_num, gaitRL_->unit_history_obs_num*(gaitRL_->encoder_history_length-1)) = gaitRL_->history_encoder_obs.segment(0, gaitRL_->unit_history_obs_num*(gaitRL_->encoder_history_length-1));
            // gaitRL_->history_encoder_obs.segment(0, gaitRL_->unit_history_obs_num) = gaitRL_->inputdata_nlp.segment(3, gaitRL_->unit_history_obs_num);



            Eigen::VectorXd encoder_vel_z = gaitRL_->nlpmodel->act_interface_encoder(gaitRL_->history_encoder_obs);
            gaitRL_->inputdata_nlp.segment(0, 3) = encoder_vel_z.segment(0,3);
            Eigen::VectorXd latent_z = encoder_vel_z.segment(3, 16);
            double norm = latent_z.norm();
            if (norm > 0) {
                latent_z /= norm;
            } else {
                // std::cerr << "Warning: L2 norm is zero, unable to normalize." << std::endl;
            }
            // gaitRL_->inputdata_nlp.segment(gaitRL_->obs_num-16, 16) = latent_z;
            gaitRL_->inputdata_nlp.segment(273, 16) = latent_z;

            gaitRL_->nlpmodel->set_inputdata(gaitRL_->inputdata_nlp);

            gaitRL_->nlpmodel->act_interface();

            gaitRL_->outputdata_nlp = gaitRL_->nlpmodel->get_outputdata();

            basicfunction::clip(gaitRL_->outputdata_nlp, -gaitRL_->action_clip, gaitRL_->action_clip);
            // std::cout << "input: " << gaitRL_->inputdata_nlp.segment(26, 19) << std::endl;
            // std::cout << "&&&&&&&&&&&&&&&&&&&&&7" << std::endl;
            // std::cout << "input: " << gravity_vector << std::endl;

        }

        Eigen::VectorXd nlpout = (gaitRL_->outputdata_nlp * gaitRL_->action_scales + gaitRL_->default_dof_pos);
        gaitRL_->action_last = gaitRL_->outputdata_nlp;
        data.input_nlp = gaitRL_->inputdata_nlp;
        data.output_nlp = nlpout;
        // std::cout << "&&&&&&&&&&&&&&&&&&&&&7" << std::endl;
        // std::cout << "input: " << gaitRL_->inputdata_nlp.segment(64, 19) << std::endl;

        // motor list

        data.motor_Pos_desire = nlpout;
        data.motor_Vel_desire.setZero();
        data.motor_Torque_desire.setZero();


    }
    void exit(Event event, DataPackage& data) override {
        // if (gaitRL_) {
        //     gaitRL_->setstand(1);  
        // }
    }
    int getRunCount() const {
        return count;
    }

private: 
    GaitRL* gaitRL_ = nullptr;  
    int count = 0;
    int fre = 5;             
};