#include "StateMachine/include/statemacine.h"
#include "WholeBodyControl/include/WBCSolver.h"
#include "GaitPlanner/include/GaitPlanner.h"
#include "Pino_Kinematics/include/Pino_Kinematics.hpp"



class Walking : public StateMachine {
public:
    Walking():wbc(nullptr){
        // 默认初始化状态并打印
        setCurrentState(State::WALKING);
        printCurrentState();
    }

    void start(Event event, DataPackage& data) override {
        // std::cout << "Handling Event1 in state " << static_cast<int>(currentState) << std::endl;
                // 如果 wbc 尚未被创建，则创建它
        if (wbc == nullptr) {
            wbc = std::make_unique<WBC_Solver::WeightedWBCSolver>(data, toString(State::WALKING));
            gaitplanner = std::make_unique<GaitPlanner>(data, toString(State::WALKING));
            pino = std::make_unique<Pino_Kinematics>();
        }
         

    }

    void run(Event event, DataPackage& data) override {

         
        
        gaitplanner->GetDataFromPackage(data);

        gaitplanner->Walk();

        gaitplanner->SetDataToPackage(data);

        // std::cout<< "gaitplanner->SetDataToPackage:"<<data.IfWalkStart<<std::endl;

        auto record_start_time1 = std::chrono::steady_clock::now();

        pino->GetDataFromPackage(data);

        pino->compute_generalized_q_dq_desired(State::WALKING);

        pino->SetDataToPackage(data);

        auto record_now_time1 = std::chrono::steady_clock::now();
        std::chrono::duration<double> elapsed_record1 = record_now_time1 - record_start_time1;
        std::cout<< "pino time:"<<elapsed_record1.count()<<std::endl;
        
        // WBC_Solver::WeightedWBCSolver wbc(data);
        wbc->GetDataFromPackage(data);

        wbc->Step();

        wbc->SetDataToPackage(data);

        // std::cout<< "wbc->SetDataToPackage:"<<data.IfWalkStart<<std::endl;

    }




    void exit(Event event, DataPackage& data) override {


    }


private: 
    std::unique_ptr<WBC_Solver::WeightedWBCSolver> wbc;  // 使用 unique_ptr 延迟创建
    std::unique_ptr<GaitPlanner> gaitplanner;  // 使用 unique_ptr 延迟创建
    std::unique_ptr<Pino_Kinematics> pino; 

};