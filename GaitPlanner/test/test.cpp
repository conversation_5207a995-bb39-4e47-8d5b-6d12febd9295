include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/eigen3)

include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/pinocchio/include)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/yaml/include)
include_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/urdfdom/include)


link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/pinocchio/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/yaml/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/urdfdom/lib)
link_directories(${PROJECT_SOURCE_DIR}/../ThirdParty/boost/lib)


aux_source_directory(${PROJECT_SOURCE_DIR}/src dir_datapackage)