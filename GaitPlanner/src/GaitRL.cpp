#include "../include/GaitRL.h"

GaitRL::GaitRL() {}

GaitRL::~GaitRL() {}

void GaitRL::init(double _dt, DataPackage *data) {

    command_scales[0] = obs_scales_lin_vel;
    command_scales[1] = obs_scales_lin_vel;
    command_scales[2] = obs_scales_ang_vel;
    default_dof_pos = Eigen::VectorXd::Zero(action_num);
    default_dof_pos << -0.345, 0.0, 0.0, 0.631, -0.341, 0,
    -0.345, 0.0, 0.0, 0.631, -0.341, 0,
    0.0, 
    0.0, 0.0, 0.0, 0.0, 0.0, 0.0;

    action_last = Eigen::VectorXd::Zero(action_num);

    command.setZero();

    inputdata_nlp = Eigen::VectorXd::Zero(obs_num);
    inputdata_nlp_encoder = Eigen::VectorXd::Zero(encoder_num);
    outputdata_nlp = Eigen::VectorXd::Zero(action_num);
    nlpout = Eigen::VectorXd::Zero(action_num);
    outputdata_nlp_encoder = Eigen::VectorXd::Zero(latent_num);

    history_pos = Eigen::VectorXd::Zero(action_num * his_len);
    history_vel = Eigen::VectorXd::Zero(action_num * his_len);
    history_action = Eigen::VectorXd::Zero(action_num * his_len);
    history_gravity = Eigen::VectorXd::Zero(3 * his_len);
    history_angvel = Eigen::VectorXd::Zero(3 * his_len);
    history_pos_obs = Eigen::VectorXd::Zero(action_num * his_len_obs);
    history_vel_obs = Eigen::VectorXd::Zero(action_num * his_len_obs);

    history_encoder_obs = Eigen::VectorXd::Zero(encoder_num);


    nlpmodel = new NlpModel();
    nlpmodel->init(model_pb, encoder_pb, obs_num, encoder_num, action_num, latent_num, lstm_layers, lstm_hidden_size);

    CPGInit(4, 0.01, float(1.0 / 0.8));
    CPGReset(true);
}


void GaitRL::CPGInit(int num_feet, float dt, float frequency) {
    step_dt = dt;
    gait_freqency = frequency;

    cycle_r = Eigen::Vector4d::Ones();
    px = Eigen::Vector4d::Ones();
    py = Eigen::Vector4d::Zero();

    std::cout << " init px: " << px.transpose() << std::endl;
    std::cout << " init py: " << py.transpose() << std::endl;

    // px = Eigen::VectorXd::Zero(num_feet);
    // py = Eigen::VectorXd::Ones(num_feet);

    phase_offset = Eigen::VectorXd::Zero(num_feet);
    phase_offset_target = Eigen::VectorXd::Zero(num_feet);
    phase_offset_change_rate = 0.01 * (step_dt / 0.002);

    std::cout << " phase_offset_change_rate: " << phase_offset_change_rate << std::endl;

    CPGSetPhaseOffset(phase_offset);

    phase_offset_stand << 0.0, (0.0) * M_PI, 0.0, (0.0) * M_PI;
    phase_offset_walk << 0.0, (1.0) * M_PI, (-0.15) * M_PI, (-1.15) * M_PI;
    phase_offset_run << 0.0, (1.0) * M_PI, (-0.16) * M_PI, (-1.16) * M_PI;

    coupling_change_rate = Eigen::Vector4d::Ones(num_feet) * COUPLING_CHANGE_RATE_INCREASE_BASE;
    coupling_change_rate_increase = 1.0 * COUPLING_CHANGE_RATE_INCREASE_BASE;
    coupling_change_rate_decrease = 1.0 * COUPLING_CHANGE_RATE_DECREASE_BASE;

    amplitude_change_rate = Eigen::Vector4d::Ones(num_feet);
    amplitude_change_rate_increase = std::pow(1.0 * AMPLITUDE_CHANGE_RATE_INCREASE_BASE, (step_dt / 0.002));
    amplitude_change_rate_decrease = std::pow(1.0 * AMPLITUDE_CHANGE_RATE_DECREASE_BASE, (step_dt / 0.002));

    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.5);

}

Eigen::VectorXd GaitRL::CPGGammaCal() {
    Eigen::VectorXd term1 = Eigen::VectorXd::Zero(num_feet);
    Eigen::VectorXd term2 = Eigen::VectorXd::Zero(num_feet);
    for (int i = 0; i < num_feet; i++) {
        term1(i) = M_PI / ((1 - contact_ratio(i)) * (std::exp((-1) * coff_b * py(i)) + 1));
        term2(i) = M_PI / ((0 + contact_ratio(i)) * (std::exp(coff_b * py(i)) + 1));

    }
    return term1 + term2;
}

Eigen::MatrixXd GaitRL::CPGCoupling() {
    Eigen::VectorXd coupling_x = Eigen::VectorXd::Zero(num_feet);
    Eigen::VectorXd coupling_y = Eigen::VectorXd::Zero(num_feet);
    double coupling_x_ij = 0.0;
    double coupling_y_ij = 0.0;
    double r_i = 0.0;
    double r_j = 0.0;
    
    for (int i = 0; i < num_feet; i++) {
        coupling_x(i) = 0.0;
        coupling_y(i) = 0.0;
        for (int j = 0; j < num_feet; j++) {
            if (i == j) {
                continue;
            }
            coupling_x_ij = std::cos(phase_offset(i) - phase_offset(j)) * px(j) 
                         - std::sin(phase_offset(i) - phase_offset(j)) * py(j);
            coupling_y_ij = std::sin(phase_offset(i) - phase_offset(j)) * px(j) 
                         + std::cos(phase_offset(i) - phase_offset(j)) * py(j);
            
            r_i = std::sqrt(std::pow(px(i), 2) + std::pow(py(i), 2));
            r_j = std::sqrt(std::pow(px(j), 2) + std::pow(py(j), 2));
            
            coupling_x_ij = coupling_x_ij / r_j * r_i;
            coupling_y_ij = coupling_y_ij / r_j * r_i;
            
            coupling_x(i) += coupling_x_ij;
            coupling_y(i) += coupling_y_ij;
        }
    }
    
    Eigen::MatrixXd result(num_feet, 2);
    result.col(0) = coupling_x;
    result.col(1) = coupling_y;
    return result;
}

void GaitRL::CPGPhaseChange() {
    phase_offset += phase_offset_change_rate * (phase_offset_target - phase_offset);
}

void GaitRL::CPGHopfOscillator() {
    Eigen::Vector4d r;
    // std::cout << "r: " << r.transpose() << std::endl;
    for (int i = 0; i < num_feet; i++) {
        r(i) = std::sqrt(std::pow(px(i), 2) + std::pow(py(i), 2));
    }
    
    Eigen::Vector4d gamma = CPGGammaCal();
    double alpha = gait_freqency * rate;
    Eigen::Vector4d omega = gait_freqency * gamma;
    
    CPGPhaseChange();
    Eigen::MatrixXd coupling_xy = CPGCoupling();
    Eigen::Vector4d coupling_x = coupling_xy.col(0);
    Eigen::Vector4d coupling_y = coupling_xy.col(1);

    Eigen::Vector4d dx;
    Eigen::Vector4d dy;
    
    for (int i = 0; i < num_feet; i++) {
        dx(i) = (alpha * (std::pow(cycle_r(i), 2) - std::pow(r(i), 2)) * px(i) - omega(i) * py(i)) 
              + coupling_change_rate(i) * coupling_x(i);
        dy(i) = (alpha * (std::pow(cycle_r(i), 2) - std::pow(r(i), 2)) * py(i) + omega(i) * px(i)) 
              + coupling_change_rate(i) * coupling_y(i);

        px(i) = px(i) + dx(i) * step_dt;
        py(i) = py(i) + dy(i) * step_dt;

        if (r(i) > cycle_r_min) {
            px(i) = px(i) * amplitude_change_rate(i);
            py(i) = py(i) * amplitude_change_rate(i);
        }

        r(i) = std::sqrt(std::pow(px(i), 2) + std::pow(py(i), 2));
        if (r(i) > cycle_r_max) {
            px(i) = px(i) / r(i);
            py(i) = py(i) / r(i);
        }
    }
}


void GaitRL::CPGReset(bool flag) {
    if(flag) {
        px << std::cos(0.5*M_PI), std::cos(0.5*M_PI), std::cos(0.5*M_PI), std::cos(0.5*M_PI);
        py << std::sin(0.5*M_PI), std::sin(0.5*M_PI), std::sin(0.5*M_PI), std::sin(0.5*M_PI);
    } else {
        px << std::cos(0.5*M_PI), std::cos(0.5*M_PI), std::cos(0.5*M_PI), std::cos(0.5*M_PI);
        py << std::sin(0.5*M_PI), std::sin(0.5*M_PI), std::sin(0.5*M_PI), std::sin(0.5*M_PI);        
    }
    CPGSetPhaseOffset(phase_offset_target);
}

void GaitRL::CPGSetCycleR(Eigen::VectorXd Cycle_r) {
    cycle_r = Cycle_r;
}

void GaitRL::CPGSetCouplingChangeRate(Eigen::VectorXd Coupling_change_rate) {
    coupling_change_rate = Coupling_change_rate;
}

void GaitRL::CPGSetAmplitudeChangeRate(Eigen::VectorXd Amplitude_change_rate) {
    amplitude_change_rate = Amplitude_change_rate;
}

void GaitRL::CPGSetPhaseOffset(Eigen::VectorXd Phase_offset) {
    phase_offset_target = Phase_offset;
}

void GaitRL::CPGSetContactRatio(Eigen::VectorXd Contact_ratio) {
    contact_ratio = Contact_ratio;
}

void GaitRL::CPGSetFrequency(double frequency) {
    gait_freqency = frequency;
}


void GaitRL::CPGSetStandPattern() {
    CPGSetFrequency((1 / 1.0));
    CPGSetCycleR(Eigen::Vector4d::Ones(4) * cycle_r_min);
    CPGSetCouplingChangeRate(Eigen::Vector4d::Ones(4) * coupling_change_rate_decrease);
    CPGSetAmplitudeChangeRate(Eigen::Vector4d::Ones(4) * amplitude_change_rate_decrease);
    CPGSetPhaseOffset(phase_offset_stand);
    CPGSetContactRatio(Eigen::Vector4d::Ones(4) * 0.99);
    stand_flag = true;
    walk_flag = false;
    // run_flag = false;

}

void GaitRL::CPGSetWalkPattern_1() {
    CPGSetFrequency((1 / 0.8));
    CPGSetCycleR(Eigen::VectorXd::Ones(4) * cycle_r_max);
    CPGSetCouplingChangeRate(Eigen::VectorXd::Ones(4) * coupling_change_rate_increase);
    CPGSetAmplitudeChangeRate(Eigen::VectorXd::Ones(4) * amplitude_change_rate_increase);
    CPGSetPhaseOffset(phase_offset_walk);
    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.6);
    walk_flag = true;
    if ((walk_flag) &&(stand_flag))
    {
        CPGReset(false);
    }
    stand_flag = false;
    // run_flag = false;
    // std::cout << "CPGSetWalkPattern" << std::endl;
}
void GaitRL::CPGSetWalkPattern_2() {
    CPGSetFrequency((1 / 0.8));
    CPGSetCycleR(Eigen::VectorXd::Ones(4) * cycle_r_max);
    CPGSetCouplingChangeRate(Eigen::VectorXd::Ones(4) * coupling_change_rate_increase);
    CPGSetAmplitudeChangeRate(Eigen::VectorXd::Ones(4) * amplitude_change_rate_increase);
    CPGSetPhaseOffset(phase_offset_walk);
    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.55);
    walk_flag = true;
    if ((walk_flag) &&(stand_flag))
    {
        CPGReset(false);
    }
    stand_flag = false;
    // run_flag = false;
    // std::cout << "CPGSetWalkPattern" << std::endl;
}
void GaitRL::CPGSetWalkPattern_3() {
    CPGSetFrequency((1 / 0.7));
    CPGSetCycleR(Eigen::VectorXd::Ones(4) * cycle_r_max);
    CPGSetCouplingChangeRate(Eigen::VectorXd::Ones(4) * coupling_change_rate_increase);
    CPGSetAmplitudeChangeRate(Eigen::VectorXd::Ones(4) * amplitude_change_rate_increase);
    CPGSetPhaseOffset(phase_offset_walk);
    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.5);
    walk_flag = true;
    if ((walk_flag) &&(stand_flag))
    {
        CPGReset(false);
    }
    stand_flag = false;
    // run_flag = false;
}
void GaitRL::CPGSetWalkPattern_4() {
    CPGSetFrequency((1 / 0.7));
    CPGSetCycleR(Eigen::VectorXd::Ones(4) * cycle_r_max);
    CPGSetCouplingChangeRate(Eigen::VectorXd::Ones(4) * coupling_change_rate_increase);
    CPGSetAmplitudeChangeRate(Eigen::VectorXd::Ones(4) * amplitude_change_rate_increase);
    CPGSetPhaseOffset(phase_offset_walk);
    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.45);
    walk_flag = true;
    if ((walk_flag) &&(stand_flag))
    {
        CPGReset(false);
    }
    stand_flag = false;
    // run_flag = false;
}
void GaitRL::CPGSetWalkPattern_5() {
    CPGSetFrequency((1 / 0.6));
    CPGSetCycleR(Eigen::VectorXd::Ones(4) * cycle_r_max);
    CPGSetCouplingChangeRate(Eigen::VectorXd::Ones(4) * coupling_change_rate_increase);
    CPGSetAmplitudeChangeRate(Eigen::VectorXd::Ones(4) * amplitude_change_rate_increase);
    CPGSetPhaseOffset(phase_offset_walk);
    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.4);
    walk_flag = true;
    if ((walk_flag) &&(stand_flag))
    {
        CPGReset(false);
    }
    stand_flag = false;
    // run_flag = false;
}
void GaitRL::CPGSetWalkPattern_6() {
    CPGSetFrequency((1 / 0.5));
    CPGSetCycleR(Eigen::VectorXd::Ones(4) * cycle_r_max);
    CPGSetCouplingChangeRate(Eigen::VectorXd::Ones(4) * coupling_change_rate_increase);
    CPGSetAmplitudeChangeRate(Eigen::VectorXd::Ones(4) * amplitude_change_rate_increase);
    CPGSetPhaseOffset(phase_offset_walk);
    CPGSetContactRatio(Eigen::VectorXd::Ones(4) * 0.35);
    walk_flag = true;
    if ((walk_flag) &&(stand_flag))
    {
        CPGReset(false);
    }
    stand_flag = false;
    // run_flag = false;
}

Eigen::VectorXd GaitRL::CPGGetXNorm() {
    Eigen::Vector4d r(num_feet);
    Eigen::Vector4d x_norm(num_feet);
    for (int i = 0; i < num_feet; i++) {
        r(i) = std::sqrt(std::pow(px(i), 2) + std::pow(py(i), 2));
        x_norm(i) = (r(i) > cycle_r_min) ? px(i) : 0.0;
    }
    return x_norm;
}

Eigen::VectorXd GaitRL::CPGGetYNorm() {
    Eigen::Vector4d r(num_feet);
    Eigen::Vector4d y_norm(num_feet);
    for (int i = 0; i < num_feet; i++) {
        r(i) = std::sqrt(std::pow(px(i), 2) + std::pow(py(i), 2));
        y_norm(i) = (r(i) > cycle_r_min) ? py(i) : 0.0;
    }
    return y_norm;
}


void GaitRL::UpdateGaitGeneratorPattern(Eigen::VectorXd Commands, double mode) {
    // std::cout << "command: " << Commands.transpose() << std::endl;
    double vel_norm = Commands.norm();
    if (mode == 0 || vel_norm < 0.05)
    {
        // std::cout << "CPGSetStandPattern" << std::endl;
        CPGSetStandPattern();
    }

    if (vel_norm >= 0.05 and vel_norm < 0.5 and mode == 1) 
    {
        // std::cout << "CPGSetWalkPattern_1" << std::endl;
        CPGSetWalkPattern_1();
    }
    else if (vel_norm >= 0.5 and vel_norm < 1.0 and mode == 1) 
    {
        // std::cout << "CPGSetWalkPattern_2" << std::endl;
        CPGSetWalkPattern_2();
    }
    else if (vel_norm >= 1.0 and vel_norm < 1.5 and mode == 1) 
    {
        // std::cout << "CPGSetWalkPattern_3" << std::endl;
        CPGSetWalkPattern_3();
    }
    else if (vel_norm >= 1.5 and vel_norm < 2.0 and mode == 1)
    {
        CPGSetWalkPattern_4();
    }
    else if (vel_norm >= 2.0 and vel_norm < 2.5 and mode == 1)
    {
        CPGSetWalkPattern_5();
    }
    else if (vel_norm >= 2.5 and vel_norm < 3.0 and mode == 1)
    {
        CPGSetWalkPattern_6();
    }
}

void GaitRL::CommandRefinement() {

    Eigen::VectorXd Commands_buf = Eigen::VectorXd::Zero(3);
    Eigen::VectorXd last_Commands_buf ;

}
