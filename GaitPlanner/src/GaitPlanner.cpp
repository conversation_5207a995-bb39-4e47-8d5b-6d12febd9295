#include "GaitPlanner/include/GaitPlanner.h"
#include <iostream>
#include <fstream>
#include <cmath>
// #include "tinysplinecxx.h"


GaitPlanner::Gait<PERSON>lanner(DataPackage &DataPackage, std::string state)
{
    // zero_position.setZero(DataPackage.actuatedDofNum);
    // zero_position<<-0.2,0,0,0.4,-0.2,0,-0.2,0,0,0.4,-0.2,0;
    Gait_state = state;

    std::string file_path = "../GaitPlanner/config/" + state + ".yaml";
    std::cout<<"cc: "<<file_path<<std::endl;

    loadConfig(file_path);    
    // std::cout<<"T_jump: "<<T_jump<<std::endl;
    lambda = std::sqrt(g/(com_height-foot_height)); 
    T = T_DSP+T_SSP;

    // std::cout << "GaitPlanner " << std::endl;
}


void GaitPlanner::loadConfig(const std::string& filename)
{
    // std::cout << "filename: " << filename << std::endl;
    try {
        // std::cout << "filename: " << filename << std::endl;
        // Load the YAML file
        YAML::Node config = YAML::LoadFile(filename);
        // std::cout << "filename: " << filename << std::endl;  

        // Read values from YAML file and assign to class members
        if (config["foot_height"]) {
            foot_height = config["foot_height"].as<double>();
        }
        if (config["base_heignt"]) {
            base_heignt = config["base_heignt"].as<double>();
        }
        if (config["uy_l_ref"]) {
            uy_l_ref = config["uy_l_ref"].as<double>();
        }
        if (config["uy_r_ref"]) {
            uy_r_ref = config["uy_r_ref"].as<double>(); 
        }

        stand_offset_x = config["stand_offset_x"].as<double>(); 

        if(Gait_state == "WALKING"||Gait_state == "FLYING"){

            if (config["T_DSP"]) {
                T_DSP = config["T_DSP"].as<double>();
            }
            if (config["T_SSP"]) {
                T_SSP = config["T_SSP"].as<double>();
            }
            if (config["step_height"]) {
                step_height = config["step_height"].as<double>();
            }
            if (config["g"]) {
                g = config["g"].as<double>(); 
            }
    
            contact_force_start_threshold = config["contact_force_start_threshold"].as<double>(); 
            contact_force_change_threshold = config["contact_force_change_threshold"].as<double>(); 
            contact_velocity = config["contact_velocity"].as<double>(); 
            contact_check_time = config["contact_check_time"].as<double>(); 
            T_jump = config["T_jump"].as<double>(); 
            jump_height = config["jump_height"].as<double>(); 
            fall_velocity = config["fall_velocity"].as<double>(); 
            up_velocity = config["up_velocity"].as<double>(); 
            com_height = config["com_height"].as<double>();
            feedback_factor_vy = config["feedback_factor_vy"].as<double>();
            feedback_factor_vx = config["feedback_factor_vx"].as<double>();
    
            leftarm_joint_behind = parseVector(config["leftarm_joint_behind"]);
            leftarm_joint_front   = parseVector(config["leftarm_joint_front"]);
            rightarm_joint_behind = parseVector(config["rightarm_joint_behind"]);
            rightarm_joint_front  = parseVector(config["rightarm_joint_front"]);
    
            ux_threshold = config["ux_threshold"].as<double>();
            vx_threshold = config["vx_threshold"].as<double>();
            uy_threshold = config["uy_threshold"].as<double>();
    
            Kp_vy = config["Kp_vy"].as<double>();
    
            pre_swing_time = config["pre_swing_time"].as<double>();
    
            u_B_offset_x = config["u_B_offset_x"].as<double>();
    
            t_afterImpact = config["t_afterImpact"].as<double>();
            t_beforeImpact = config["t_beforeImpact"].as<double>();

            leftArm_file_path = config["leftArm_file_path"].as<std::string>();
            rightArm_file_path = config["rightArm_file_path"].as<std::string>();

            leftArm_joint_pos = readCSV(leftArm_file_path);
            rightArm_joint_pos = readCSV(rightArm_file_path);

            foot_distance_max = config["foot_distance_max"].as<double>();
            foot_EulerY_max = config["foot_EulerY_max"].as<double>();

            foot_yaw_offset = config["foot_yaw_offset"].as<double>();

            step_length_max_X = config["step_length_max_X"].as<double>();
            step_length_max_Y = config["step_length_max_Y"].as<double>();
            step_length_min_Y = config["step_length_min_Y"].as<double>();

            // std::cout << "Loaded leftArm_file_path:\n" << leftArm_file_path << std::endl;
            // std::cout << "Loaded rightArm_file_path:\n" << rightArm_file_path << std::endl;

            // std::cout << "Loaded leftSwing_joint_pos:\n" << leftArm_joint_pos << std::endl;
            // std::cout << "Loaded rightSwing_joint_pos:\n" << rightArm_joint_pos << std::endl;


        }





    } catch (const YAML::Exception& e) {
        std::cerr << "Error loading YAML file: " << e.what() << std::endl;
    }

}
void GaitPlanner::GetDataFromPackage(DataPackage &DataPackage)
{
    // data.generalized_q_desired.tail(data.actuatedDofNum) = zero_position;
    dt = DataPackage.control_period;
    T_START = DataPackage.T_START;
    t = DataPackage.t;
    FootNum = DataPackage.FootNum;
    feet_r_Pos_PhaseStart_W = DataPackage.feet_r_Pos_PhaseStart_W;
    feet_l_Pos_PhaseStart_W = DataPackage.feet_l_Pos_PhaseStart_W;

    base_Pos_actual = DataPackage.generalized_q_actual.segment(0, 3);
    base_LinVel_actual = DataPackage.generalized_q_dot_actual.segment(0, 3);
    feet_r_Pos_W_actual = DataPackage.feet_r_Pos_W_actual;
    feet_l_Pos_W_actual = DataPackage.feet_l_Pos_W_actual;
    contact_force = DataPackage.contact_force;
    contact_force_history = DataPackage.contact_force_history;

    contact_flag = DataPackage.contact_flag;

    gait_vx_desire = DataPackage.gait_vx_desire;

    arm_l_JointPos_actual = DataPackage.generalized_q_actual.segment(18, 5);
    arm_r_JointPos_actual = DataPackage.generalized_q_actual.segment(23, 5);
    arm_l_JointPos_GaitStart = DataPackage.arm_l_JointPos_GaitStart;
    arm_r_JointPos_GaitStart = DataPackage.arm_r_JointPos_GaitStart;

    IfFirstStand = DataPackage.IfFirstStand;
    IfWalkStart = DataPackage.IfWalkStart;
    IfGaitEnd = DataPackage.IfGaitEnd;
    IfStand2Walk = DataPackage.IfStand2Walk;
    IfCanStop = DataPackage.IfCanStop;
    IfFly2Wlak = DataPackage.IfFly2Wlak;

    feet_r_Pos_StandStart_W = DataPackage.feet_r_Pos_StandStart_W;
    feet_l_Pos_StandStart_W = DataPackage.feet_l_Pos_StandStart_W;
    base_Pos_StandStart_W = DataPackage.base_Pos_StandStart_W;

    speed_forward_history = DataPackage.speed_forward_history;

    gait_OmegaZ_desire = DataPackage.gait_OmegaZ_desire;
    base_EulerZ_actual = DataPackage.generalized_q_actual[3];
    // base_OmegaZ_actual = DataPackage.generalized_q_dot_actual[3];
    feet_l_EulerZ_W_actual = DataPackage.feet_l_EulerZ_W_actual;
    feet_r_EulerZ_W_actual = DataPackage.feet_r_EulerZ_W_actual;

    feet_r_EulerZ_PhaseStart_W = DataPackage.feet_r_EulerZ_PhaseStart_W;
    feet_l_EulerZ_PhaseStart_W = DataPackage.feet_l_EulerZ_PhaseStart_W;
    base_EulerZ_PhaseStart = DataPackage.base_EulerZ_PhaseStart;

    gait_vy_desire = DataPackage.gait_vy_desire;

    vy_Final = DataPackage.vy_Final;
    vy_ref = DataPackage.vy_ref;

    isWalking = DataPackage.isWalking;

    base_Pos_PhaseStart = DataPackage.base_Pos_PhaseStart;

    stand_up_pos = DataPackage.stand_up_pos;
    stand_forward_pos = DataPackage.stand_forward_pos;
    stand_left_pos = DataPackage.stand_left_pos;
    stand_yaw_pos = DataPackage.stand_yaw_pos;

    stand_up_vel = DataPackage.stand_up_vel;



}
void GaitPlanner::SetDataToPackage(DataPackage &DataPackage)
{
    //DataPackage.generalized_q_desired.tail(DataPackage.actuatedDofNum) = zero_position;

    DataPackage.base_Pos_desire = base_Pos_desire;// + Eigen::Vector3d(0.01, 0, 0);

    DataPackage.base_LinVel_desire = base_LinVel_desire;
    DataPackage.feet_r_Pos_W_desire = feet_r_Pos_W_desire;
    DataPackage.feet_l_Pos_W_desire = feet_l_Pos_W_desire;
    DataPackage.feet_r_LinVel_W_desire = feet_r_LinVel_W_desire;
    DataPackage.feet_l_LinVel_W_desire = feet_l_LinVel_W_desire;

    DataPackage.swingFactor = swingFactor;
    DataPackage.swingR = swingR;
    DataPackage.swingL = swingL;

    DataPackage.t = t;
    DataPackage.T_START = T_START;
    DataPackage.FootNum = FootNum;
    DataPackage.feet_r_Pos_PhaseStart_W = feet_r_Pos_PhaseStart_W;
    DataPackage.feet_l_Pos_PhaseStart_W = feet_l_Pos_PhaseStart_W;

    DataPackage.contact_flag = contact_flag;

    DataPackage.arm_l_JointPos_GaitStart = arm_l_JointPos_GaitStart;
    DataPackage.arm_r_JointPos_GaitStart = arm_r_JointPos_GaitStart;

    DataPackage.arm_l_JointPos_desire = arm_l_JointPos_desire;
    DataPackage.arm_r_JointPos_desire = arm_r_JointPos_desire;  
    DataPackage.arm_l_JointVel_desire = arm_l_JointVel_desire;
    DataPackage.arm_r_JointVel_desire = arm_r_JointVel_desire;  

    DataPackage.IfFirstStand = IfFirstStand;
    DataPackage.IfWalkStart = IfWalkStart;
    DataPackage.IfGaitEnd = IfGaitEnd;
    DataPackage.IfStand2Walk = IfStand2Walk;
    DataPackage.IfCanStop = IfCanStop;
    DataPackage.IfFly2Wlak = IfFly2Wlak;

    DataPackage.feet_r_Pos_StandStart_W = feet_r_Pos_StandStart_W;
    DataPackage.feet_l_Pos_StandStart_W = feet_l_Pos_StandStart_W;
    DataPackage.base_Pos_StandStart_W = base_Pos_StandStart_W;

    DataPackage.speed_forward_avg = speed_forward_avg;

    DataPackage.feet_r_EulerZ_PhaseStart_W = feet_r_EulerZ_PhaseStart_W;
    DataPackage.feet_l_EulerZ_PhaseStart_W = feet_l_EulerZ_PhaseStart_W;
    DataPackage.base_EulerZ_PhaseStart = base_EulerZ_PhaseStart;

    DataPackage.base_EulerZ_desire = base_EulerZ_desire;
    DataPackage.base_OmegaZ_desire = base_OmegaZ_desire;
    DataPackage.feet_l_EulerZ_W_desire = feet_l_EulerZ_W_desire;
    DataPackage.feet_l_OmegaZ_W_desire = feet_l_OmegaZ_W_desire;
    DataPackage.feet_r_EulerZ_W_desire = feet_r_EulerZ_W_desire;
    DataPackage.feet_r_OmegaZ_W_desire = feet_r_OmegaZ_W_desire;

    DataPackage.feet_l_EulerY_W_desire = feet_l_EulerY_W_desire;
    DataPackage.feet_r_EulerY_W_desire = feet_r_EulerY_W_desire;
    DataPackage.feet_l_OmegaY_W_desire = feet_l_OmegaY_W_desire;
    DataPackage.feet_r_OmegaY_W_desire = feet_r_OmegaY_W_desire;

    DataPackage.u_B = u_B;
    DataPackage.u_W = u_W;
    DataPackage.P_B = P_B;
    DataPackage.V_B = V_B;
    DataPackage.P_W = P_W;
    DataPackage.V_W = V_W;
    DataPackage.vx_Final = vx_Final;
    DataPackage.vy_Final = vy_Final;
    DataPackage.vy_ref = vy_ref;

    DataPackage.isWalking = isWalking;

    DataPackage.base_Pos_PhaseStart = base_Pos_PhaseStart;
    DataPackage.waist_pos_desire = waist_pos_desire;

}

void GaitPlanner::RotationByBase(){

    base_EulerZ_desire = base_EulerZ_actual + gait_OmegaZ_desire * dt;

}

void GaitPlanner::Stand()
{
    updateContactFlags("ALL", contact_flag);

    if(isWalking){
        isWalking = false;
    }

    if(IfFirstStand){
        feet_r_Pos_StandStart_W = Eigen::Vector3d(-0.08, -0.5*uy_r_ref, foot_height);
        feet_l_Pos_StandStart_W = Eigen::Vector3d(-0.08, -0.5*uy_l_ref, foot_height);
        base_Pos_StandStart_W = Eigen::Vector3d(-0.08, 0, base_heignt);
        RecordPhaseStartPos();
        IfFirstStand = false;
    }

    if(t<0.1){
        RecordPhaseStartPos();
    }
    
    base_Pos_desire = (feet_l_Pos_StandStart_W + feet_r_Pos_StandStart_W)/2;
    // base_Pos_desire[0] = 0.03;
    base_Pos_desire[0] += stand_forward_pos + stand_offset_x;
    base_Pos_desire[1] += stand_left_pos;
    base_Pos_desire[2] = base_heignt + stand_up_pos; 
    feet_r_Pos_W_desire = feet_r_Pos_StandStart_W;
    feet_r_Pos_W_desire[2] = foot_height; 
    feet_l_Pos_W_desire = feet_l_Pos_StandStart_W;  
    feet_l_Pos_W_desire[2] = foot_height; 

    // for(int i = 0; i<feet_r_Pos_W_desire.size(); ++i){
    //     std::cout << "feet_r_Pos_W_desire[" << i << "] = " << feet_r_Pos_W_desire[i] << std::endl;
    // }

    // for(int i = 0; i<feet_l_Pos_W_desire.size(); ++i){
    //     std::cout << "feet_l_Pos_W_desire[" << i << "] = " << feet_l_Pos_W_desire[i] << std::endl;
    // }

    base_LinVel_desire = Eigen::Vector3d(0, 0, stand_up_vel);
    feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);
    feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

    base_EulerZ_desire = base_EulerZ_PhaseStart;
    base_OmegaZ_desire = 0;
    feet_l_EulerZ_W_desire = feet_l_EulerZ_PhaseStart_W;
    feet_l_OmegaZ_W_desire = 0;
    feet_r_EulerZ_W_desire = feet_r_EulerZ_PhaseStart_W;
    feet_r_OmegaZ_W_desire = 0;

    feet_l_EulerY_W_desire = 0;
    feet_r_EulerY_W_desire = 0;
    feet_l_OmegaY_W_desire = 0;
    feet_r_OmegaY_W_desire = 0;


    arm_l_JointPos_desire.setZero();
    arm_r_JointPos_desire.setZero();
    arm_l_JointVel_desire.setZero();
    arm_r_JointVel_desire.setZero();
    waist_pos_desire = stand_yaw_pos;
    std::cout<<"waist_pos_desire:"<< waist_pos_desire<<std::endl;

    vx_Final = base_LinVel_actual[0];

    swingL = 0;
    swingR = 0;

    T_START = t;
    t = t + dt;
}

void GaitPlanner::Stand2Walk()
{
    if((IfWalkStart)){

        IfWalkStart = false;
        WalkStartTime = t;
        RecordPhaseStartPos();

    }
    
    if((t- WalkStartTime)<=pre_swing_time){

        updateContactFlags("ALL", contact_flag);
        FirstStep();
        T_START = t;
        FootNum = 1;
        RecordGaitStartArmJointPos();
        t = t + dt;
        // std::cout<< "WalkStartTime:"<<WalkStartTime<<std::endl;
    }else{
        IfStand2Walk = false;
    }

    arm_l_JointPos_desire.setZero();
    arm_r_JointPos_desire.setZero();
    arm_l_JointVel_desire.setZero();
    arm_r_JointVel_desire.setZero();
    waist_pos_desire = 0;

    swingL = 0;
    swingR = 0;

    vx_Final = base_LinVel_actual[0];

}

void GaitPlanner::getCurrentSpeedAsVx_Final()
{
    vx_Final = base_LinVel_actual[0];
}


void GaitPlanner::Walk()
{
    if(IfStand2Walk){

        Stand2Walk();
        return;
    }

    if(IfFly2Wlak){
        Fly2Walk();
        return;
    }

    if(!isWalking){
        isWalking = true;
    }

    t_con = t - T_START;  // Execution time
    // std::cout << "t_con:" << t_con << std::endl;yy
    if(t_con <= T_DSP && (t_con + dt > T_DSP)){
        RecordPhaseStartPos();
    }

    double vx_desire = gait_vx_desire;  // Target velocity in x direction
    double sigma1 = lambda / tanh(lambda * T_SSP / 2);
    double px_ref = vx_desire * T / (2 + T_DSP * sigma1);  // End state position
    double vx_ref = sigma1 * px_ref;

    double c1_sig_desire = 0.5*(-px_ref + vx_ref/lambda);
    double c2_sig_desire = 0.5*(-px_ref - vx_ref/lambda);

    // std::cout << "c1_sig_desire:" << c1_sig_desire<< " ";
    // std::cout << "c2_sig_desire:" << c2_sig_desire<< " ";
    // std::cout << std::endl;

    // Coronal plane        
    double sigma2 = lambda * tanh(lambda * T_SSP / 2);

    // if (t_con <= T_DSP) {  // Double support phase
    //     vy_control = gait_vy_desire + Kp_vy * (vy_ref - vy_Final);
    // } else {  // Single support phase
    //     vy_control = gait_vy_desire;
    // }

    vy_control = gait_vy_desire + Kp_vy * (vy_ref - vy_Final);

    double uy_l_crl = uy_l_ref + vy_control * T;
    double uy_r_crl = uy_r_ref + vy_control * T;

    std::cout << "t_con:" << t_con<< " ";
    std::cout << "vy_control:" << vy_control<< " ";
    std::cout << "uy_l_crl:" << uy_l_crl<< " ";
    std::cout << "uy_r_crl:" << uy_r_crl<< " ";
    std::cout << std::endl;

    double d2 = (lambda * lambda * (1 / (cosh(lambda * T_SSP / 2) * cosh(lambda * T_SSP / 2))) * T * vy_control) / (lambda * lambda * T_DSP + 2 * sigma2);
    double py_l_ref = (uy_l_crl - T_DSP * d2) / (2 + T_DSP * sigma2);
    double vy_l_ref = (sigma2 * py_l_ref + d2);
    double py_r_ref = (uy_r_crl - T_DSP * d2) / (2 + T_DSP * sigma2);
    double vy_r_ref = (sigma2 * py_r_ref + d2);

    double c1_l_cor_desire = 0.5*(py_l_ref + (-vy_l_ref)/lambda);
    double c2_l_cor_desire = 0.5*(py_l_ref - (-vy_l_ref)/lambda);
    double c1_r_cor_desire = 0.5*(py_r_ref + (-vy_r_ref)/lambda);
    double c2_r_cor_desire = 0.5*(py_r_ref - (-vy_r_ref)/lambda);

    double c1_cor_desire, c2_cor_desire;

    // Get state variables
    Eigen::Vector2d P_l_last_W = base_Pos_actual.head(2) - feet_l_Pos_W_actual.head(2);
    Eigen::Vector2d P_r_last_W = base_Pos_actual.head(2) - feet_r_Pos_W_actual.head(2);
    Eigen::Vector2d V_last_W = base_LinVel_actual.head(2);

    Eigen::Matrix2d Rotation_W2B = rotation_matrix_plane(-base_EulerZ_actual);
    Eigen::Vector2d P_l_last_B = Rotation_W2B * P_l_last_W;
    Eigen::Vector2d P_r_last_B = Rotation_W2B * P_r_last_W;
    Eigen::Vector2d V_last_B = Rotation_W2B * V_last_W;
    
    Eigen::Matrix2d Rotation_B2W = rotation_matrix_plane(base_EulerZ_actual);

    std::cout << "V_last_B[1]:" << V_last_B[1]<< " ";

    // Gait planning
    if (t_con <= T_DSP) {  // Double support phase

        updateContactFlags("ALL", contact_flag);

        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg 
            // vy_ref = vy_l_ref;  
            vy_ref = -vy_r_ref; 
        } 
        else {  // Right leg is the swing leg, left leg is the support leg
            // vy_ref = vy_r_ref; 
            vy_ref = -vy_l_ref;  
        }   

        V_B << vx_ref, vy_ref;
        
        V_W = Rotation_B2W * V_B;

        vx_Final = V_last_B[0];
        vy_Final = V_last_B[1];

        std::cout << "vx_Final:" << vx_Final<< " ";
        std::cout << "vx_ref:" << vx_ref<< " ";
        std::cout << "vy_Final:" << vy_Final<< " ";
        std::cout << "vy_ref:" << vy_ref<< " ";
        std::cout << std::endl;

        base_Pos_desire.head<2>() = base_Pos_actual.head<2>() + V_W * dt;
        base_Pos_desire[2] = base_heignt; 
        feet_r_Pos_W_desire = feet_r_Pos_W_actual;
        feet_l_Pos_W_desire = feet_l_Pos_W_actual;
        feet_r_Pos_W_desire[2] = foot_height;
        feet_l_Pos_W_desire[2] = foot_height;

        base_LinVel_desire.head<2>() = V_W;  
        base_LinVel_desire[2] = 0; 
        feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);
        feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

        base_EulerZ_desire = base_EulerZ_actual + gait_OmegaZ_desire * dt;
        base_OmegaZ_desire = gait_OmegaZ_desire;
        feet_l_EulerZ_W_desire = feet_l_EulerZ_PhaseStart_W;
        feet_l_OmegaZ_W_desire = 0;
        feet_r_EulerZ_W_desire = feet_r_EulerZ_PhaseStart_W;
        feet_r_OmegaZ_W_desire = 0;

        feet_l_EulerY_W_desire = 0;
        feet_r_EulerY_W_desire = 0;
        feet_l_OmegaY_W_desire = 0;
        feet_r_OmegaY_W_desire = 0;

        // base_Pos_desire.head<2>() = base_Pos_actual.head<2>() + base_LinVel_actual.head<2>() * dt;
        // base_LinVel_desire.head<2>() = base_LinVel_actual.head<2>();  

        swingFactor = t_con / T_DSP;
        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg
            swingL = swingFactor;
            swingR = 1 - swingFactor;
        } 
        else {  // Right leg is the swing leg, left leg is the support leg
            swingR = swingFactor;
            swingL = 1 - swingFactor;
        }  



    } else {  // Single support phase
        double t_sp = (t_con - T_DSP + dt) / T_SSP;  // Time inside SSP next step
        if (t_sp > 1) t_sp = 1;

        double t_sp_last = (t_con - T_DSP) / T_SSP;  
        if (t_sp_last > 1) t_sp_last = 1;
        
        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg
            px_support = P_r_last_B[0]; 
            py_support = P_r_last_B[1];  
            py_ref = py_r_ref;  
            vy_ref = vy_r_ref;  
            c1_cor_desire = c1_r_cor_desire;
            c2_cor_desire = c2_r_cor_desire;

            updateContactFlags("R", contact_flag);

        } 
        else {  // Right leg is the swing leg, left leg is the support leg
            px_support = P_l_last_B[0]; 
            py_support = P_l_last_B[1];  
            py_ref = py_l_ref;  
            vy_ref = vy_l_ref; 
            c1_cor_desire = c1_l_cor_desire;
            c2_cor_desire = c2_l_cor_desire;

            updateContactFlags("L", contact_flag);
        }    

        // Sagittal plane planning
        double p_sig_last = px_support;  
        double v_sig_last = V_last_B[0]; 

        double E_last_s = std::exp(lambda * (t_sp_last * T_SSP));
        double c1_sig_last = (p_sig_last / (2 * E_last_s)) + (v_sig_last / (2 * lambda * E_last_s));
        double c2_sig_last = (p_sig_last * E_last_s / 2) - (v_sig_last * E_last_s / (2 * lambda));

        // P_B[0] = c1_sig_last * std::exp(lambda * (t_sp * T_SSP)) + c2_sig_last * std::exp(-lambda * (t_sp * T_SSP));
        // V_B[0] = lambda * (c1_sig_last * std::exp(lambda * (t_sp * T_SSP)) - c2_sig_last * std::exp(-lambda * (t_sp * T_SSP)));

        P_B[0] = c1_sig_desire * std::exp(lambda * (t_sp * T_SSP)) + c2_sig_desire * std::exp(-lambda * (t_sp * T_SSP));
        V_B[0] = lambda * (c1_sig_desire * std::exp(lambda * (t_sp * T_SSP)) - c2_sig_desire * std::exp(-lambda * (t_sp * T_SSP)));

        // px_Final = c1_sig_last * std::exp(lambda * (T_SSP)) + c2_sig_last * std::exp(-lambda * (T_SSP));
        // vx_Final = lambda * (c1_sig_last * exp(lambda * (T_SSP)) - c2_sig_last * std::exp(-lambda * (T_SSP)));

        px_Final = p_sig_last;
        vx_Final = v_sig_last;

        if(t_con - T_DSP <= t_afterImpact){
            u_B[0] = 2*px_ref + T_DSP * vx_ref + u_B_offset_x;
        }else{
            u_B[0] = 2*px_ref + (px_Final-px_ref) + T_DSP * vx_Final + 1 / tanh(lambda * T_SSP) * (vx_Final - vx_ref) / lambda * feedback_factor_vx + u_B_offset_x;
        }

        

        // std::cout << "px_Final:" << px_Final<< " ";
        // std::cout << "px_ref:" << px_ref<< " ";
        // std::cout << "vx_Final:" << vx_Final<< " ";
        // std::cout << "vx_ref:" << vx_ref<< " ";
        // std::cout << std::endl;

        // Coronal plane planning
        double p_cor_last = py_support;  
        double v_cor_last = V_last_B[1]; 

        double E_last_c = std::exp(lambda * (t_sp_last * T_SSP));
        double c1_cor_last = (p_cor_last / (2 * E_last_c)) + (v_cor_last / (2 * lambda * E_last_c));
        double c2_cor_last = (p_cor_last * E_last_c / 2) - (v_cor_last * E_last_c / (2 * lambda));

        // P_B[1] = c1_cor_last * std::exp(lambda * (t_sp * T_SSP)) + c2_cor_last * std::exp(-lambda * (t_sp * T_SSP));
        // V_B[1] = lambda * (c1_cor_last * std::exp(lambda * (t_sp * T_SSP)) - c2_cor_last * std::exp(-lambda * (t_sp * T_SSP)));

        P_B[1] = c1_cor_desire * std::exp(lambda * (t_sp * T_SSP)) + c2_cor_desire * std::exp(-lambda * (t_sp * T_SSP));
        V_B[1] = lambda * (c1_cor_desire * std::exp(lambda * (t_sp * T_SSP)) - c2_cor_desire * std::exp(-lambda * (t_sp * T_SSP)));

        double py_Final = c1_cor_last * std::exp(lambda * (T_SSP)) + c2_cor_last * std::exp(-lambda * (T_SSP));
        vy_Final = lambda * (c1_cor_last * exp(lambda * (T_SSP)) - c2_cor_last * std::exp(-lambda * (T_SSP)));

        if(t_con - T_DSP <= t_afterImpact){
            u_B[1] = 2*py_ref + T_DSP * vy_ref;
        }else{
            u_B[1] = 2*py_ref + (py_Final-py_ref) + T_DSP * vy_Final + 1 / tanh(lambda * T_SSP) * (vy_Final - vy_ref) / lambda * feedback_factor_vy;
        }

        // 步幅的安全限制
        u_B[0] = std::max(-step_length_max_X, std::min(step_length_max_X, u_B[0]));
        u_B[1] = std::max(step_length_min_Y, std::min(step_length_max_Y, u_B[1]));


        std::cout << "vy_Final:" << vy_Final<< " ";
        std::cout << "vy_ref:" << vy_ref<< " ";
        std::cout << "py_Final:" << py_Final<< " ";
        std::cout << "py_ref:" << py_ref<< " ";
        std::cout << "u_B[1]:" << u_B[1]<< " ";
        std::cout << std::endl;

        double z_swing_pos, z_swing_dot;
        ThirdpolyPlus(foot_height, 0, foot_height, contact_velocity, T_SSP, t_sp * T_SSP, z_swing_pos, z_swing_dot, 0.5*T_SSP, foot_height + step_height, 0);

        Eigen::Vector2d plus_rotation_speed = computeLinearVelocity(P_B, gait_OmegaZ_desire);
        P_B += plus_rotation_speed * dt; 
        V_B += plus_rotation_speed;

        // u_B += 2*plus_rotation_speed*dt;

        P_W = Rotation_B2W * P_B;
        V_W = Rotation_B2W * V_B;
        u_W = Rotation_B2W * u_B;

        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg

            swingL = 1;
            swingR = 0;

            feet_r_Pos_W_desire = feet_r_Pos_PhaseStart_W;
            feet_r_Pos_W_desire[2] = foot_height;
            feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            base_Pos_desire.head(2) = feet_r_Pos_W_actual.head(2) + P_W;
            // base_Pos_desire[0] = 0.14 + P_W[0];
            base_Pos_desire[2] = base_heignt; 

            base_LinVel_desire.head(2) = V_W;
            base_LinVel_desire[2] = 0; 

            if(t_con - T_DSP <= T_SSP - t_beforeImpact){
                Thirdpoly(feet_l_Pos_PhaseStart_W[0], 0, feet_r_Pos_PhaseStart_W[0] + u_W[0], 0, T_SSP - t_beforeImpact, t_sp*T_SSP, feet_l_Pos_W_desire[0], feet_l_LinVel_W_desire[0]);
                Thirdpoly(feet_l_Pos_PhaseStart_W[1], 0, feet_r_Pos_PhaseStart_W[1] + u_W[1], 0, T_SSP - t_beforeImpact, t_sp*T_SSP, feet_l_Pos_W_desire[1], feet_l_LinVel_W_desire[1]);
            }else{
                feet_l_LinVel_W_desire.head(2).setZero();
            }


            // feet_l_Pos_PhaseStart_W[0] = -0.14;
            // feet_r_Pos_PhaseStart_W[0] = 0.14;

            feet_l_Pos_W_desire[2] = z_swing_pos; 
            feet_l_LinVel_W_desire[2] = z_swing_dot; 

            base_EulerZ_desire = base_EulerZ_actual + gait_OmegaZ_desire * dt;
            base_OmegaZ_desire = gait_OmegaZ_desire;
            feet_r_EulerZ_W_desire = feet_r_EulerZ_PhaseStart_W;
            feet_r_OmegaZ_W_desire = 0;
            Thirdpoly(feet_l_EulerZ_PhaseStart_W, 0, base_EulerZ_desire + foot_yaw_offset, 0, T_SSP, t_sp*T_SSP, feet_l_EulerZ_W_desire, feet_l_OmegaZ_W_desire);   
            
            feet_r_EulerY_W_desire = 0;
            feet_r_OmegaY_W_desire = 0;
            ThirdpolyPlus(0, 0, 0, 0, T_SSP, t_sp * T_SSP, feet_l_EulerY_W_desire, feet_l_OmegaY_W_desire, 0.5*T_SSP, foot_EulerY_max*(u_B[0]/foot_distance_max), 0);
            // feet_l_EulerZ_W_desire = base_EulerZ_actual;
            // feet_l_OmegaZ_W_desire = 0;    
 
        } 
        else {  // Right leg is the swing leg, left leg is the support leg

            swingL = 0;
            swingR = 1;

            feet_l_Pos_W_desire = feet_l_Pos_PhaseStart_W;
            feet_l_Pos_W_desire[2] = foot_height;
            feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            base_Pos_desire.head(2) = feet_l_Pos_W_actual.head(2) + P_W;
            // base_Pos_desire[0] = -0.14 + P_W[0];
            base_Pos_desire[2] = base_heignt; 

            base_LinVel_desire.head(2) = V_W;
            base_LinVel_desire[2] = 0; 
            // std::cout<<"u_b[0]: "<<u_B[0]<<std::endl;
            // std::cout<<"u_b[1]: "<<u_B[1]<<std::endl;
            // Thirdpoly(feet_r_Pos_PhaseStart_W[0], 0, feet_l_Pos_PhaseStart_W[0] + u_W[0], 0, 0.9 * T_SSP, t_sp*T_SSP, feet_r_Pos_W_desire[0], feet_r_LinVel_W_desire[0]);
            // Thirdpoly(feet_r_Pos_PhaseStart_W[1], 0, feet_l_Pos_PhaseStart_W[1] + u_W[1], 0, 0.9 * T_SSP, t_sp*T_SSP, feet_r_Pos_W_desire[1], feet_r_LinVel_W_desire[1]);
            // feet_l_Pos_PhaseStart_W[0] = -0.14;
            // feet_r_Pos_PhaseStart_W[0] = 0.14;
            if(t_con - T_DSP <= T_SSP - t_beforeImpact){
                Thirdpoly(feet_r_Pos_PhaseStart_W[0], 0, feet_l_Pos_PhaseStart_W[0] + u_W[0], 0, T_SSP - t_beforeImpact, t_sp*T_SSP, feet_r_Pos_W_desire[0], feet_r_LinVel_W_desire[0]);
                Thirdpoly(feet_r_Pos_PhaseStart_W[1], 0, feet_l_Pos_PhaseStart_W[1] + u_W[1], 0, T_SSP - t_beforeImpact, t_sp*T_SSP, feet_r_Pos_W_desire[1], feet_r_LinVel_W_desire[1]);
            }else{
                feet_r_LinVel_W_desire.head(2).setZero();
            }

            feet_r_Pos_W_desire[2] = z_swing_pos; 
            feet_r_LinVel_W_desire[2] = z_swing_dot; 

            base_EulerZ_desire = base_EulerZ_actual + gait_OmegaZ_desire * dt;
            base_OmegaZ_desire = gait_OmegaZ_desire;
            feet_l_EulerZ_W_desire = feet_l_EulerZ_PhaseStart_W;
            feet_l_OmegaZ_W_desire = 0;
            Thirdpoly(feet_r_EulerZ_PhaseStart_W, 0, base_EulerZ_desire - foot_yaw_offset, 0, T_SSP, t_sp*T_SSP, feet_r_EulerZ_W_desire, feet_r_OmegaZ_W_desire); 
            
            feet_l_EulerY_W_desire = 0;
            feet_l_OmegaY_W_desire = 0;
            ThirdpolyPlus(0, 0, 0, 0, T_SSP, t_sp * T_SSP, feet_r_EulerY_W_desire, feet_r_OmegaY_W_desire, 0.5*T_SSP, foot_EulerY_max*(u_B[0]/foot_distance_max), 0);
            // feet_l_EulerZ_W_desire = base_EulerZ_actual;
            // feet_l_OmegaZ_W_desire = 0;  

        } 

        // Eigen::IOFormat oneLineFmt(Eigen::StreamPrecision, 0, ", ", ", ", "[", "]", "[", "]");
        // std::cout 
        //         << "  P_B: " << P_B.format(oneLineFmt)
        //         << "  V_B: " << V_B.format(oneLineFmt)
        //         << "  P_W: " << P_W.format(oneLineFmt)
        //         << "  V_W: " << V_W.format(oneLineFmt)
        //         << "  P_W: " << P_W.format(oneLineFmt)
        //         << "  V_W: " << V_W.format(oneLineFmt)
        //         << std::endl;

        
    

    }

    CalculateAvgSpeedForward();

    ArmJointPlan(T);
    waist_pos_desire = 0;
    // ArmJointRef();

    t = t + dt;
    CheckLegChange(T);

}


void GaitPlanner::FirstStep()
{
    
    feet_r_Pos_W_desire = feet_r_Pos_PhaseStart_W;
    feet_r_Pos_W_desire[2] = foot_height;
    feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

    feet_l_Pos_W_desire = feet_l_Pos_PhaseStart_W;
    feet_l_Pos_W_desire[2] = foot_height;
    feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

    double sigma2 = lambda * tanh(lambda * T_SSP / 2);
    double vy_desire = (uy_l_ref+uy_r_ref)/(2*T);
    double d2 = (lambda * lambda * (1 / (cosh(lambda * T_SSP / 2) * cosh(lambda * T_SSP / 2))) * T * vy_desire) / (lambda * lambda * T_DSP + 2 * sigma2);
    double py_l_ref = (uy_l_ref - T_DSP * d2) / (2 + T_DSP * sigma2);
    double vy_l_ref = (sigma2 * py_l_ref + d2);

    Eigen::Vector2d P_l_start_W = base_Pos_PhaseStart.head(2) - feet_l_Pos_PhaseStart_W.head(2);

    Eigen::Matrix2d Rotation_W2B = rotation_matrix_plane(-base_EulerZ_actual);
    Eigen::Vector2d P_l_start_B = Rotation_W2B * P_l_start_W;
    
    Eigen::Matrix2d Rotation_B2W = rotation_matrix_plane(base_EulerZ_actual);
    
    P_B[0] = P_l_start_B[0];
    V_B[0] = 0;
    ThirdpolyPlus(P_l_start_B[1], 0, P_l_start_B[1], vy_l_ref, pre_swing_time, t-WalkStartTime, P_B[1], V_B[1], 0.7*pre_swing_time, P_l_start_B[1]/2, 0);

    P_W = Rotation_B2W * P_B;
    V_W = Rotation_B2W * V_B;

    // std::cout<<"P_W: "<<P_W<<std::endl;

    base_Pos_desire.head(2) = feet_l_Pos_PhaseStart_W.head(2) + P_W;
    base_Pos_desire[2] = base_heignt; 

    base_LinVel_desire.head(2) = V_W;
    base_LinVel_desire[2] = 0; 

    base_EulerZ_desire = base_EulerZ_actual;
    base_OmegaZ_desire = 0;
    feet_l_EulerZ_W_desire = feet_l_EulerZ_PhaseStart_W;
    feet_l_OmegaZ_W_desire = 0;
    feet_r_EulerZ_W_desire = feet_r_EulerZ_PhaseStart_W;
    feet_r_OmegaZ_W_desire = 0;

    feet_l_EulerY_W_desire = 0;
    feet_r_EulerY_W_desire = 0;
    feet_l_OmegaY_W_desire = 0;
    feet_r_OmegaY_W_desire = 0;

}

void GaitPlanner::RecordPhaseStartPos(){

    base_Pos_PhaseStart = base_Pos_actual;
    feet_r_Pos_PhaseStart_W = feet_r_Pos_W_actual;
    feet_l_Pos_PhaseStart_W = feet_l_Pos_W_actual;
    feet_r_EulerZ_PhaseStart_W = feet_r_EulerZ_W_actual;
    feet_l_EulerZ_PhaseStart_W = feet_l_EulerZ_W_actual;
    base_EulerZ_PhaseStart = base_EulerZ_actual;

}

void GaitPlanner::RecordStandStartPos(){

    feet_l_Pos_StandStart_W = feet_l_Pos_W_actual;
    feet_r_Pos_StandStart_W = feet_r_Pos_W_actual;
    base_Pos_StandStart_W = (feet_l_Pos_W_actual + feet_r_Pos_W_actual)/2;

    base_Pos_StandStart_W[2] = base_heignt;
    feet_l_Pos_StandStart_W[2] = foot_height;
    feet_r_Pos_StandStart_W[2] = foot_height;

    // std::cout << "RecordStandStartPos" << std::endl;

}

void GaitPlanner::RecordGaitStartArmJointPos(){

    arm_l_JointPos_GaitStart = arm_l_JointPos_actual;
    arm_r_JointPos_GaitStart = arm_r_JointPos_actual;

}

void GaitPlanner::CheckLegChange(double Total_time){

    if(t_con>=Total_time || (CheckSwingContact())){
        // std::cout << "CheckSwingContact:" << CheckSwingContact() <<std::endl;
        T_START = t;
        RecordGaitStartArmJointPos();
        RecordPhaseStartPos();
        FootNum = (FootNum == 1) ? 2 : 1;

        IfGaitEnd = true;
        IfCanStop = (abs(feet_r_Pos_W_actual[0]-feet_l_Pos_W_actual[0])<=ux_threshold)
                    &&(base_LinVel_actual[0]<=vx_threshold)
                    &&(abs(feet_l_Pos_W_actual[1]-feet_r_Pos_W_actual[1]-uy_r_ref)<=uy_threshold);

        if(IfCanStop){
            RecordStandStartPos();
        }
        
    }else{
        IfGaitEnd = false;
        IfCanStop = false;      
    }

}

void GaitPlanner::CheckFlyLegChange(double Total_time){

    if(t_con>=Total_time || (CheckSwingContact())){
        T_START = t;
        FootNum = (FootNum == 1) ? 2 : 1;
        RecordGaitStartArmJointPos();
        RecordPhaseStartPos();
        IfGaitEnd = true;

    }else{
        IfGaitEnd = false;
        IfCanStop = false;      
    }


}

bool GaitPlanner::CheckSwingContact(){

    bool If_contact;
    double contact_swing_force_before;
    double contact_swing_force_now;
    // std::cout << "contact_check_time: " << contact_check_time << std::endl;
    // std::cout << "dt: " << dt << std::endl;
    double check_length = contact_check_time / dt;
    // std::cout << "check_length: " << check_length << std::endl;
    if (contact_force_history.size() >= check_length) {
        // double value = contact_force_history[contact_force_history.size() - 20](8);
        // std::cout << "check_length: " << check_length << std::endl;
        if(FootNum==1){
            contact_swing_force_before = contact_force_history[contact_force_history.size() - check_length](2);
            contact_swing_force_now = contact_force_history.back()(2);
        }
        else{
            contact_swing_force_before = contact_force_history[contact_force_history.size() - check_length](8);
            contact_swing_force_now = contact_force_history.back()(8);
        }

        If_contact = (contact_swing_force_before>=0) && (contact_swing_force_before<contact_force_start_threshold) && (contact_swing_force_now-contact_swing_force_before>contact_force_change_threshold);

    } else {
        std::cerr << "Not enough data points to retrieve the 20th last contact_force_normal." << std::endl;
        exit(1);
    }

    return If_contact;

}

void GaitPlanner::updateContactFlags(const std::string& command, std::vector<bool>& contact_flag) {
    if (command == "L") {
        for (size_t i = 0; i < contact_flag.size(); ++i) {
            contact_flag[i] = (i % 2 == 0); // 单数位置（从0开始计数）为true，双数位置为false
        }
    } else if (command == "R") {
        for (size_t i = 0; i < contact_flag.size(); ++i) {
            contact_flag[i] = (i % 2 != 0); // 单数位置为false，双数位置为true
        }
    } else if (command == "ALL") {
        for (size_t i = 0; i < contact_flag.size(); ++i) {
            contact_flag[i] = (true); // 全部为true
        }
    }else if (command == "None") {
        for (size_t i = 0; i < contact_flag.size(); ++i) {
            contact_flag[i] = (false); // 全部为false
        }
    }

}

void GaitPlanner::Fly()
{
    getCurrentSpeedAsVx_Final();

    t_con = t - T_START;  // Execution time
    
    double vx_desire = gait_vx_desire;  // Target velocity in x direction
    double sigma1 = lambda / tanh(lambda * T_SSP / 2);
    double px_ref = vx_desire * T / (2 + T_DSP * sigma1);  // End state position
    double vx_ref = sigma1 * px_ref;

    // Coronal plane        
    double sigma2 = lambda * tanh(lambda * T_SSP / 2);
    double vy_desire = (uy_l_ref+uy_r_ref)/(2*T);
    double d2 = (lambda * lambda * (1 / (cosh(lambda * T_SSP / 2) * cosh(lambda * T_SSP / 2))) * T * vy_desire) / (lambda * lambda * T_DSP + 2 * sigma2);
    double py_l_ref = (uy_l_ref - T_DSP * d2) / (2 + T_DSP * sigma2);
    double vy_l_ref = (sigma2 * py_l_ref + d2);
    double py_r_ref = (uy_r_ref - T_DSP * d2) / (2 + T_DSP * sigma2);
    double vy_r_ref = (sigma2 * py_r_ref + d2);
    
    // Get state variables
    double xL_last = feet_l_Pos_W_actual[0] - base_Pos_actual[0];
    double yL_last = feet_l_Pos_W_actual[1] - base_Pos_actual[1];
    double zL_last = feet_l_Pos_W_actual[2] - base_Pos_actual[2];

    double xR_last = feet_r_Pos_W_actual[0] - base_Pos_actual[0];
    double yR_last = feet_r_Pos_W_actual[1] - base_Pos_actual[1];
    double zR_last = feet_r_Pos_W_actual[2] - base_Pos_actual[2];

    double xL_0 = feet_l_Pos_PhaseStart_W[0];
    double yL_0 = feet_l_Pos_PhaseStart_W[1];
    double zL_0 = feet_l_Pos_PhaseStart_W[2];

    double xR_0 = feet_r_Pos_PhaseStart_W[0];
    double yR_0 = feet_r_Pos_PhaseStart_W[1];
    double zR_0 = feet_r_Pos_PhaseStart_W[2];

    // Gait planning
    if (t_con <= T_SSP) {  // Single support phase

        double t_sp = (t_con + dt) / T_SSP;  // Time inside SSP next step
        if (t_sp > 1) t_sp = 1;

        double t_sp_last = t_con  / T_SSP;  
        if (t_sp_last > 1) t_sp_last = 1;

        double py_ref,vy_ref;
        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg
            px_support = -xR_last; 
            py_support = -yR_last;  
            py_ref = py_r_ref;  
            vy_ref = vy_r_ref;  

            updateContactFlags("R", contact_flag);

        } 
        else {  // Right leg is the swing leg, left leg is the support leg
            px_support = -xL_last; 
            py_support = -yL_last;  
            py_ref = py_l_ref;  
            vy_ref = vy_l_ref; 

            updateContactFlags("L", contact_flag);
        }    

        // Sagittal plane planning
        double p_sig_last = px_support;  
        double v_sig_last = base_LinVel_actual[0]; 

        double E_last_s = std::exp(lambda * (t_sp_last * T_SSP));
        double c1_sig_last = (p_sig_last / (2 * E_last_s)) + (v_sig_last / (2 * lambda * E_last_s));
        double c2_sig_last = (p_sig_last * E_last_s / 2) - (v_sig_last * E_last_s / (2 * lambda));

        double p_sig = c1_sig_last * std::exp(lambda * (t_sp * T_SSP)) + c2_sig_last * std::exp(-lambda * (t_sp * T_SSP));
        double v_sig = lambda * (c1_sig_last * std::exp(lambda * (t_sp * T_SSP)) - c2_sig_last * std::exp(-lambda * (t_sp * T_SSP)));

        double px_Final = c1_sig_last * std::exp(lambda * (T_SSP)) + c2_sig_last * std::exp(-lambda * (T_SSP));
        vx_Final = lambda * (c1_sig_last * exp(lambda * (T_SSP)) - c2_sig_last * std::exp(-lambda * (T_SSP)));

        ux = px_Final + px_ref + 1 / tanh(lambda * T_SSP) * (vx_Final - vx_ref) / lambda;

        // Coronal plane planning
        double p_cor_last = py_support;  
        double v_cor_last = base_LinVel_actual[1]; 

        double E_last_c = std::exp(lambda * (t_sp_last * T_SSP));
        double c1_cor_last = (p_cor_last / (2 * E_last_c)) + (v_cor_last / (2 * lambda * E_last_c));
        double c2_cor_last = (p_cor_last * E_last_c / 2) - (v_cor_last * E_last_c / (2 * lambda));

        double p_cor = c1_cor_last * std::exp(lambda * (t_sp * T_SSP)) + c2_cor_last * std::exp(-lambda * (t_sp * T_SSP));
        double v_cor = lambda * (c1_cor_last * std::exp(lambda * (t_sp * T_SSP)) - c2_cor_last * std::exp(-lambda * (t_sp * T_SSP)));

        double py_Final = c1_cor_last * std::exp(lambda * (T_SSP)) + c2_cor_last * std::exp(-lambda * (T_SSP));
        double vy_Final = lambda * (c1_cor_last * exp(lambda * (T_SSP)) - c2_cor_last * std::exp(-lambda * (T_SSP)));

        uy = py_Final + py_ref + 1 / tanh(lambda * T_SSP) * (vy_Final - vy_ref) / lambda * feedback_factor_vy;

        double bh, bh_dot;
        Thirdpoly(0, 0, 1, 0, 1, t_sp, bh, bh_dot);

        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg

            double z_swing_pos, z_swing_dot;
            ThirdpolyPlus(zL_0, 0, foot_height + jump_height, fall_velocity, T_SSP, t_sp * T_SSP, z_swing_pos, z_swing_dot, 0.7*T_SSP, foot_height + step_height, 0);
            swingL = 1;
            swingR = 0;

            feet_r_Pos_W_desire = feet_r_Pos_W_actual;
            feet_r_Pos_W_desire[2] = foot_height;
            feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            base_Pos_desire[0] = feet_r_Pos_W_actual[0] + p_sig;
            base_Pos_desire[1] = feet_r_Pos_W_actual[1] + p_cor;
            base_Pos_desire[2] = base_heignt; 

            base_LinVel_desire[0] = v_sig;
            base_LinVel_desire[1] = v_cor;
            base_LinVel_desire[2] = 0; 

            feet_l_Pos_W_desire[0] = feet_r_Pos_W_actual[0] + (xL_0 - xR_0) * (1 - bh) + ux * bh;
            feet_l_Pos_W_desire[1] = feet_r_Pos_W_actual[1] + (yL_0 - yR_0) * (1 - bh) + uy * bh;
            feet_l_Pos_W_desire[2] = z_swing_pos; 

            feet_l_LinVel_W_desire[0] = bh_dot * (ux + xR_0 - xL_0) / T_SSP;
            feet_l_LinVel_W_desire[1] = bh_dot * (uy + yR_0 - yL_0) / T_SSP;
            feet_l_LinVel_W_desire[2] = z_swing_dot; 
 
        } 
        else {  // Right leg is the swing leg, left leg is the support leg

            double z_swing_pos, z_swing_dot;
            ThirdpolyPlus(zR_0, 0, foot_height + jump_height, fall_velocity, T_SSP, t_sp * T_SSP, z_swing_pos, z_swing_dot, 0.66*T_SSP, foot_height + step_height, 0);
            swingL = 0;
            swingR = 1;

            feet_l_Pos_W_desire = feet_l_Pos_W_actual;
            feet_l_Pos_W_desire[2] = foot_height;
            feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            base_Pos_desire[0] = feet_l_Pos_W_actual[0] + p_sig;
            base_Pos_desire[1] = feet_l_Pos_W_actual[1] + p_cor;
            base_Pos_desire[2] = base_heignt; 

            base_LinVel_desire[0] = v_sig;
            base_LinVel_desire[1] = v_cor;
            base_LinVel_desire[2] = 0; 

            feet_r_Pos_W_desire[0] = feet_l_Pos_W_actual[0] + (xR_0 - xL_0) * (1 - bh) + ux * bh;
            feet_r_Pos_W_desire[1] = feet_l_Pos_W_actual[1] + (yR_0 - yL_0) * (1 - bh) + uy * bh;
            feet_r_Pos_W_desire[2] = z_swing_pos; 

            feet_r_LinVel_W_desire[0] = bh_dot * (ux + xL_0 - xR_0) / T_SSP;
            feet_r_LinVel_W_desire[1] = bh_dot * (uy + yL_0 - yR_0) / T_SSP;
            feet_r_LinVel_W_desire[2] = z_swing_dot; 
        } 
        


    }
    else{         // Fly phase

        updateContactFlags("None", contact_flag);
        swingL = 1;
        swingR = 1;

        base_Pos_desire[0] = base_Pos_actual[0] + base_LinVel_actual[0] * dt;
        base_Pos_desire[1] = base_Pos_actual[1] + base_LinVel_actual[1] * dt;
        base_Pos_desire[2] = base_heignt; 

        base_LinVel_desire[0] = base_LinVel_actual[0];  
        base_LinVel_desire[1] = base_LinVel_actual[1];
        base_LinVel_desire[2] = 0; 

        double z_swing_pos, z_swing_dot;
        Thirdpoly(foot_height + jump_height, fall_velocity, foot_height, 0, T_jump, t_con-T_SSP, z_swing_pos, z_swing_dot);
        double z_sup_pos, z_sup_dot;
        Thirdpoly(foot_height, up_velocity, foot_height + jump_height, 0, T_jump, t_con-T_SSP, z_sup_pos, z_sup_dot);

        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg

            feet_r_Pos_W_desire = feet_r_Pos_W_actual;
            feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);
            feet_l_Pos_W_desire = feet_l_Pos_W_actual;
            feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            feet_l_Pos_W_desire[0] = feet_l_Pos_W_actual[0] + base_LinVel_actual[0]*dt;
            feet_l_LinVel_W_desire[0] = base_LinVel_actual[0];  

            feet_l_Pos_W_desire[2] = z_swing_pos; 
            feet_l_LinVel_W_desire[2] = z_swing_dot; 
            feet_r_Pos_W_desire[2] = z_sup_pos; 
            feet_r_LinVel_W_desire[2] = z_sup_dot; 


        } 
        else {  // Right leg is the swing leg, left leg is the support leg
        
            feet_r_Pos_W_desire = feet_r_Pos_W_actual;
            feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);
            feet_l_Pos_W_desire = feet_l_Pos_W_actual;
            feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            feet_r_Pos_W_desire[0] = feet_r_Pos_W_actual[0] + base_LinVel_actual[0]*dt;
            feet_r_LinVel_W_desire[0] = base_LinVel_actual[0];  

            feet_r_Pos_W_desire[2] = z_swing_pos; 
            feet_r_LinVel_W_desire[2] = z_swing_dot; 
            feet_l_Pos_W_desire[2] = z_sup_pos; 
            feet_l_LinVel_W_desire[2] = z_sup_dot; 

        } 


    }


    CalculateAvgSpeedForward();
    ArmJointPlan(T_SSP + T_jump);
    // ArmJointRef();

    t = t + dt;
    CheckFlyLegChange(T_SSP + T_jump);

}

void GaitPlanner::ArmJointPlan(double TotalTime)
{

    if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg
        for(int i=0; i<4; ++i){

            Thirdpoly(arm_l_JointPos_GaitStart[i], 0, leftarm_joint_behind[i], 0, 
                        TotalTime, t_con, arm_l_JointPos_desire[i], arm_l_JointVel_desire[i]);      
            Thirdpoly(arm_r_JointPos_GaitStart[i], 0, rightarm_joint_front[i], 0, 
                        TotalTime, t_con, arm_r_JointPos_desire[i], arm_r_JointVel_desire[i]);      

        }

    } 
    else {  // Right leg is the swing leg, left leg is the support leg

        for(int i=0; i<4; ++i){

            Thirdpoly(arm_l_JointPos_GaitStart[i], 0, leftarm_joint_front[i], 0, 
                        TotalTime, t_con, arm_l_JointPos_desire[i], arm_l_JointVel_desire[i]);      
            Thirdpoly(arm_r_JointPos_GaitStart[i], 0, rightarm_joint_behind[i], 0, 
                        TotalTime, t_con, arm_r_JointPos_desire[i], arm_r_JointVel_desire[i]);      

        }
    } 
    arm_l_JointPos_desire[4] = 0;
    arm_r_JointPos_desire[4] = 0;
    arm_l_JointVel_desire[4] = 0;
    arm_r_JointVel_desire[4] = 0;

    double speed_forward_avg_factor = std::min(speed_forward_avg, 1.0);

    arm_l_JointPos_desire *= (0.2+0.8*speed_forward_avg_factor);
    arm_r_JointPos_desire *= (0.2+0.8*speed_forward_avg_factor);
    arm_l_JointVel_desire *= (0.2+0.8*speed_forward_avg_factor);
    arm_r_JointVel_desire *= (0.2+0.8*speed_forward_avg_factor);

}

void GaitPlanner::ArmJointRef()
{

    double t_walking = t - pre_swing_time -WalkStartTime;
    double arm_cycle_time = 12.5*T;
    double t_walking_in_cycle = std::fmod(t_walking, arm_cycle_time);

    interpolateJointPosVel(leftArm_joint_pos, t_walking_in_cycle, arm_cycle_time, arm_l_JointPos_desire, arm_l_JointVel_desire);
    interpolateJointPosVel(rightArm_joint_pos, t_walking_in_cycle, arm_cycle_time, arm_r_JointPos_desire, arm_r_JointVel_desire);

    arm_l_JointPos_desire[4] = 0;
    arm_r_JointPos_desire[4] = 0;
    arm_l_JointVel_desire[4] = 0;
    arm_r_JointVel_desire[4] = 0;

    std::cout << "t_walking:" << t_walking<< " ";
    std::cout << "arm_cycle_time:" << arm_cycle_time<< " ";
    std::cout << "t_walking_in_cycle:" << t_walking_in_cycle<< " ";
    // std::cout << "leftArm_joint_pos:" << leftArm_joint_pos<< " ";
    // std::cout << "arm_l_JointPos_desire:" << arm_l_JointPos_desire<< " ";
    std::cout << std::endl;

}


void GaitPlanner::interpolateJointPosVel(
    const Eigen::MatrixXd& arm_joint_pos, // 行：时间，列：关节
    double t_arm, double T_arm_total,                     // 当前时间和总时长
    Eigen::Matrix<double, 5, 1>& arm_JointPos_desire,  
    Eigen::Matrix<double, 5, 1>& arm_JointVel_desire
) {
    int time_steps = arm_joint_pos.rows(); // 行数 = 时间步数
    int joint_num = arm_joint_pos.cols();  // 列数 = 关节数

    // arm_l_JointPos_desire.resize(joint_num);
    // arm_l_JointVel_desire.resize(joint_num);

    double t_norm = t_arm / T_arm_total;

    // 在时间步上的浮点位置
    double index_f = t_norm * (time_steps - 1);
    int idx = static_cast<int>(std::floor(index_f));
    double frac = index_f - idx;

    // 边界处理
    if (idx >= time_steps - 1) {
        idx = time_steps - 2;
        frac = 1.0;
    } else if (idx < 0) {
        idx = 0;
        frac = 0.0;
    }

    double dt_steps = T_arm_total / (time_steps - 1); // 单个时间步长

    for (int j = 0; j < joint_num; ++j) {
        double p0 = arm_joint_pos(idx, j);
        double p1 = arm_joint_pos(idx + 1, j);

        // 插值位置
        double pos = (1 - frac) * p0 + frac * p1;
        arm_JointPos_desire(j) = pos;

        // 速度 = 差分 / dt
        double vel = (p1 - p0) / dt_steps;

        // 边界速度设为 0
        if (t_norm <= 0.0 || t_norm >= 1.0) {
            vel = 0.0;
        }

        arm_JointVel_desire(j) = vel;
    }
}

void GaitPlanner::CalculateAvgSpeedForward()
{
    double speed_forward_avg_cal;
    double check_length = T_SSP / dt;
    int n = static_cast<int>(check_length);  // 将 check_length 转换为整数

    if (speed_forward_history.size() >= static_cast<size_t>(n) && n > 0) {
        double sum = 0;
        // 对 speed_forward_history 的最后 n 个元素求和
        for (size_t i = speed_forward_history.size() - n; i < speed_forward_history.size(); i++) {
            sum += speed_forward_history[i];
        }
        speed_forward_avg_cal = sum / n;
    } else {
        speed_forward_avg_cal = 0;
    }

    speed_forward_avg = speed_forward_avg_cal;
}




void GaitPlanner::CheckFly2WalkEnd()
{
    if(t_con>T_SSP || (CheckSwingContact())){
        // std::cout << "CheckSwingContact:" << CheckSwingContact() <<std::endl;
        T_START = t;
        RecordGaitStartArmJointPos();
        RecordPhaseStartPos();
        FootNum = (FootNum == 1) ? 2 : 1;
        IfFly2Wlak = false;
        
    }
}

void GaitPlanner::Fly2Walk()
{
    t_con = t - T_START;  // Execution time

    // Gait planning
    if (t_con <= T_SSP) {  // Single support phase  

        double vx_desire = gait_vx_desire;  // Target velocity in x direction
        double sigma1 = lambda / tanh(lambda * T_SSP / 2);
        double px_ref = vx_desire * T / (2 + T_DSP * sigma1);  // End state position
        double vx_ref = sigma1 * px_ref;

        // Coronal plane        
        double sigma2 = lambda * tanh(lambda * T_SSP / 2);
        double vy_desire = (uy_l_ref+uy_r_ref)/(2*T);
        double d2 = (lambda * lambda * (1 / (cosh(lambda * T_SSP / 2) * cosh(lambda * T_SSP / 2))) * T * vy_desire) / (lambda * lambda * T_DSP + 2 * sigma2);
        double py_l_ref = (uy_l_ref - T_DSP * d2) / (2 + T_DSP * sigma2);
        double vy_l_ref = (sigma2 * py_l_ref + d2);
        double py_r_ref = (uy_r_ref - T_DSP * d2) / (2 + T_DSP * sigma2);
        double vy_r_ref = (sigma2 * py_r_ref + d2);
        
        // Get state variables
        double xL_last = feet_l_Pos_W_actual[0] - base_Pos_actual[0];
        double yL_last = feet_l_Pos_W_actual[1] - base_Pos_actual[1];
        double zL_last = feet_l_Pos_W_actual[2] - base_Pos_actual[2];

        double xR_last = feet_r_Pos_W_actual[0] - base_Pos_actual[0];
        double yR_last = feet_r_Pos_W_actual[1] - base_Pos_actual[1];
        double zR_last = feet_r_Pos_W_actual[2] - base_Pos_actual[2];

        double xL_0 = feet_l_Pos_PhaseStart_W[0];
        double yL_0 = feet_l_Pos_PhaseStart_W[1];
        double zL_0 = feet_l_Pos_PhaseStart_W[2];

        double xR_0 = feet_r_Pos_PhaseStart_W[0];
        double yR_0 = feet_r_Pos_PhaseStart_W[1];
        double zR_0 = feet_r_Pos_PhaseStart_W[2];




        double t_sp = (t_con + dt) / T_SSP;  // Time inside SSP next step
        if (t_sp > 1) t_sp = 1;

        double t_sp_last = t_con  / T_SSP;  
        if (t_sp_last > 1) t_sp_last = 1;

        double py_ref,vy_ref;
        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg
            px_support = -xR_last; 
            py_support = -yR_last;  
            py_ref = py_r_ref;  
            vy_ref = vy_r_ref;  

            updateContactFlags("R", contact_flag);

        } 
        else {  // Right leg is the swing leg, left leg is the support leg
            px_support = -xL_last; 
            py_support = -yL_last;  
            py_ref = py_l_ref;  
            vy_ref = vy_l_ref; 

            updateContactFlags("L", contact_flag);
        }    

        // Sagittal plane planning
        double p_sig_last = px_support;  
        double v_sig_last = base_LinVel_actual[0]; 

        double E_last_s = std::exp(lambda * (t_sp_last * T_SSP));
        double c1_sig_last = (p_sig_last / (2 * E_last_s)) + (v_sig_last / (2 * lambda * E_last_s));
        double c2_sig_last = (p_sig_last * E_last_s / 2) - (v_sig_last * E_last_s / (2 * lambda));

        double p_sig = c1_sig_last * std::exp(lambda * (t_sp * T_SSP)) + c2_sig_last * std::exp(-lambda * (t_sp * T_SSP));
        double v_sig = lambda * (c1_sig_last * std::exp(lambda * (t_sp * T_SSP)) - c2_sig_last * std::exp(-lambda * (t_sp * T_SSP)));

        double px_Final = c1_sig_last * std::exp(lambda * (T_SSP)) + c2_sig_last * std::exp(-lambda * (T_SSP));
        vx_Final = lambda * (c1_sig_last * exp(lambda * (T_SSP)) - c2_sig_last * std::exp(-lambda * (T_SSP)));

        ux = px_Final + px_ref + 1 / tanh(lambda * T_SSP) * (vx_Final - vx_ref) / lambda;

        // Coronal plane planning
        double p_cor_last = py_support;  
        double v_cor_last = base_LinVel_actual[1]; 

        double E_last_c = std::exp(lambda * (t_sp_last * T_SSP));
        double c1_cor_last = (p_cor_last / (2 * E_last_c)) + (v_cor_last / (2 * lambda * E_last_c));
        double c2_cor_last = (p_cor_last * E_last_c / 2) - (v_cor_last * E_last_c / (2 * lambda));

        double p_cor = c1_cor_last * std::exp(lambda * (t_sp * T_SSP)) + c2_cor_last * std::exp(-lambda * (t_sp * T_SSP));
        double v_cor = lambda * (c1_cor_last * std::exp(lambda * (t_sp * T_SSP)) - c2_cor_last * std::exp(-lambda * (t_sp * T_SSP)));

        double py_Final = c1_cor_last * std::exp(lambda * (T_SSP)) + c2_cor_last * std::exp(-lambda * (T_SSP));
        double vy_Final = lambda * (c1_cor_last * exp(lambda * (T_SSP)) - c2_cor_last * std::exp(-lambda * (T_SSP)));

        uy = py_Final + py_ref + 1 / tanh(lambda * T_SSP) * (vy_Final - vy_ref) / lambda * feedback_factor_vy;

        double bh, bh_dot;
        Thirdpoly(0, 0, 1, 0, 1, t_sp, bh, bh_dot);

        if (FootNum == 1) {  // Left leg is the swing leg, right leg is the support leg

            double z_swing_pos, z_swing_dot;
            ThirdpolyPlus(zL_0, 0, foot_height, 0, T_SSP, t_sp * T_SSP, z_swing_pos, z_swing_dot, 0.5*T_SSP, foot_height + step_height, 0);
            swingL = 1;
            swingR = 0;

            feet_r_Pos_W_desire = feet_r_Pos_W_actual;
            feet_r_Pos_W_desire[2] = foot_height;
            feet_r_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            base_Pos_desire[0] = feet_r_Pos_W_actual[0] + p_sig;
            base_Pos_desire[1] = feet_r_Pos_W_actual[1] + p_cor;
            base_Pos_desire[2] = base_heignt; 

            base_LinVel_desire[0] = v_sig;
            base_LinVel_desire[1] = v_cor;
            base_LinVel_desire[2] = 0; 

            feet_l_Pos_W_desire[0] = feet_r_Pos_W_actual[0] + (xL_0 - xR_0) * (1 - bh) + ux * bh;
            feet_l_Pos_W_desire[1] = feet_r_Pos_W_actual[1] + (yL_0 - yR_0) * (1 - bh) + uy * bh;
            feet_l_Pos_W_desire[2] = z_swing_pos; 

            feet_l_LinVel_W_desire[0] = bh_dot * (ux + xR_0 - xL_0) / T_SSP;
            feet_l_LinVel_W_desire[1] = bh_dot * (uy + yR_0 - yL_0) / T_SSP;
            feet_l_LinVel_W_desire[2] = z_swing_dot; 
 
        } 
        else {  // Right leg is the swing leg, left leg is the support leg

            double z_swing_pos, z_swing_dot;
            ThirdpolyPlus(zR_0, 0, foot_height, 0, T_SSP, t_sp * T_SSP, z_swing_pos, z_swing_dot, 0.5*T_SSP, foot_height + step_height, 0);
            swingL = 0;
            swingR = 1;

            feet_l_Pos_W_desire = feet_l_Pos_W_actual;
            feet_l_Pos_W_desire[2] = foot_height;
            feet_l_LinVel_W_desire = Eigen::Vector3d(0, 0, 0);

            base_Pos_desire[0] = feet_l_Pos_W_actual[0] + p_sig;
            base_Pos_desire[1] = feet_l_Pos_W_actual[1] + p_cor;
            base_Pos_desire[2] = base_heignt; 

            base_LinVel_desire[0] = v_sig;
            base_LinVel_desire[1] = v_cor;
            base_LinVel_desire[2] = 0; 

            feet_r_Pos_W_desire[0] = feet_l_Pos_W_actual[0] + (xR_0 - xL_0) * (1 - bh) + ux * bh;
            feet_r_Pos_W_desire[1] = feet_l_Pos_W_actual[1] + (yR_0 - yL_0) * (1 - bh) + uy * bh;
            feet_r_Pos_W_desire[2] = z_swing_pos; 

            feet_r_LinVel_W_desire[0] = bh_dot * (ux + xL_0 - xR_0) / T_SSP;
            feet_r_LinVel_W_desire[1] = bh_dot * (uy + yL_0 - yR_0) / T_SSP;
            feet_r_LinVel_W_desire[2] = z_swing_dot; 
        }     
        
        ArmJointPlan(T_SSP);
    }


    t = t + dt;
    CheckFly2WalkEnd();


}

Eigen::MatrixXd GaitPlanner::readCSV(const std::string& filename) {
    std::ifstream file(filename);
    std::string line;
    std::vector<std::vector<double>> data;

    while (std::getline(file, line)) {
        std::stringstream ss(line);
        std::string value;
        std::vector<double> row;

        while (std::getline(ss, value, ',')) {
            row.push_back(std::stod(value));
        }

        data.push_back(row);
    }

    // 将 vector<vector<double>> 转为 Eigen::MatrixXd
    int rows = data.size();
    int cols = data[0].size();
    Eigen::MatrixXd mat(rows, cols);
    
    for (int i = 0; i < rows; ++i)
        mat.row(i) = Eigen::VectorXd::Map(&data[i][0], cols);

    return mat;
}


void Thirdpoly(double p0, double p0_dot, double p1, double p1_dot,
               double totalTime,   // total permating time
               double currenttime, // current time,from 0 to total time
               double &pd, double &pd_dot) {
    if(currenttime <0){
        pd = p0;
        pd_dot = 0;
    }
    else if (currenttime <= totalTime) {
        double a0 = p0;
        double a1 = p0_dot;
        double m = p1 - p0 - p0_dot * totalTime;
        double n = p1_dot - p0_dot;
        double a2 = 3 * m / (totalTime * totalTime) - n / totalTime;
        double a3 = -2 * m / (totalTime * totalTime * totalTime) + n / (totalTime * totalTime);
        pd = a3 * currenttime * currenttime * currenttime + a2 * currenttime * currenttime + a1 * currenttime + a0;
        pd_dot = 3 * a3 * currenttime * currenttime + 2 * a2 * currenttime + a1;
    } else {
        pd = p1;
        pd_dot = 0;
    }
}

void ThirdpolyPlus(double p0, double p0_dot, double p1, double p1_dot,
               double totalTime,   // total permating time
               double currenttime, // current time,from 0 to total time
               double &pd, double &pd_dot, double midtime, double pmid, double pmid_dot){
    if (currenttime <= midtime) {
        Thirdpoly(p0, p0_dot, pmid, pmid_dot, midtime, currenttime, pd, pd_dot);

    } else {
        Thirdpoly(pmid, pmid_dot, p1, p1_dot, totalTime-midtime, currenttime-midtime, pd, pd_dot);
    }
} 

Eigen::VectorXd parseVector(const YAML::Node& node) {
    Eigen::VectorXd vec(node.size());
    for (std::size_t i = 0; i < node.size(); ++i) {
        vec(i) = node[i].as<double>();
    }
    return vec;
}            

Eigen::Matrix2d rotation_matrix_plane(double angle) {
    Eigen::Matrix2d R;
    double c = std::cos(angle);
    double s = std::sin(angle);

    R << c, -s,
        s, c;

    return R;
}

Eigen::Matrix3d rotation_matrix_3d(const std::string& axis, double angle) {
    Eigen::Matrix3d R;
    double c = std::cos(angle);
    double s = std::sin(angle);

    if (axis == "x") {
        R << 1, 0, 0,
             0, c, -s,
             0, s, c;
    } else if (axis == "y") {
        R << c, 0, s,
             0, 1, 0,
            -s, 0, c;
    } else if (axis == "z") {
        R << c, -s, 0,
             s, c, 0,
             0, 0, 1;
    } else {
        throw std::invalid_argument("Axis must be 'x', 'y', or 'z'.");
    }
    return R;
}

Eigen::Vector2d computeLinearVelocity(const Eigen::Vector2d& r, double omega)
{
    // 根据公式：v = ω × r = (-omega * r_y, omega * r_x)
    return Eigen::Vector2d(-omega * r.y(), omega * r.x());
}

// logData
bool dataLog(Eigen::VectorXd &v, std::ofstream &f) {
    for (int i = 0; i < v.size(); i++) {
        f << v[i] << " ";
    }
    f << std::endl;
    return true;
}