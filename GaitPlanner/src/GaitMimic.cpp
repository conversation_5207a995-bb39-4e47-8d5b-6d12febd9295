#include "../include/GaitMimic.h"

GaitMimic::GaitMimic() {}

GaitMimic::~GaitMimic() {}

void GaitMimic::init(double _dt, DataPackage *data) {
    default_dof_pos = Eigen::VectorXd::Zero(action_num);
    default_dof_pos << -0.15, 0.0, 0.0, 0.3, -0.15, 0,
    -0.15, 0.0, 0.0, 0.3, -0.15, 0,
    0.0, 
    0.0, 0.0, 0.0, 0.0, 0.0, 0.0; 

    action_last = Eigen::VectorXd::Zero(action_num);
    root_command.setZero();

    inputdata_nlp = Eigen::VectorXd::Zero(obs_num);
    outputdata_nlp = Eigen::VectorXd::Zero(action_num);
    nlpout = Eigen::VectorXd::Zero(action_num);

    history_dof_pos = Eigen::VectorXd::Zero(action_num * long_hist_len);
    history_dof_vel = Eigen::VectorXd::Zero(action_num * long_hist_len);
    history_action = Eigen::VectorXd::Zero(action_num * long_hist_len);
    history_gravity = Eigen::VectorXd::Zero(3 * long_hist_len);
    history_angvel = Eigen::VectorXd::Zero(3 * long_hist_len);

    nlp_mimic_model = new NlpMimicModel();
    nlp_mimic_model->init(policy_cpkt, obs_num, action_num);
}


