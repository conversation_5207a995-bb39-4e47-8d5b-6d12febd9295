#include "../include/GaitMimic.h"

GaitMimic::Gait<PERSON>imic() {}

GaitMimic::~<PERSON>ait<PERSON>imic() {}


void GaitMimic::init(double _dt, DataPackage *data) {
    default_dof_pos = Eigen::VectorXd::Zero(action_num);
    default_dof_pos << -0.15, 0.0, 0.0, 0.3, -0.15, 0,
    -0.15, 0.0, 0.0, 0.3, -0.15, 0,
    0.0, 
    0.0, 0.0, 0.0, 0.0, 0.0, 0.0; 

    action_last = Eigen::VectorXd::Zero(action_num);

    inputdata_nlp = Eigen::VectorXd::Zero(obs_num);
    outputdata_nlp = Eigen::VectorXd::Zero(action_num);
    nlpout = Eigen::VectorXd::Zero(action_num);

    history_dof_pos = Eigen::VectorXd::Zero(action_num * long_hist_len);
    history_dof_vel = Eigen::VectorXd::Zero(action_num * long_hist_len);
    history_action = Eigen::VectorXd::Zero(action_num * long_hist_len);
    history_gravity = Eigen::VectorXd::Zero(3 * long_hist_len);
    history_angvel = Eigen::VectorXd::Zero(3 * long_hist_len);

    // init model
    nlp_mimic_model = new NlpMimicModel();
    nlp_mimic_model->init(policy_cpkt, obs_num, action_num);

    // init the motion_lib
    motion_frames = LoadMotionLib(motion_path, false);
}

Eigen::MatrixXd GaitMimic::LoadMotionLib(const std::string& filename, bool skipHeader) {
    std::ifstream file(filename);
    std::string line;
    std::vector<std::vector<double>> data;

    // read w/o process
    if (skipHeader) {
        std::getline(file, line);
    }

    while (std::getline(file, line)) {
        std::stringstream ss(line);
        std::string value;
        std::vector<double> row;

        while (std::getline(ss, value, ',')) {
            try {
                row.push_back(std::stod(value));
            } catch (...) {
                row.push_back(0.0);  // 可以自定义处理方式
            }
        }

        if (!row.empty())
            data.push_back(row);
    }

    if (data.empty()) {
        throw std::runtime_error("No data found in file: " + filename);
    }

    int rows = data.size();
    int cols = data[0].size();
    num_motion_frames = rows;
    Eigen::MatrixXd mat(rows, cols);

    for (int i = 0; i < rows; ++i)
        mat.row(i) = Eigen::VectorXd::Map(&data[i][0], cols);
    std::cout << "motion lib loaded !!!" << std::endl;

    return mat;
}
