#ifndef GAITRL_H_
#define GAITRL_H_
#include "NlpModel/include/NlpModel.h"
#include "DataPackage/include/DataPackage.h"
#include <vector>
#include <Eigen/Geometry>



class GaitRL {
  public:
    GaitRL();
    ~GaitRL();
    DataPackage *robotdata;
    void init(double _dt,
              DataPackage *data);


  private:


  public:

    Eigen::Vector3d command;
    // perform freq Hz
    Eigen::VectorXd inputdata_nlp;          //
    Eigen::VectorXd inputdata_nlp_encoder;  //
    Eigen::VectorXd outputdata_nlp;         //
    Eigen::VectorXd outputdata_nlp_encoder; //

    Eigen::VectorXd history_encoder_obs;

    Eigen::VectorXd nlpout;
    Eigen::VectorXd history_pos;     //
    Eigen::VectorXd history_vel;     //
    Eigen::VectorXd history_action;  //
    Eigen::VectorXd history_gravity; //
    Eigen::VectorXd history_angvel;  //

    Eigen::VectorXd history_pos_obs; //
    Eigen::VectorXd history_vel_obs; //

    int action_num = 19;
    int obs_num = 3+3+3+9+8+action_num*3+action_num*10+16;
    int unit_history_obs_num = 3+3+9+8+action_num*3;
    int encoder_history_length = 20;
    int encoder_num = (3+3+9+8+action_num*3) * encoder_history_length;

    int latent_num = 19;
    
    int his_len = 10;
    int his_len_obs = 5;

    int lstm_layers = 0;
    int lstm_hidden_size = 0;
    // nlp output data
    double obs_scales_lin_vel = 1.0;
    double obs_scales_ang_vel = 0.25;
    Eigen::Vector3d command_scales;
    
    double obs_scales_dof_pos = 1.0;
    // double obs_scales_dof_vel = 0.05;
    double obs_scales_dof_vel = 0.05;
    Eigen::VectorXd default_dof_pos;
    Eigen::VectorXd action_last;
    double action_scales = 0.1;
    double action_clip = 50;

    std::string model_pb = "../PythonModule/source/0616/cpg_policy.pt";
    std::string encoder_pb = "../PythonModule/source/0616/cpg_encoder.pt";

    NlpModel *nlpmodel;

  #define COUPLING_CHANGE_RATE_INCREASE_BASE 4.0
  #define COUPLING_CHANGE_RATE_DECREASE_BASE 1.0
  #define AMPLITUDE_CHANGE_RATE_INCREASE_BASE 1.05
  #define AMPLITUDE_CHANGE_RATE_DECREASE_BASE 0.995

    int num_feet = 4;
    double step_dt;
    double gait_freqency;
    Eigen::Vector4d cycle_r;
    double cycle_r_max = 1.0;
    double cycle_r_min = 0.001;
    Eigen::Vector4d px;
    Eigen::Vector4d py;
    double rate = 10.0;
    Eigen::Vector4d phase_offset;
    Eigen::Vector4d phase_offset_target;
    double phase_offset_change_rate;
    Eigen::Vector4d phase_offset_stand;
    Eigen::Vector4d phase_offset_walk;
    Eigen::Vector4d phase_offset_run;
    Eigen::Vector4d coupling_change_rate;

    Eigen::Vector4d contact_ratio;
    double coff_b = 1000.0;
    double coupling_change_rate_increase;
    double coupling_change_rate_decrease;
    Eigen::Vector4d amplitude_change_rate;
    double amplitude_change_rate_increase;
    double amplitude_change_rate_decrease;

    bool walk_flag = false;
    bool stand_flag = false;
    // bool run_flag = false;

    void CPGInit(int num_feet, float dt, float frequency);
    Eigen::VectorXd CPGGammaCal();
    Eigen::MatrixXd CPGCoupling();
    void CPGPhaseChange();
    void CPGHopfOscillator();
    void CPGReset(bool flag);
    void CPGSetCycleR(Eigen::VectorXd Cycle_r);
    void CPGSetCouplingChangeRate(Eigen::VectorXd Coupling_change_rate);
    void CPGSetAmplitudeChangeRate(Eigen::VectorXd Amplitude_change_rate);
    void CPGSetPhaseOffset(Eigen::VectorXd Phase_offset);
    void CPGSetContactRatio(Eigen::VectorXd Contact_ratio);
    void CPGSetFrequency(double frequency);
    void CPGSetStandPattern();
    // void CPGSetWalkPattern();
    // void CPGSetRunPattern();
    void CPGSetWalkPattern_1();
    void CPGSetWalkPattern_2();
    void CPGSetWalkPattern_3();
    void CPGSetWalkPattern_4();
    void CPGSetWalkPattern_5();
    void CPGSetWalkPattern_6();

    Eigen::VectorXd CPGGetXNorm();
    Eigen::VectorXd CPGGetYNorm();
    // void UpdateGaitGeneratorPattern(Eigen::VectorXd Commands,Eigen::VectorXd Projected_grav);
    void UpdateGaitGeneratorPattern(Eigen::VectorXd Commands, double mode);
    void CommandRefinement();
    // void writeDataToFile(Eigen::VectorXd data);
};
#endif
