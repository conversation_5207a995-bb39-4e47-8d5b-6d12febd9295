#ifndef GAITPLANNER_H
#define GAITPLANNER_H


#include "DataPackage/include/DataPackage.h"
#include <eigen3/Eigen/Dense>
#include <vector>
#include "yaml-cpp/yaml.h"

class GaitPlanner{

public:

    Eigen::Matrix<double, Eigen::Dynamic,1> zero_position;
    std::string Gait_state;
    double base_heignt, step_height, foot_height, com_height;
    double T_DSP;    // Double support time
    double T_SSP;     // Single support time
    double g;
    double lambda;
    double uy_l_ref,uy_r_ref;
    double contact_force_start_threshold;
    double contact_force_change_threshold;
    double contact_velocity;
    double gait_vx_desire{0}, gait_vy_desire{0};
    double acc_max;
    double T_jump, jump_height;
    double fall_velocity;
    double up_velocity;
    double feedback_factor_vy;
    double feedback_factor_vx;
    double ux_threshold;
    double vx_threshold;
    double uy_threshold;
    double Kp_vy;
    double u_B_offset_x;
    double foot_yaw_offset;

    double t_afterImpact, t_beforeImpact;

    std::string leftArm_file_path, rightArm_file_path;
    Eigen::MatrixXd leftArm_joint_pos, rightArm_joint_pos;


    double dt,T_START{0},t{0};
    int FootNum{1};
    Eigen::Vector3d base_Pos_actual, base_LinVel_actual;
    Eigen::Vector3d base_Pos_desire, base_LinVel_desire;
    Eigen::Vector3d feet_r_Pos_W_actual, feet_l_Pos_W_actual;
    Eigen::Vector3d feet_r_Pos_W_desire, feet_l_Pos_W_desire;
    Eigen::Vector3d feet_r_LinVel_W_desire, feet_l_LinVel_W_desire;
    Eigen::Vector3d feet_r_Pos_PhaseStart_W, feet_l_Pos_PhaseStart_W;
    Eigen::Vector3d base_Pos_PhaseStart;
    Eigen::Matrix<double, Eigen::Dynamic,1> contact_force;  
    std::vector<Eigen::VectorXd> contact_force_history;
    std::vector<double> speed_forward_history;
    std::vector<bool> contact_flag;
    double speed_forward_avg;

    double T, xL, xR, yL, yR, zL, zR;
    double y_start_speed;
    double px_support, py_support;
    bool IfFirstStand;
    bool IfWalkStart;
    bool IfGaitEnd;
    bool IfStand2Walk;
    bool IfCanStop;
    bool IfFly2Wlak;
    double t_con;
    double contact_swing_force_change;
    double contact_check_time;
    double WalkStartTime;
    double swingFactor{0}, swingR{0}, swingL{0};
    double ux, uy;
    Eigen::Vector2d u_B, u_W;
    Eigen::Vector2d P_B, V_B;
    Eigen::Vector2d P_W, V_W;
    double vx_Final{0}, px_Final;
    double vy_Final{0};
    double py_ref,vy_ref;
    double vy_control;

    double pre_swing_time;

    Eigen::Matrix<double, 5, 1> arm_l_JointPos_GaitStart, arm_r_JointPos_GaitStart;
    Eigen::Matrix<double, 5, 1> arm_l_JointPos_actual, arm_r_JointPos_actual;
    Eigen::Matrix<double, 5, 1> arm_l_JointPos_desire, arm_r_JointPos_desire;
    Eigen::Matrix<double, 5, 1> arm_l_JointVel_desire, arm_r_JointVel_desire;
    Eigen::VectorXd leftarm_joint_behind, leftarm_joint_front;
    Eigen::VectorXd rightarm_joint_behind, rightarm_joint_front;
    Eigen::Vector3d feet_l_Pos_StandStart_W, feet_r_Pos_StandStart_W;
    Eigen::Vector3d base_Pos_StandStart_W;

    double gait_OmegaZ_desire{0};
    double base_EulerZ_actual;
    double base_EulerZ_desire;
    double base_OmegaZ_actual;
    double base_OmegaZ_desire;
    double feet_l_EulerZ_W_actual, feet_r_EulerZ_W_actual;
    double feet_l_EulerZ_W_desire, feet_r_EulerZ_W_desire;
    double feet_r_EulerZ_PhaseStart_W, feet_l_EulerZ_PhaseStart_W;
    double feet_l_OmegaZ_W_desire, feet_r_OmegaZ_W_desire;
    double base_EulerZ_PhaseStart;

    double feet_l_EulerY_W_desire, feet_r_EulerY_W_desire;
    double feet_l_OmegaY_W_desire, feet_r_OmegaY_W_desire;
    double foot_distance_max;
    double foot_EulerY_max;

    bool isWalking;

    double stand_up_pos{0};
    double stand_forward_pos{0};
    double stand_left_pos{0};
    double stand_yaw_pos{0};

    double waist_pos_desire{0};

    double step_length_max_X, step_length_max_Y, step_length_min_Y;

    double stand_up_vel;

    double stand_offset_x;

    GaitPlanner(DataPackage &DataPackage, std::string state);

    void loadConfig(const std::string& filename);
    void GetDataFromPackage(DataPackage &DataPackage);
    void SetDataToPackage(DataPackage &DataPackage);
    void RotationByBase();
    void Stand();
    void StandUPandDOWN();
    void Stand2Walk();
    void Walk();
    void Fly2Walk();
    void Fly();
    void getCurrentSpeedAsVx_Final();
    // void Step2();
    void FirstStep();
    // void CheckGaitPhase();
    void CheckLegChange(double Total_time);
    void RecordPhaseStartPos();
    void RecordStandStartPos();
    void RecordGaitStartArmJointPos();
    void updateContactFlags(const std::string& command, std::vector<bool>& contact_flag);
    bool CheckSwingContact();
    void CheckFlyLegChange(double Total_time);
    void CheckFly2WalkEnd();
    void ArmJointPlan(double TotalTime);
    void ArmJointRef();
    void CalculateAvgSpeedForward();
    Eigen::MatrixXd readCSV(const std::string& filename);
    void interpolateJointPosVel(
        const Eigen::MatrixXd& arm_joint_pos, // 行：时间，列：关节
        double t_arm, double T_arm_total,                     // 当前时间和总时长
        Eigen::Matrix<double, 5, 1>& arm_JointPos_desire,  
        Eigen::Matrix<double, 5, 1>& arm_JointVel_desire
    ) ;



};

// double queryBSplineValueAndSlope(const std::vector<double>& time_points, const std::vector<double>& values, double t_sp, double& slope);
// double queryHermiteSplineValueAndSlope2(const std::vector<double>& time_points, const std::vector<double>& values, const std::vector<double>& slopes, double t_sp, double& slope);
void Thirdpoly(double p0, double p0_dot, double p1, double p1_dot,
               double totalTime,   // total permating time
               double currenttime, // current time,from 0 to total time
               double &pd, double &pd_dot);

void ThirdpolyPlus(double p0, double p0_dot, double p1, double p1_dot,
               double totalTime,   // total permating time
               double currenttime, // current time,from 0 to total time
               double &pd, double &pd_dot, double midtime, double pmid, double pmid_dot);

Eigen::VectorXd parseVector(const YAML::Node& node);
Eigen::Matrix2d rotation_matrix_plane(double angle);
Eigen::Vector2d computeLinearVelocity(const Eigen::Vector2d& r, double omega);
bool dataLog(Eigen::VectorXd &v, std::ofstream &f);

#endif 