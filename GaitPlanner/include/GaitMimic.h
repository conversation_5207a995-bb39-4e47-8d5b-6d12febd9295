#ifndef GAITMIMIC_H_
#define GAITMIMIC_H_
#include "NlpModel/include/NlpMimicModel.h"
#include "DataPackage/include/DataPackage.h"
#include <vector>
#include <fstream>
#include <Eigen/Geometry>


class GaitMimic {
    public:
        GaitMimic();
        ~GaitMimic();
        DataPackage *robotdata;
        void init(double _dt, DataPackage *data);

        Eigen::VectorXd root_command;
        Eigen::VectorXd inputdata_nlp;
        Eigen::VectorXd outputdata_nlp;
        Eigen::VectorXd nlpout;
        Eigen::VectorXd history_dof_pos;
        Eigen::VectorXd history_dof_vel;
        Eigen::VectorXd history_action;
        Eigen::VectorXd history_gravity;
        Eigen::VectorXd history_angvel;
        Eigen::VectorXd history_obs_buf;

        int action_num = 21;
        int keybody_num = 10;
        int proprio_num = 4 + 3 + 3 + 3 * action_num;
        int fut_step_num = 3;
        int task_obs_num = (1 + 6 + 3 + 3 + action_num + 6 * keybody_num) * fut_step_num;
        int long_hist_len = 20;
        int short_hist_len = 5;
        int obs_num = proprio_num + task_obs_num + long_hist_len * proprio_num;

        double obs_scales_dof_pos = 1.0;
        double obs_scales_dof_vel = 0.05;
        double obs_scales_ang_vel = 0.25;

        Eigen::VectorXd default_dof_pos;
        Eigen::VectorXd action_last;
        double action_scales = 0.1;
        double action_clip = 5;
        
        std::string policy_cpkt = "../PythonModule/source/mimic/mimic_policy.pt";
        std::string motion_path = "../PythonModule/source/mimic/140_17_poses.csv";
        int num_motion_frames;

        NlpMimicModel *nlp_mimic_model;

        // read csv format ref motion frames
        Eigen::MatrixXd motion_frames;
        Eigen::MatrixXd LoadMotionLib(const std::string& filename, bool skipHeader);
};
#endif
