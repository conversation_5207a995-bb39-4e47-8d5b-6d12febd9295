#ifndef DATALOGGER_H 
#define DATALOGGER_H

#include <eigen3/Eigen/Dense>
#include <vector>
#include "yaml-cpp/yaml.h"
#include "pinocchio/parsers/urdf.hpp"
#include "pinocchio/algorithm/joint-configuration.hpp"
#include "pinocchio/algorithm/kinematics.hpp"
#include <mujoco/mujoco.h>
#include "DataPackage/include/DataPackage.h"
#include <fstream>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <ctime>  // 用于获取系统时间
#include <filesystem>  // 用于创建文件夹
#include "mujoco_interface.h"
#include "StateEstimator/include/StateEstimator.h"

namespace fs = std::filesystem;

class DataLogger
{
public:
    DataLogger() {
        // 获取当前系统时间
        std::time_t now = std::time(nullptr);
        std::tm* localTime = std::localtime(&now);

        // 格式化时间为字符串，作为文件夹和文件名
        char buffer[80];
        std::strftime(buffer, sizeof(buffer), "%Y%m%d_%H%M%S", localTime);
        std::string folderName = "../log/" + std::string(buffer);  // 文件夹路径
        std::string txtFileName = folderName + "/log.txt";  // txt 文件路径
        std::string csvFileName = folderName + "/data.csv";  // csv 文件路径
        std::string MotorPoscsvFileName = folderName + "/MotorPos.csv";  // csv 文件路径

        // 创建文件夹
        if (!fs::exists(folderName)) {
            fs::create_directories(folderName);
        }

        // 打开文件并检查是否成功
        logFile.open(txtFileName, std::ios::out);
        csvFile.open(csvFileName, std::ios::out);
        MotorPoscsvFile.open(MotorPoscsvFileName, std::ios::out);
        if (!logFile.is_open() || !csvFile.is_open() || !MotorPoscsvFile.is_open()) {
            std::cerr << "Error: Unable to create log or CSV file." << std::endl;
        } else {
            std::cout << "Log files created in folder: " << folderName << std::endl;
        }
    }

    ~DataLogger() {
        if (logFile.is_open()) {
            logFile.close();
        }
        if (csvFile.is_open()) {
            csvFile.close();
        }
        if (MotorPoscsvFile.is_open()) {
            MotorPoscsvFile.close();
        }
    }

    void SaveDataToFile(const DataPackage& dataPackage, double t, mjData* mj_data, Eigen::Vector3d velocity, StateEstimator state_estimator);
    void SaveDataToFile(const DataPackage& dataPackage, double t, StateEstimator state_estimator);

    std::stringstream ss_log;
    std::stringstream ss_csv;
    std::stringstream ss_motor;

    bool first_log = true;

    template <typename VecType>
    void writeVec(const VecType& vec) {
        for (int i = 0; i < vec.size(); ++i)
            ss_csv << "," << vec[i];
    }


private:
    std::ofstream logFile;  // txt 文件流对象
    std::ofstream csvFile;  // csv 文件流对象

    std::ofstream MotorPoscsvFile;  // csv 文件流对象
};

#endif // DATALOGGER_H