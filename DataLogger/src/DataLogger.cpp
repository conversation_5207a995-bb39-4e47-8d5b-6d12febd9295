#include "DataLogger/include/DataLogger.h"


// void DataLogger::SaveDataToFile(const DataPackage& dataPackage, double t, mjData* mj_data, Eigen::Vector3d velocity, StateEstimator state_estimator) {
//     // 检查文件是否打开
//     if (!logFile.is_open() || !csvFile.is_open() || !MotorPoscsvFile.is_open()) {
//         std::cerr << "Error: Log file or CSV file is not open." << std::endl;
//         return;
//     }

//     // 在第一次调用时写入表头
//     if (csvFile.tellp() == 0) {  // 检查是否是文件开始
//         csvFile << "time";
//         csvFile << "," << "generalized_q_actual_1" << "," << "generalized_q_actual_2" << "," << "generalized_q_actual_3";
//         csvFile << "," << "generalized_q_desired_1" << "," << "generalized_q_desired_2" << "," << "generalized_q_desired_3";
//         csvFile << "," << "generalized_q_dot_actual_1" << "," << "generalized_q_dot_actual_2" << "," << "generalized_q_dot_actual_3";
//         csvFile << "," << "generalized_q_dot_desired_1" << "," << "generalized_q_dot_desired_2" << "," << "generalized_q_dot_desired_3";
//         csvFile << "," << "feet_l_Pos_W_actual_1" << "," << "feet_l_Pos_W_actual_2" << "," << "feet_l_Pos_W_actual_3";
//         csvFile << "," << "feet_l_Pos_W_desire_1" << "," << "feet_l_Pos_W_desire_2" << "," << "feet_l_Pos_W_desire_3";
//         csvFile << "," << "feet_r_Pos_W_actual_1" << "," << "feet_r_Pos_W_actual_2" << "," << "feet_r_Pos_W_actual_3";
//         csvFile << "," << "feet_r_Pos_W_desire_1" << "," << "feet_r_Pos_W_desire_2" << "," << "feet_r_Pos_W_desire_3";
//         csvFile << "," << "left_contactforce" << "," << "right_contactforce";
//         csvFile << "," << "T_START";
//         csvFile << "," << "mj_data->xanchor[41]";
//         // csvFile << "," << "mj_data->xanchor[2]";
//         csvFile << "," << "mj_data->xanchor[39]";
//         csvFile << "," << "mj_data->xanchor[40]";
//         csvFile << "," << "feet_r_LinVel_W_actual_1" << "," << "feet_r_LinVel_W_actual_2" << "," << "feet_r_LinVel_W_actual_3";
//         csvFile << "," << "feet_r_LinVel_W_mj_data_1" << "," << "feet_r_LinVel_W_mj_data_2" << "," << "feet_r_LinVel_W_mj_data_3";
//         // csvFile << "," << "feet_r_LinVel_W_actual_1+base" << "," << "feet_r_LinVel_W_actual_2+base" << "," << "feet_r_LinVel_W_actual_3+base";
//         csvFile << "," << "feet_r_LinVel_W_actual_1_jacobi" << "," << "feet_r_LinVel_W_actual_2_jacobi" << "," << "feet_r_LinVel_W_actual_3_jacobi";
//         csvFile << "," << "feet_r_LinVel_W_desire_1" << "," << "feet_r_LinVel_W_desire_2" << "," << "feet_r_LinVel_W_desire_3";
//         csvFile << "," << "generalized_q_mujoco_1" << "," << "generalized_q_mujoco_2" << "," << "generalized_q_mujoco_3";
//         csvFile << "," << "generalized_q_dot_mujoco_1" << "," << "generalized_q_dot_mujoco_2" << "," << "generalized_q_dot_mujoco_3";
//         csvFile << "," << "feet_l_Pos_W_mujoco_1" << "," << "feet_l_Pos_W_mujoco_2" << "," << "feet_l_Pos_W_mujoco_3";
//         csvFile << "," << "feet_distance_W_1" << "," << "feet_distance_W_2";
//         csvFile << "," << "FootNum" ;
//         csvFile << "," << "js_vx_desire" << "," << "gait_vx_desire" << "," << "speed_forward_avg";
//         csvFile << "," << "base_EulerZ_actual" << "," << "base_EulerZ_desire" ;
//         csvFile << "," << "u_B_1" << "," << "u_B_2";
//         csvFile << "," << "u_W_1" << "," << "u_W_2";
//         csvFile << "," << "vx_Final" ;
//         csvFile << "," << "vy_Final" << "," << "vy_ref";
//         csvFile << "," << "feet_l_Pos_PhaseStart_W_1" << "," << "feet_l_Pos_PhaseStart_W_2";
//         csvFile << "," << "feet_r_Pos_PhaseStart_W_1" << "," << "feet_r_Pos_PhaseStart_W_2";
//         for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
//             csvFile << "," << "Torque_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
//             csvFile << "," << "Pos_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.motor_vel.size(); ++i) {
//             csvFile << "," << "Vel_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//             csvFile << "," << "Pos_Joint_desire_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//             csvFile << "," << "Vel_Joint_desire_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         csvFile << "," << "feet_r_EulerZ_W_actual" << "," << "feet_r_EulerZ_W_desire";
//         csvFile << "," << "t_con" ;
//         for (int i = 1; i <= dataPackage.feet_l_Pos_W_IK.size(); ++i) {
//             csvFile << "," << "feet_l_Pos_W_IK_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 1; i <= dataPackage.feet_r_Pos_W_IK.size(); ++i) {
//             csvFile << "," << "feet_r_Pos_W_IK_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.feet_l_EulerZYX_W_actual.size(); ++i) {
//             csvFile << "," << "feet_l_EulerZYX_W_actual_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.feet_r_EulerZYX_W_actual.size(); ++i) {
//             csvFile << "," << "feet_r_EulerZYX_W_actual_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         csvFile << std::endl;
//     }

//     // 使用字符串流来合并输出
//     std::stringstream ss;

//     // 打印 simulate_time
//     ss << "simulate_time: " << t << " ";

//     // // // 打印 generalized_q_actual
//     // ss << "generalized_q_actual: ";
//     // for (int i = 0; i < 3; ++i) {
//     //     ss << dataPackage.generalized_q_actual[i] << " ";
//     // }

//     // ss << "feet_l_Pos_W_actual: ";
//     // for (int i = 0; i < 3; ++i) {
//     //     ss << dataPackage.feet_l_Pos_W_actual[i] << " ";
//     // }

//     // ss << "feet_r_Pos_W_actual: ";
//     // for (int i = 0; i < 3; ++i) {
//     //     ss << dataPackage.feet_r_Pos_W_actual[i] << " ";
//     // }

//     ss << "contact_flag: ";
//     for (int i = 0; i < dataPackage.contact_flag.size(); ++i) {
//         ss << dataPackage.contact_flag[i] << " ";
//     }

//     // ss << "torq_desire: ";
//     // for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
//     //     ss << dataPackage.torq_desire[i] << " ";
//     // }

//     // ss << "gait_vx_desire: ";
//     // ss << dataPackage.gait_vx_desire << " ";

//     ss << "CurrentState: ";
//     ss << dataPackage.CurrentState << " ";

//     ss << "NextState: ";
//     ss << dataPackage.NextState << " ";

//     // ss << "IfWalkStart: ";
//     // ss << dataPackage.IfWalkStart << " ";

//     // ss << "FootNum: ";
//     // ss << dataPackage.FootNum << " ";

//     ss << "js_vx_desire: ";
//     ss << dataPackage.js_vx_desire << " ";

//     ss << "gait_vx_desire: ";
//     ss << dataPackage.gait_vx_desire << " ";

//     ss << "speed_forward_avg: ";
//     ss << dataPackage.speed_forward_avg << " ";

//     ss << "gait_OmegaZ_desire: ";
//     ss << dataPackage.gait_OmegaZ_desire << " ";

//     // ss << "isWalking: ";
//     // ss << dataPackage.isWalking << " ";

//     ss << "base_EulerZ_desire: ";
//     ss << dataPackage.base_EulerZ_desire << " ";

//     ss << "gait_vy_desire: ";
//     ss << dataPackage.gait_vy_desire << " ";

//     ss << std::endl;
//     ss << "************************************";
//     // 将数据写入 log.txt 文件
//     logFile << ss.str() << std::endl;

//     // 打印到控制台（可选）
//     std::cout << ss.str() << std::endl;

//     // 将数据写入 csv 文件
//     csvFile << t;  // 写入时间戳

//     // 打印 generalized_q_actual
//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_actual[i];
//     }

//     // 打印 generalized_q_desired
//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_desired[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_dot_actual[i];
//     }   

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_dot_desired[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_actual[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_desire[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_W_actual[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_W_desire[i];
//     }


//     csvFile << "," << dataPackage.contact_force[2];
//     csvFile << "," << dataPackage.contact_force[8];
//     csvFile << "," << dataPackage.T_START;
//     csvFile << "," << mj_data->xanchor[41];
//     // csvFile << "," << mj_data->xanchor[2];
//     csvFile << "," << mj_data->xanchor[39];
//     csvFile << "," << mj_data->xanchor[40];

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_LinVel_W_actual[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << velocity[i];
//     }

//     // for (int i = 0; i < 3; ++i) {
//     //     csvFile << "," << dataPackage.feet_r_LinVel_W_actual[i]+dataPackage.generalized_q_dot_actual[i];
//     // }

//     Eigen::VectorXd feet_r_LinVel_W_actual_jacobi = state_estimator.rightfoot_jacobi*dataPackage.generalized_q_dot_actual;

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << feet_r_LinVel_W_actual_jacobi[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_LinVel_W_desire[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.base_pos[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.base_lin_vel[i];
//     }

//     for (int i = 18; i < 21; ++i) {
//         csvFile << "," << mj_data->xanchor[i];
//     }

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_actual[i]-dataPackage.feet_r_Pos_W_actual[i];
//     }

//     csvFile << "," << dataPackage.FootNum;
//     csvFile << "," << dataPackage.js_vx_desire;
//     csvFile << "," << dataPackage.gait_vx_desire;
//     csvFile << "," << dataPackage.speed_forward_avg;

//     csvFile << "," << dataPackage.generalized_q_actual[3];
//     csvFile << "," << dataPackage.base_EulerZ_desire;

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.u_B[i];
//     }

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.u_W[i];
//     }

//     csvFile << "," << dataPackage.vx_Final;
//     csvFile << "," << dataPackage.vy_Final;
//     csvFile << "," << dataPackage.vy_ref;

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_PhaseStart_W[i];
//     }

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_PhaseStart_W[i];
//     }


//     for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
//         csvFile << "," << dataPackage.torq_desire[i];
//     }

//     for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
//         csvFile << "," << dataPackage.motor_pos[i];
//     }
//     for (int i = 0; i < dataPackage.motor_vel.size(); ++i) {
//         csvFile << "," << dataPackage.motor_vel[i];
//     }
//     for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//         csvFile << "," << dataPackage.generalized_q_desired.tail(dataPackage.actuatedDofNum)[i];
//     }
//     for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//         csvFile << "," << dataPackage.generalized_q_dot_desired.tail(dataPackage.actuatedDofNum)[i];
//     }
//     csvFile << "," << dataPackage.feet_r_EulerZ_W_actual;
//     csvFile << "," << dataPackage.feet_r_EulerZ_W_desire;
//     csvFile << "," << dataPackage.t-dataPackage.T_START;

//     for (int i = 0; i < dataPackage.feet_l_Pos_W_IK.size(); ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_IK[i];
//     }
//     for (int i = 0; i < dataPackage.feet_r_Pos_W_IK.size(); ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_W_IK[i];
//     }
//     for (int i = 0; i < dataPackage.feet_l_EulerZYX_W_actual.size(); ++i) {
//         csvFile << "," << dataPackage.feet_l_EulerZYX_W_actual[i];
//     }
//     for (int i = 0; i < dataPackage.feet_r_EulerZYX_W_actual.size(); ++i) {
//         csvFile << "," << dataPackage.feet_r_EulerZYX_W_actual[i];
//     }
    
//     // 换行
//     csvFile << std::endl;

//     for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
//         MotorPoscsvFile << dataPackage.motor_pos[i];
//         if(i < dataPackage.motor_pos.size()-1){
//             MotorPoscsvFile << ",";
//         }
//     }
//     MotorPoscsvFile << std::endl;



// }

void DataLogger::SaveDataToFile(const DataPackage& dataPackage, double t, mjData* mj_data, Eigen::Vector3d velocity, StateEstimator state_estimator) {
    // 检查文件是否打开
    if (!csvFile.is_open() ) {
        std::cerr << "Error: Log file or CSV file is not open." << std::endl;
        return;
    }

    if (first_log) {
        ss_csv.str("");
        ss_csv.clear();
        ss_csv << "time";
        ss_csv << "," << "generalized_q_actual_1" << "," << "generalized_q_actual_2" << "," << "generalized_q_actual_3";
        ss_csv << "," << "generalized_q_desired_1" << "," << "generalized_q_desired_2" << "," << "generalized_q_desired_3";
        ss_csv << "," << "generalized_q_dot_actual_1" << "," << "generalized_q_dot_actual_2" << "," << "generalized_q_dot_actual_3";
        ss_csv << "," << "generalized_q_dot_desired_1" << "," << "generalized_q_dot_desired_2" << "," << "generalized_q_dot_desired_3";
        ss_csv << "," << "feet_l_Pos_W_actual_1" << "," << "feet_l_Pos_W_actual_2" << "," << "feet_l_Pos_W_actual_3";
        ss_csv << "," << "feet_l_Pos_W_desire_1" << "," << "feet_l_Pos_W_desire_2" << "," << "feet_l_Pos_W_desire_3";
        ss_csv << "," << "feet_r_Pos_W_actual_1" << "," << "feet_r_Pos_W_actual_2" << "," << "feet_r_Pos_W_actual_3";
        ss_csv << "," << "feet_r_Pos_W_desire_1" << "," << "feet_r_Pos_W_desire_2" << "," << "feet_r_Pos_W_desire_3";
        ss_csv << "," << "left_contactforce" << "," << "right_contactforce";
        ss_csv << "," << "T_START";
        ss_csv << "," << "mj_data->xanchor[41]";
        // ss_csv << "," << "mj_data->xanchor[2]";
        ss_csv << "," << "mj_data->xanchor[39]";
        ss_csv << "," << "mj_data->xanchor[40]";
        ss_csv << "," << "feet_r_LinVel_W_actual_1" << "," << "feet_r_LinVel_W_actual_2" << "," << "feet_r_LinVel_W_actual_3";
        ss_csv << "," << "feet_r_LinVel_W_mj_data_1" << "," << "feet_r_LinVel_W_mj_data_2" << "," << "feet_r_LinVel_W_mj_data_3";
        // ss_csv << "," << "feet_r_LinVel_W_actual_1+base" << "," << "feet_r_LinVel_W_actual_2+base" << "," << "feet_r_LinVel_W_actual_3+base";
        ss_csv << "," << "feet_r_LinVel_W_actual_1_jacobi" << "," << "feet_r_LinVel_W_actual_2_jacobi" << "," << "feet_r_LinVel_W_actual_3_jacobi";
        ss_csv << "," << "feet_r_LinVel_W_desire_1" << "," << "feet_r_LinVel_W_desire_2" << "," << "feet_r_LinVel_W_desire_3";
        ss_csv << "," << "generalized_q_mujoco_1" << "," << "generalized_q_mujoco_2" << "," << "generalized_q_mujoco_3";
        ss_csv << "," << "generalized_q_dot_mujoco_1" << "," << "generalized_q_dot_mujoco_2" << "," << "generalized_q_dot_mujoco_3";
        ss_csv << "," << "feet_l_Pos_W_mujoco_1" << "," << "feet_l_Pos_W_mujoco_2" << "," << "feet_l_Pos_W_mujoco_3";
        ss_csv << "," << "feet_distance_W_1" << "," << "feet_distance_W_2";
        ss_csv << "," << "FootNum" ;
        ss_csv << "," << "js_vx_desire" << "," << "gait_vx_desire" << "," << "speed_forward_avg";
        ss_csv << "," << "base_EulerZ_actual" << "," << "base_EulerZ_desire" ;
        ss_csv << "," << "u_B_1" << "," << "u_B_2";
        ss_csv << "," << "u_W_1" << "," << "u_W_2";
        ss_csv << "," << "vx_Final" ;
        ss_csv << "," << "vy_Final" << "," << "vy_ref";
        ss_csv << "," << "feet_l_Pos_PhaseStart_W_1" << "," << "feet_l_Pos_PhaseStart_W_2";
        ss_csv << "," << "feet_r_Pos_PhaseStart_W_1" << "," << "feet_r_Pos_PhaseStart_W_2";
        for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
            ss_csv << "," << "Torque_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }
        for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
            ss_csv << "," << "Pos_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }
        for (int i = 0; i < dataPackage.motor_vel.size(); ++i) {
            ss_csv << "," << "Vel_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }
        for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
            ss_csv << "," << "Pos_Joint_desire_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }
        for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
            ss_csv << "," << "Vel_Joint_desire_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }
        ss_csv << "," << "feet_r_EulerZ_W_actual" << "," << "feet_r_EulerZ_W_desire";
        ss_csv << "," << "t_con" ;
        for (int i = 1; i <= dataPackage.feet_l_Pos_W_IK.size(); ++i) {
            ss_csv << "," << "feet_l_Pos_W_IK_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }
        for (int i = 1; i <= dataPackage.feet_r_Pos_W_IK.size(); ++i) {
            ss_csv << "," << "feet_r_Pos_W_IK_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }

        for (int i = 1; i <= dataPackage.feet_l_EulerZYX_W_actual.size(); ++i) {
            ss_csv << "," << "feet_l_EulerZYX_W_actual_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }

        for (int i = 1; i <= dataPackage.feet_r_EulerZYX_W_actual.size(); ++i) {
            ss_csv << "," << "feet_r_EulerZYX_W_actual_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
        }

        first_log = false;

        ss_csv << '\n';
        csvFile << ss_csv.str();
    }

    auto csvFile_start_time = std::chrono::steady_clock::now();

    ss_csv.str("");
    ss_csv.clear();

    // 将数据写入 csv 文件
    ss_csv << t;  // 写入时间戳

    // 打印 generalized_q_actual
    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.generalized_q_actual[i];
    }

    // 打印 generalized_q_desired
    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.generalized_q_desired[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.generalized_q_dot_actual[i];
    }   

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.generalized_q_dot_desired[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.feet_l_Pos_W_actual[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.feet_l_Pos_W_desire[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.feet_r_Pos_W_actual[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.feet_r_Pos_W_desire[i];
    }


    ss_csv << "," << dataPackage.contact_force[2];
    ss_csv << "," << dataPackage.contact_force[8];
    ss_csv << "," << dataPackage.T_START;
    ss_csv << "," << mj_data->xanchor[41];
    // ss_csv << "," << mj_data->xanchor[2];
    ss_csv << "," << mj_data->xanchor[39];
    ss_csv << "," << mj_data->xanchor[40];

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.feet_r_LinVel_W_actual[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << velocity[i];
    }

    // for (int i = 0; i < 3; ++i) {
    //     ss_csv << "," << dataPackage.feet_r_LinVel_W_actual[i]+dataPackage.generalized_q_dot_actual[i];
    // }

    Eigen::VectorXd feet_r_LinVel_W_actual_jacobi = state_estimator.rightfoot_jacobi*dataPackage.generalized_q_dot_actual;

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << feet_r_LinVel_W_actual_jacobi[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.feet_r_LinVel_W_desire[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.base_pos[i];
    }

    for (int i = 0; i < 3; ++i) {
        ss_csv << "," << dataPackage.base_lin_vel[i];
    }

    for (int i = 18; i < 21; ++i) {
        ss_csv << "," << mj_data->xanchor[i];
    }

    for (int i = 0; i < 2; ++i) {
        ss_csv << "," << dataPackage.feet_l_Pos_W_actual[i]-dataPackage.feet_r_Pos_W_actual[i];
    }

    ss_csv << "," << dataPackage.FootNum;
    ss_csv << "," << dataPackage.js_vx_desire;
    ss_csv << "," << dataPackage.gait_vx_desire;
    ss_csv << "," << dataPackage.speed_forward_avg;

    ss_csv << "," << dataPackage.generalized_q_actual[3];
    ss_csv << "," << dataPackage.base_EulerZ_desire;

    for (int i = 0; i < 2; ++i) {
        ss_csv << "," << dataPackage.u_B[i];
    }

    for (int i = 0; i < 2; ++i) {
        ss_csv << "," << dataPackage.u_W[i];
    }

    ss_csv << "," << dataPackage.vx_Final;
    ss_csv << "," << dataPackage.vy_Final;
    ss_csv << "," << dataPackage.vy_ref;

    for (int i = 0; i < 2; ++i) {
        ss_csv << "," << dataPackage.feet_l_Pos_PhaseStart_W[i];
    }

    for (int i = 0; i < 2; ++i) {
        ss_csv << "," << dataPackage.feet_r_Pos_PhaseStart_W[i];
    }


    for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
        ss_csv << "," << dataPackage.torq_desire[i];
    }

    for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
        ss_csv << "," << dataPackage.motor_pos[i];
    }
    for (int i = 0; i < dataPackage.motor_vel.size(); ++i) {
        ss_csv << "," << dataPackage.motor_vel[i];
    }
    for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
        ss_csv << "," << dataPackage.generalized_q_desired.tail(dataPackage.actuatedDofNum)[i];
    }
    for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
        ss_csv << "," << dataPackage.generalized_q_dot_desired.tail(dataPackage.actuatedDofNum)[i];
    }
    ss_csv << "," << dataPackage.feet_r_EulerZ_W_actual;
    ss_csv << "," << dataPackage.feet_r_EulerZ_W_desire;
    ss_csv << "," << dataPackage.t-dataPackage.T_START;

    for (int i = 0; i < dataPackage.feet_l_Pos_W_IK.size(); ++i) {
        ss_csv << "," << dataPackage.feet_l_Pos_W_IK[i];
    }
    for (int i = 0; i < dataPackage.feet_r_Pos_W_IK.size(); ++i) {
        ss_csv << "," << dataPackage.feet_r_Pos_W_IK[i];
    }
    for (int i = 0; i < dataPackage.feet_l_EulerZYX_W_actual.size(); ++i) {
        ss_csv << "," << dataPackage.feet_l_EulerZYX_W_actual[i];
    }
    for (int i = 0; i < dataPackage.feet_r_EulerZYX_W_actual.size(); ++i) {
        ss_csv << "," << dataPackage.feet_r_EulerZYX_W_actual[i];
    }
    
    // 换行
    ss_csv << '\n';

    // auto csvFile_start_time = std::chrono::steady_clock::now();
    auto csvFile_now_time = std::chrono::steady_clock::now();
    std::chrono::duration<double> elapsed_csvFile = csvFile_now_time - csvFile_start_time;
    // std::cout<< "csvFile time:"<<elapsed_csvFile.count()<<std::endl;
    
    csvFile << ss_csv.str();




}

// void DataLogger::SaveDataToFile(const DataPackage& dataPackage, double t, StateEstimator state_estimator) {
//     // 检查文件是否打开
//     if (!logFile.is_open() || !csvFile.is_open() || !MotorPoscsvFile.is_open()) {
//         std::cerr << "Error: Log file or CSV file is not open." << std::endl;
//         return;
//     }

//     // 在第一次调用时写入表头
//     if (csvFile.tellp() == 0) {  // 检查是否是文件开始
//         csvFile << "time";
//         csvFile << "," << "generalized_q_actual_1" << "," << "generalized_q_actual_2" << "," << "generalized_q_actual_3";
//         csvFile << "," << "generalized_q_desired_1" << "," << "generalized_q_desired_2" << "," << "generalized_q_desired_3";
//         csvFile << "," << "generalized_q_dot_actual_1" << "," << "generalized_q_dot_actual_2" << "," << "generalized_q_dot_actual_3";
//         csvFile << "," << "generalized_q_dot_desired_1" << "," << "generalized_q_dot_desired_2" << "," << "generalized_q_dot_desired_3";
//         csvFile << "," << "feet_l_Pos_W_actual_1" << "," << "feet_l_Pos_W_actual_2" << "," << "feet_l_Pos_W_actual_3";
//         csvFile << "," << "feet_l_Pos_W_desire_1" << "," << "feet_l_Pos_W_desire_2" << "," << "feet_l_Pos_W_desire_3";
//         csvFile << "," << "feet_r_Pos_W_actual_1" << "," << "feet_r_Pos_W_actual_2" << "," << "feet_r_Pos_W_actual_3";
//         csvFile << "," << "feet_r_Pos_W_desire_1" << "," << "feet_r_Pos_W_desire_2" << "," << "feet_r_Pos_W_desire_3";
//         csvFile << "," << "left_contactforce" << "," << "right_contactforce";
//         csvFile << "," << "T_START";

//         csvFile << "," << "feet_r_LinVel_W_actual_1" << "," << "feet_r_LinVel_W_actual_2" << "," << "feet_r_LinVel_W_actual_3";
       
//         // csvFile << "," << "feet_r_LinVel_W_actual_1+base" << "," << "feet_r_LinVel_W_actual_2+base" << "," << "feet_r_LinVel_W_actual_3+base";
//         csvFile << "," << "feet_r_LinVel_W_actual_1_jacobi" << "," << "feet_r_LinVel_W_actual_2_jacobi" << "," << "feet_r_LinVel_W_actual_3_jacobi";
//         csvFile << "," << "feet_r_LinVel_W_desire_1" << "," << "feet_r_LinVel_W_desire_2" << "," << "feet_r_LinVel_W_desire_3";
//         csvFile << "," << "generalized_q_mujoco_1" << "," << "generalized_q_mujoco_2" << "," << "generalized_q_mujoco_3";
//         csvFile << "," << "generalized_q_dot_mujoco_1" << "," << "generalized_q_dot_mujoco_2" << "," << "generalized_q_dot_mujoco_3";

//         csvFile << "," << "feet_distance_W_1" << "," << "feet_distance_W_2";
//         csvFile << "," << "FootNum" ;
//         csvFile << "," << "js_vx_desire" << "," << "gait_vx_desire" << "," << "speed_forward_avg";
//         csvFile << "," << "base_EulerZ_actual" << "," << "base_EulerZ_desire" ;
//         csvFile << "," << "u_B_1" << "," << "u_B_2";
//         csvFile << "," << "u_W_1" << "," << "u_W_2";
//         csvFile << "," << "vx_Final" ;
//         csvFile << "," << "vy_Final" << "," << "vy_ref";
//         csvFile << "," << "feet_l_Pos_PhaseStart_W_1" << "," << "feet_l_Pos_PhaseStart_W_2";
//         csvFile << "," << "feet_r_Pos_PhaseStart_W_1" << "," << "feet_r_Pos_PhaseStart_W_2";
//         for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
//             csvFile << "," << "Torque_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
//             csvFile << "," << "Pos_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.motor_vel.size(); ++i) {
//             csvFile << "," << "Vel_Joint_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//             csvFile << "," << "Pos_Joint_desire_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//             csvFile << "," << "Vel_Joint_desire_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         csvFile << "," << "feet_r_EulerZ_W_actual" << "," << "feet_r_EulerZ_W_desire";
//         csvFile << "," << "t_con" ;
//         for (int i = 1; i <= dataPackage.feet_l_Pos_W_IK.size(); ++i) {
//             csvFile << "," << "feet_l_Pos_W_IK_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 1; i <= dataPackage.feet_r_Pos_W_IK.size(); ++i) {
//             csvFile << "," << "feet_r_Pos_W_IK_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.feet_l_EulerZYX_W_actual.size(); ++i) {
//             csvFile << "," << "feet_l_EulerZYX_W_actual_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.feet_r_EulerZYX_W_actual.size(); ++i) {
//             csvFile << "," << "feet_r_EulerZYX_W_actual_" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.imu_zyx.size(); ++i) {
//             csvFile << "," << "imu_zyx" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.imu_angular_vel.size(); ++i) {
//             csvFile << "," << "imu_angular_vel" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.imu_lin_acc.size(); ++i) {
//             csvFile << "," << "imu_lin_acc" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.motor_Torque_desire.size(); ++i) {
//             csvFile << "," << "motor_Torque_desire" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         for (int i = 1; i <= dataPackage.motor_torque.size(); ++i) {
//             csvFile << "," << "motor_torque" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 1; i <= dataPackage.motor_Pos_desire.size(); ++i) {
//             csvFile << "," << "motor_Pos_desire" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }
//         for (int i = 1; i <= dataPackage.motor_Vel_desire.size(); ++i) {
//             csvFile << "," << "motor_Pos_desire" << i;  // 例如: Torque_Joint_0, Torque_Joint_1, ...
//         }

//         csvFile << std::endl;
//     }

//     // 使用字符串流来合并输出
//     std::stringstream ss;

//     // 打印 simulate_time
//     ss << "simulate_time: " << t << " ";

//     // // // 打印 generalized_q_actual
//     // ss << "generalized_q_actual: ";
//     // for (int i = 0; i < 3; ++i) {
//     //     ss << dataPackage.generalized_q_actual[i] << " ";
//     // }

//     // ss << "feet_l_Pos_W_actual: ";
//     // for (int i = 0; i < 3; ++i) {
//     //     ss << dataPackage.feet_l_Pos_W_actual[i] << " ";
//     // }

//     // ss << "feet_r_Pos_W_actual: ";
//     // for (int i = 0; i < 3; ++i) {
//     //     ss << dataPackage.feet_r_Pos_W_actual[i] << " ";
//     // }

//     ss << "contact_flag: ";
//     for (int i = 0; i < dataPackage.contact_flag.size(); ++i) {
//         ss << dataPackage.contact_flag[i] << " ";
//     }

//     // ss << "torq_desire: ";
//     // for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
//     //     ss << dataPackage.torq_desire[i] << " ";
//     // }

//     // ss << "gait_vx_desire: ";
//     // ss << dataPackage.gait_vx_desire << " ";

//     ss << "CurrentState: ";
//     ss << dataPackage.CurrentState << " ";

//     ss << "NextState: ";
//     ss << dataPackage.NextState << " ";

//     // ss << "IfWalkStart: ";
//     // ss << dataPackage.IfWalkStart << " ";

//     // ss << "FootNum: ";
//     // ss << dataPackage.FootNum << " ";

//     ss << "js_vx_desire: ";
//     ss << dataPackage.js_vx_desire << " ";

//     ss << "gait_vx_desire: ";
//     ss << dataPackage.gait_vx_desire << " ";

//     ss << "speed_forward_avg: ";
//     ss << dataPackage.speed_forward_avg << " ";

//     ss << "gait_OmegaZ_desire: ";
//     ss << dataPackage.gait_OmegaZ_desire << " ";

//     // ss << "isWalking: ";
//     // ss << dataPackage.isWalking << " ";

//     ss << "base_EulerZ_desire: ";
//     ss << dataPackage.base_EulerZ_desire << " ";

//     ss << "gait_vy_desire: ";
//     ss << dataPackage.gait_vy_desire << " ";

//     ss << std::endl;
//     ss << "************************************";
//     // 将数据写入 log.txt 文件
//     logFile << ss.str() << std::endl;

//     // 打印到控制台（可选）
//     std::cout << ss.str() << std::endl;

//     // 将数据写入 csv 文件
//     csvFile << t;  // 写入时间戳

//     // 打印 generalized_q_actual
//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_actual[i];
//     }

//     // 打印 generalized_q_desired
//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_desired[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_dot_actual[i];
//     }   

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.generalized_q_dot_desired[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_actual[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_desire[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_W_actual[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_W_desire[i];
//     }


//     csvFile << "," << dataPackage.contact_force[2];
//     csvFile << "," << dataPackage.contact_force[8];
//     csvFile << "," << dataPackage.T_START;


//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_LinVel_W_actual[i];
//     }


//     // for (int i = 0; i < 3; ++i) {
//     //     csvFile << "," << dataPackage.feet_r_LinVel_W_actual[i]+dataPackage.generalized_q_dot_actual[i];
//     // }

//     Eigen::VectorXd feet_r_LinVel_W_actual_jacobi = state_estimator.rightfoot_jacobi*dataPackage.generalized_q_dot_actual;

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << feet_r_LinVel_W_actual_jacobi[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.feet_r_LinVel_W_desire[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.base_pos[i];
//     }

//     for (int i = 0; i < 3; ++i) {
//         csvFile << "," << dataPackage.base_lin_vel[i];
//     }



//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_actual[i]-dataPackage.feet_r_Pos_W_actual[i];
//     }

//     csvFile << "," << dataPackage.FootNum;
//     csvFile << "," << dataPackage.js_vx_desire;
//     csvFile << "," << dataPackage.gait_vx_desire;
//     csvFile << "," << dataPackage.speed_forward_avg;

//     csvFile << "," << dataPackage.generalized_q_actual[3];
//     csvFile << "," << dataPackage.base_EulerZ_desire;

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.u_B[i];
//     }

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.u_W[i];
//     }

//     csvFile << "," << dataPackage.vx_Final;
//     csvFile << "," << dataPackage.vy_Final;
//     csvFile << "," << dataPackage.vy_ref;

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_PhaseStart_W[i];
//     }

//     for (int i = 0; i < 2; ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_PhaseStart_W[i];
//     }


//     for (int i = 0; i < dataPackage.torq_desire.size(); ++i) {
//         csvFile << "," << dataPackage.torq_desire[i];
//     }

//     for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
//         csvFile << "," << dataPackage.motor_pos[i];
//     }
//     for (int i = 0; i < dataPackage.motor_vel.size(); ++i) {
//         csvFile << "," << dataPackage.motor_vel[i];
//     }
//     for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//         csvFile << "," << dataPackage.generalized_q_desired.tail(dataPackage.actuatedDofNum)[i];
//     }
//     for (int i = 0; i < dataPackage.actuatedDofNum; ++i) {
//         csvFile << "," << dataPackage.generalized_q_dot_desired.tail(dataPackage.actuatedDofNum)[i];
//     }
//     csvFile << "," << dataPackage.feet_r_EulerZ_W_actual;
//     csvFile << "," << dataPackage.feet_r_EulerZ_W_desire;
//     csvFile << "," << dataPackage.t-dataPackage.T_START;

//     for (int i = 0; i < dataPackage.feet_l_Pos_W_IK.size(); ++i) {
//         csvFile << "," << dataPackage.feet_l_Pos_W_IK[i];
//     }
//     for (int i = 0; i < dataPackage.feet_r_Pos_W_IK.size(); ++i) {
//         csvFile << "," << dataPackage.feet_r_Pos_W_IK[i];
//     }
//     for (int i = 0; i < dataPackage.feet_l_EulerZYX_W_actual.size(); ++i) {
//         csvFile << "," << dataPackage.feet_l_EulerZYX_W_actual[i];
//     }
//     for (int i = 0; i < dataPackage.feet_r_EulerZYX_W_actual.size(); ++i) {
//         csvFile << "," << dataPackage.feet_r_EulerZYX_W_actual[i];
//     }

//     for (int i = 0; i < dataPackage.imu_zyx.size(); ++i) {
//         csvFile << "," << dataPackage.imu_zyx[i];
//     }

//     for (int i = 0; i < dataPackage.imu_angular_vel.size(); ++i) {
//         csvFile << "," << dataPackage.imu_angular_vel[i];
//     }

//     for (int i = 0; i < dataPackage.imu_lin_acc.size(); ++i) {
//         csvFile << "," << dataPackage.imu_lin_acc[i];
//     }
    
//     for (int i = 0; i < dataPackage.motor_Torque_desire.size(); ++i) {
//         csvFile << "," << dataPackage.motor_Torque_desire[i];
//     }

//     for (int i = 0; i < dataPackage.motor_torque.size(); ++i) {
//         csvFile << "," << dataPackage.motor_torque[i];
//     }
//     for (int i = 0; i < dataPackage.motor_Pos_desire.size(); ++i) {
//         csvFile << "," << dataPackage.motor_Pos_desire[i];
//     }
//     for (int i = 0; i < dataPackage.motor_Vel_desire.size(); ++i) {
//         csvFile << "," << dataPackage.motor_Vel_desire[i];
//     }
//     // 换行
//     csvFile << std::endl;

//     for (int i = 0; i < dataPackage.motor_pos.size(); ++i) {
//         MotorPoscsvFile << dataPackage.motor_pos[i];
//         if(i < dataPackage.motor_pos.size()-1){
//             MotorPoscsvFile << ",";
//         }
//     }
//     MotorPoscsvFile << std::endl;



// }

void DataLogger::SaveDataToFile(const DataPackage& dataPackage, double t, StateEstimator state_estimator) {
    // 检查文件是否打开
    if (!csvFile.is_open()) {
        std::cerr << "Error: Log file or CSV file is not open." << std::endl;
        return;
    }



        // 在第一次调用时写入表头
    if (first_log) {
        ss_csv.str("");
        ss_csv.clear();

        ss_csv << "time";
        ss_csv << "," << "generalized_q_actual_1" << "," << "generalized_q_actual_2" << "," << "generalized_q_actual_3";
        ss_csv << "," << "generalized_q_desired_1" << "," << "generalized_q_desired_2" << "," << "generalized_q_desired_3";
        ss_csv << "," << "generalized_q_dot_actual_1" << "," << "generalized_q_dot_actual_2" << "," << "generalized_q_dot_actual_3";
        ss_csv << "," << "generalized_q_dot_desired_1" << "," << "generalized_q_dot_desired_2" << "," << "generalized_q_dot_desired_3";
        ss_csv << "," << "feet_l_Pos_W_actual_1" << "," << "feet_l_Pos_W_actual_2" << "," << "feet_l_Pos_W_actual_3";
        ss_csv << "," << "feet_l_Pos_W_desire_1" << "," << "feet_l_Pos_W_desire_2" << "," << "feet_l_Pos_W_desire_3";
        ss_csv << "," << "feet_r_Pos_W_actual_1" << "," << "feet_r_Pos_W_actual_2" << "," << "feet_r_Pos_W_actual_3";
        ss_csv << "," << "feet_r_Pos_W_desire_1" << "," << "feet_r_Pos_W_desire_2" << "," << "feet_r_Pos_W_desire_3";
        ss_csv << "," << "left_contactforce" << "," << "right_contactforce";
        ss_csv << "," << "T_START";
        ss_csv << "," << "mj_data->xanchor[41]";
        ss_csv << "," << "mj_data->xanchor[39]";
        ss_csv << "," << "mj_data->xanchor[40]";
        ss_csv << "," << "feet_r_LinVel_W_actual_1" << "," << "feet_r_LinVel_W_actual_2" << "," << "feet_r_LinVel_W_actual_3";
        ss_csv << "," << "feet_r_LinVel_W_mj_data_1" << "," << "feet_r_LinVel_W_mj_data_2" << "," << "feet_r_LinVel_W_mj_data_3";
        // ss_csv << "," << "feet_r_LinVel_W_actual_1_jacobi" << "," << "feet_r_LinVel_W_actual_2_jacobi" << "," << "feet_r_LinVel_W_actual_3_jacobi";
        ss_csv << "," << "feet_r_LinVel_W_desire_1" << "," << "feet_r_LinVel_W_desire_2" << "," << "feet_r_LinVel_W_desire_3";
        ss_csv << "," << "generalized_q_mujoco_1" << "," << "generalized_q_mujoco_2" << "," << "generalized_q_mujoco_3";
        ss_csv << "," << "generalized_q_dot_mujoco_1" << "," << "generalized_q_dot_mujoco_2" << "," << "generalized_q_dot_mujoco_3";
        ss_csv << "," << "feet_l_Pos_W_mujoco_1" << "," << "feet_l_Pos_W_mujoco_2" << "," << "feet_l_Pos_W_mujoco_3";
        ss_csv << "," << "feet_distance_W_1" << "," << "feet_distance_W_2";
        ss_csv << "," << "FootNum";
        ss_csv << "," << "js_vx_desire" << "," << "gait_vx_desire" << "," << "speed_forward_avg";
        ss_csv << "," << "base_EulerZ_actual" << "," << "base_EulerZ_desire";
        ss_csv << "," << "u_B_1" << "," << "u_B_2";
        ss_csv << "," << "u_W_1" << "," << "u_W_2";
        ss_csv << "," << "vx_Final";
        ss_csv << "," << "vy_Final" << "," << "vy_ref";
        ss_csv << "," << "feet_l_Pos_PhaseStart_W_1" << "," << "feet_l_Pos_PhaseStart_W_2";
        ss_csv << "," << "feet_r_Pos_PhaseStart_W_1" << "," << "feet_r_Pos_PhaseStart_W_2";

        for (int i = 0; i < dataPackage.torq_desire.size(); ++i)
            ss_csv << "," << "Torque_Joint_" << i;

        for (int i = 0; i < dataPackage.motor_pos.size(); ++i)
            ss_csv << "," << "Pos_Joint_" << i;

        for (int i = 0; i < dataPackage.motor_vel.size(); ++i)
            ss_csv << "," << "Vel_Joint_" << i;

        for (int i = 0; i < dataPackage.actuatedDofNum; ++i)
            ss_csv << "," << "Pos_Joint_desire_" << i;

        for (int i = 0; i < dataPackage.actuatedDofNum; ++i)
            ss_csv << "," << "Vel_Joint_desire_" << i;

        ss_csv << "," << "feet_r_EulerZ_W_actual" << "," << "feet_r_EulerZ_W_desire";
        ss_csv << "," << "t_con";

        for (int i = 1; i <= dataPackage.feet_l_Pos_W_IK.size(); ++i)
            ss_csv << "," << "feet_l_Pos_W_IK_" << i;

        for (int i = 1; i <= dataPackage.feet_r_Pos_W_IK.size(); ++i)
            ss_csv << "," << "feet_r_Pos_W_IK_" << i;

        for (int i = 1; i <= dataPackage.feet_l_EulerZYX_W_actual.size(); ++i)
            ss_csv << "," << "feet_l_EulerZYX_W_actual_" << i;

        for (int i = 1; i <= dataPackage.feet_r_EulerZYX_W_actual.size(); ++i)
            ss_csv << "," << "feet_r_EulerZYX_W_actual_" << i;

        ss_csv << '\n';
        csvFile << ss_csv.str();
        first_log = false;
    }

    ss_log.str("");
    ss_log.clear();
    ss_csv.str("");
    ss_csv.clear();


    // ---------- 拼接 logFile 内容 ----------
    // ss_log << "simulate_time: " << t << " ";
    // ss_log << "contact_flag: ";
    // for (int i = 0; i < dataPackage.contact_flag.size(); ++i) {
    //     ss_log << dataPackage.contact_flag[i] << " ";
    // }
    // ss_log << "CurrentState: " << dataPackage.CurrentState << " ";
    // ss_log << "NextState: " << dataPackage.NextState << " ";
    // ss_log << "js_vx_desire: " << dataPackage.js_vx_desire << " ";
    // ss_log << "gait_vx_desire: " << dataPackage.gait_vx_desire << " ";
    // ss_log << "speed_forward_avg: " << dataPackage.speed_forward_avg << " ";
    // ss_log << "gait_OmegaZ_desire: " << dataPackage.gait_OmegaZ_desire << " ";
    // ss_log << "base_EulerZ_desire: " << dataPackage.base_EulerZ_desire << " ";
    // ss_log << "gait_vy_desire: " << dataPackage.gait_vy_desire << "\n";
    // ss_log << "************************************";

    // logFile << ss_log.str() << std::endl;
    // std::cout << ss_log.str() << std::endl;

    // ---------- 拼接 csvFile 内容 ----------
    ss_csv << t;

    writeVec(dataPackage.generalized_q_actual.head(3));
    writeVec(dataPackage.generalized_q_desired.head(3));
    writeVec(dataPackage.generalized_q_dot_actual.head(3));
    writeVec(dataPackage.generalized_q_dot_desired.head(3));
    writeVec(dataPackage.feet_l_Pos_W_actual);
    writeVec(dataPackage.feet_l_Pos_W_desire);
    writeVec(dataPackage.feet_r_Pos_W_actual);
    writeVec(dataPackage.feet_r_Pos_W_desire);

    ss_csv << "," << dataPackage.contact_force[2] << "," << dataPackage.contact_force[8] << "," << dataPackage.T_START;

    writeVec(dataPackage.feet_r_LinVel_W_actual);

    // Eigen::VectorXd feet_r_LinVel_W_actual_jacobi = state_estimator.rightfoot_jacobi * dataPackage.generalized_q_dot_actual;
    // for (int i = 0; i < 3; ++i) ss_csv << "," << feet_r_LinVel_W_actual_jacobi[i];
    writeVec(dataPackage.feet_r_LinVel_W_desire);
    writeVec(dataPackage.base_pos.head(3));
    writeVec(dataPackage.base_lin_vel.head(3));

    for (int i = 0; i < 2; ++i) {
        ss_csv << "," << (dataPackage.feet_l_Pos_W_actual[i] - dataPackage.feet_r_Pos_W_actual[i]);
    }

    ss_csv << "," << dataPackage.FootNum;
    ss_csv << "," << dataPackage.js_vx_desire << "," << dataPackage.gait_vx_desire << "," << dataPackage.speed_forward_avg;
    ss_csv << "," << dataPackage.generalized_q_actual[3] << "," << dataPackage.base_EulerZ_desire;
    writeVec(dataPackage.u_B.head(2));
    writeVec(dataPackage.u_W.head(2));
    ss_csv << "," << dataPackage.vx_Final << "," << dataPackage.vy_Final << "," << dataPackage.vy_ref;
    writeVec(dataPackage.feet_l_Pos_PhaseStart_W);
    writeVec(dataPackage.feet_r_Pos_PhaseStart_W);
    writeVec(dataPackage.torq_desire);
    writeVec(dataPackage.motor_pos);
    writeVec(dataPackage.motor_vel);
    writeVec(dataPackage.generalized_q_desired.tail(dataPackage.actuatedDofNum));
    writeVec(dataPackage.generalized_q_dot_desired.tail(dataPackage.actuatedDofNum));

    ss_csv << "," << dataPackage.feet_r_EulerZ_W_actual << "," << dataPackage.feet_r_EulerZ_W_desire;
    ss_csv << "," << dataPackage.t - dataPackage.T_START;

    writeVec(dataPackage.feet_l_Pos_W_IK);
    writeVec(dataPackage.feet_r_Pos_W_IK);
    writeVec(dataPackage.feet_l_EulerZYX_W_actual);
    writeVec(dataPackage.feet_r_EulerZYX_W_actual);
    writeVec(dataPackage.imu_zyx);
    writeVec(dataPackage.imu_angular_vel);
    writeVec(dataPackage.imu_lin_acc);
    writeVec(dataPackage.motor_Torque_desire);
    writeVec(dataPackage.motor_torque);
    writeVec(dataPackage.motor_Pos_desire);
    writeVec(dataPackage.motor_Vel_desire);

    ss_csv << '\n';
    csvFile << ss_csv.str();

}


