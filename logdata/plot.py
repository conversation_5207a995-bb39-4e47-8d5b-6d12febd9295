import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# data_log = pd.read_csv("/home/<USER>/Music/MotorList_lxl/data_0512.csv")
data_log = np.loadtxt("/home/<USER>/Documents/PhybotSofware/logdata/datacollection_0524.txt")

print(data_log.shape)  # 打印前5行
print(','.join(f'{x:.5f}' for x in data_log[7067, 1:7]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 7:13]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 13:19]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 19:25]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 25:49]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 49:73]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 97:121]))
print(','.join(f'{x:.5f}' for x in data_log[7067, 121:145]))

x = data_log[:,0]
y1 = data_log[:,26]
# y4 = data_log['Pos_Joint_3']
# y2 = data_log['motor_Torque_desire4'] 
# y3 = data_log['motor_torque4'] 
# col_index = data_log.columns.get_loc('Vel_Joint_3')
# print(col_index)
# i= 15
# 开始画图
# y1 = pos_act.iloc[:, i]
# y2 = pos_des.iloc[:, i]
# y3 = pos_act.iloc[:, i+2]


# z1 = vel_act.iloc[:, i]
# z2 = vel_des.iloc[:, i]

# 画图
# 创建第一个图
plt.figure()  # 新建一个图
plt.plot(x, y1, color='blue')
# plt.plot(x, y2, color='red')
# plt.plot(x, y3, color='green')
# plt.plot(x, y4, color='yellow')
# plt.plot(x, y2, color='red')
plt.xlabel('Time')
plt.ylabel('Value')
plt.title('Plot pos')


# # 创建第二个图
# plt.figure()  # 新建一个图
# plt.plot(x, z1, color='blue')
# plt.plot(x, z2, color='red')
# plt.xlabel('Time')
# plt.ylabel('Value')
# plt.title('Plot vel')



# plt.figure()  # 新建一个图
# plt.plot(x, y1, color='blue')
# plt.plot(x, y3, color='red')
# plt.xlabel('Time')
# plt.ylabel('Value')
# plt.title('Plot vel')


plt.grid(True)
plt.show()


