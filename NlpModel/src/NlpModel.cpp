#include "../include/NlpModel.h"
#include "StateEstimator/include/basicfunction.h"
#define TIMEMEASURE
#ifdef TIMEMEASURE
#include "ThirdParty/broccoli/core/Time.hpp"
using namespace broccoli::core;
#endif
NlpModel::NlpModel() {}

NlpModel::~NlpModel() {}

void NlpModel::init(std::string model_pb_path, std::string encoder_pb_path, int obs_num_, int encoder_num_, int action_num_, int latent_num_, int lstm_layers_, int lstm_hidden_size_) {
    // init para
    obs_num = obs_num_;
    encoder_num = encoder_num_;
    action_num = action_num_;
    latent_num = latent_num_;
    lstm_layers = lstm_layers_;
    lstm_hidden_size = lstm_hidden_size_;

    inputdata_nlp = Eigen::VectorXd::Zero(obs_num + 2 * lstm_layers * lstm_hidden_size);
    inputdata_encoder_nlp = Eigen::VectorXd::Zero(encoder_num + 2 * lstm_layers * lstm_hidden_size);
    outputdata_nlp = Eigen::VectorXd::Zero(action_num + 2 * lstm_layers * lstm_hidden_size);
    outputdata_encoder_nlp = Eigen::VectorXd::Zero(latent_num + 2 * lstm_layers * lstm_hidden_size);
    // outputdata_nlp = Eigen::VectorXd::Zero(action_num + 2 * lstm_layers * lstm_hidden_size);
    outputdata_nlp_filter = Eigen::VectorXd::Zero(action_num);
    compute_complete = true;

    nlpmodel_ = torch::jit::load(model_pb_path, torch::kCPU);
    encodermodel_ = torch::jit::load(encoder_pb_path, torch::kCPU);

    for (const auto& method : nlpmodel_.get_methods()) {
        std::cout << "- " << method.name() << std::endl;
    }
    std::cout <<"asdf" << std::endl;
    // nlpmodel_thread = std::thread(&NlpModel::act_interface, this);
}

Eigen::VectorXd NlpModel::act_interface_encoder(Eigen::VectorXd inputdata_encoder_nlp) {

    #ifdef TIMEMEASURE
    auto total_start = std::chrono::high_resolution_clock::now();
    #endif

    torch::Device device(torch::kCPU);

    std::vector<torch::jit::IValue> inputs_encoder;
    torch::Tensor inputdata_encoder = torch::zeros({encoder_num + 2 * lstm_layers * lstm_hidden_size}).toType(torch::kFloat);

    // Copy data directly to avoid memory issues
    auto accessor_encoder = inputdata_encoder.accessor<float, 1>();
    for (int i = 0; i < encoder_num + 2 * lstm_layers * lstm_hidden_size; i++) {
        accessor_encoder[i] = static_cast<float>(inputdata_encoder_nlp(i));
    }

    inputdata_encoder = inputdata_encoder.to(device);
    // Add batch dimension: (encoder_num + lstm_size) -> (1, encoder_num + lstm_size)
    inputdata_encoder = inputdata_encoder.unsqueeze(0);
    inputs_encoder.push_back(inputdata_encoder);

    #ifdef TIMEMEASURE
    auto forward_start = std::chrono::high_resolution_clock::now();
    #endif

    torch::Tensor outputdata_encoder = encodermodel_.forward(inputs_encoder).toTensor();

    #ifdef TIMEMEASURE
    auto forward_end = std::chrono::high_resolution_clock::now();
    auto forward_duration = std::chrono::duration_cast<std::chrono::microseconds>(forward_end - forward_start);
    // std::cout << "Forward pass took: " << forward_duration.count() << " μs" << std::endl;
    #endif

    std::vector<float> v_encoder(outputdata_encoder.data_ptr<float>(), outputdata_encoder.data_ptr<float>() + outputdata_encoder.numel());
    for (int i = 0; i < latent_num + 2 * lstm_layers * lstm_hidden_size; i++) {
        outputdata_encoder_nlp(i) = v_encoder[i];
        // outputdata_encoder_nlp(i) = std::max(-2.0, std::min(double(v_encoder[i]), 2.0));
    }
    #ifdef TIMEMEASURE
    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start);
    // std::cout << "Total act_interface execution time: " << total_duration.count() << " ms" << std::endl;
    #endif

    return outputdata_encoder_nlp;
}


void NlpModel::act_interface() {
    #ifdef TIMEMEASURE
    auto total_start = std::chrono::high_resolution_clock::now();
    #endif

    torch::Device device(torch::kCPU);
    std::vector<torch::jit::IValue> inputs;
    torch::Tensor inputdata = torch::zeros({obs_num + 2 * lstm_layers * lstm_hidden_size}).toType(torch::kFloat);

    // Copy data directly to avoid memory issues
    auto accessor = inputdata.accessor<float, 1>();
    for (int i = 0; i < obs_num + 2 * lstm_layers * lstm_hidden_size; i++) {
        accessor[i] = static_cast<float>(inputdata_nlp(i));
    }

    inputdata = inputdata.to(device);
    // Add batch dimension: (obs_num + lstm_size) -> (1, obs_num + lstm_size)
    inputdata = inputdata.unsqueeze(0);
    inputs.push_back(inputdata);

    #ifdef TIMEMEASURE
    auto forward_start = std::chrono::high_resolution_clock::now();
    #endif
    
    torch::Tensor outputdata = nlpmodel_.forward(inputs).toTensor();
    
    #ifdef TIMEMEASURE
    auto forward_end = std::chrono::high_resolution_clock::now();
    auto forward_duration = std::chrono::duration_cast<std::chrono::microseconds>(forward_end - forward_start);
    // std::cout << "Forward pass took: " << forward_duration.count() << " μs" << std::endl;
    #endif

    std::vector<float> v(outputdata.data_ptr<float>(), outputdata.data_ptr<float>() + outputdata.numel());
    for (int i = 0; i < action_num + 2 * lstm_layers * lstm_hidden_size; i++) {
        outputdata_nlp(i) = v[i];
    }

    outputdata_nlp_filter = outputdata_nlp;

    #ifdef TIMEMEASURE
    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start);
    // std::cout << "Total act_interface execution time: " << total_duration.count() << " ms" << std::endl;
    #endif
}


Eigen::VectorXd NlpModel::get_outputdata() {
    return outputdata_nlp_filter;
}

Eigen::VectorXd NlpModel::get_outputdata_encoder() {
    return outputdata_encoder_nlp;
}

void NlpModel::set_inputdata(Eigen::VectorXd inputdata_nlp_) {

    // set obs
    inputdata_nlp.segment(0, obs_num) = inputdata_nlp_;
    // set hidden states
    if (lstm_layers > 0) {
        inputdata_nlp.segment(obs_num, 2 * lstm_layers * lstm_hidden_size) = outputdata_nlp.segment(action_num, 2 * lstm_layers * lstm_hidden_size);
    }
    // last action for temp
    // inputdata_nlp.segment(52,12) = outputdata_nlp.segment(0,12);
    // set compute flag
}