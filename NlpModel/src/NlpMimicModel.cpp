#include "../include/NlpMimicModel.h"
#include "StateEstimator/include/basicfunction.h"
#define TIMEMEASURE
#ifdef TIMEMEASURE
#include "ThirdParty/broccoli/core/Time.hpp"
using namespace broccoli::core;
#endif

NlpMimicModel::NlpMimicModel() {}
NlpMimicModel::~NlpMimicModel() {}

void NlpMimicModel::init(std::string model_cpkt_path, int obs_num_, int action_num_) {
    // init para
    obs_num = obs_num_;
    action_num = action_num_;

    inputdata_nlp = Eigen::VectorXd::Zero(obs_num);
    outputdata_nlp = Eigen::VectorXd::Zero(action_num);
    outputdata_nlp_filter = Eigen::VectorXd::Zero(action_num);
    compute_complete = true;
    // load the pre-trained model
    nlp_mimic_model_ = torch::jit::load(model_cpkt_path, torch::kCPU);

    std::cout << "NlpMimicModel init complete" << std::endl;
}

void NlpMimicModel::set_inputdata(Eigen::VectorXd inputdata_nlp_) {
    inputdata_nlp.segment(0, obs_num) = inputdata_nlp_;
}

void NlpMimicModel::act_interface() {
    #ifdef TIMEMEASURE
    auto total_start = std::chrono::high_resolution_clock::now();
    #endif

    torch::Device device(torch::kCPU);
    std::vector<torch::jit::IValue> inputs;
    torch::Tensor inputdata = torch::zeros({obs_num}).toType(torch::kFloat);
    // copy inputdata_nlp to inputdata
    basicfunction::copytoinputs(inputdata, inputdata_nlp, obs_num);
    inputdata = inputdata.to(device);
    inputs.push_back(inputdata);

    #ifdef TIMEMEASURE
    auto forward_start = std::chrono::high_resolution_clock::now();
    #endif

    torch::Tensor outputdata = nlp_mimic_model_.forward(inputs).toTensor();

    // Remove batch dimension: (1, 21) -> (21)
    outputdata = outputdata.squeeze(0);

    #ifdef TIMEMEASURE
    auto forward_end = std::chrono::high_resolution_clock::now();
    auto forward_duration = std::chrono::duration_cast<std::chrono::microseconds>(forward_end - forward_start);
    #endif

    // transfer outputdata to vector
    std::vector<float> v(outputdata.data_ptr<float>(), outputdata.data_ptr<float>() + outputdata.numel());
    for (int i = 0; i < action_num; i++) {
        outputdata_nlp(i) = v[i];
    }

    outputdata_nlp_filter = outputdata_nlp;

    #ifdef TIMEMEASURE
    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(total_end - total_start);
    #endif
}

Eigen::VectorXd NlpMimicModel::get_outputdata() {
    return outputdata_nlp_filter;
}
