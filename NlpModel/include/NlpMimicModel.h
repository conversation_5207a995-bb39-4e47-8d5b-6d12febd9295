#ifndef NlpMimicModel_H_
#define NlpMimicModel_H_

#include <Eigen/Dense>
#include <thread>
#include <time.h>
#include <torch/script.h>

class NlpMimicModel {
    public:
        NlpMimicModel();
        ~NlpMimicModel();
        void init(std::string model_cpkt_path, int obs_num_, int action_num_);
        void act_interface();
        Eigen::VectorXd act_interface_encoder(Eigen::VectorXd inputdata_encoder_nlp);
        void set_inputdata(Eigen::VectorXd inputdata_nlp_);
        Eigen::VectorXd get_outputdata();
    
    private:
        int obs_num;
        int action_num;

        Eigen::VectorXd inputdata_nlp;
        Eigen::VectorXd outputdata_nlp;
        Eigen::VectorXd outputdata_nlp_filter;

        bool compute_complete = true;
        std::thread nlp_mimic_model_thread;

        torch::jit::script::Module nlp_mimic_model_;
};
#endif