#ifndef KEYBOARD_H
#define KEYBOARD_H

#endif // KEYBOARD_H
#include <termio.h>
#include <stdio.h>
#include<Eigen/Dense>

#include <iostream>

using namespace std;

void scanKeyboard(int & in)
{
    while(1)
    {
        struct termios new_settings;
        struct termios stored_settings;
        tcgetattr(0,&stored_settings);
        new_settings = stored_settings;
        new_settings.c_lflag &= (~ICANON);
        new_settings.c_cc[VTIME] = 0;
        tcgetattr(0,&stored_settings);
        new_settings.c_cc[VMIN] = 1;
        tcsetattr(0,TCSANOW,&new_settings);
        in = getchar();

        tcsetattr(0,TCSANOW,&stored_settings);
    }



}

double FSmSwitch(Eigen::VectorXd & command, int& key, double& joint3)
{

    command.setZero();
    int flag = 0;
    if(key == 'w')//z
    {

        command(2) = 1;
        flag = 1;
    }
    else if (key  == 's')//z
    {

        command(2) = -1;
        flag = 1;
    }
    else if (key  == 'a')//y
    {

        command(1) = 1;
        flag = 1;
    }
    else if (key  == 'd')//y
    {

        command(1)= -1;
        flag = 1;
    }
    else if (key  == 'q')//x
    {

        command(0) = 1;
        flag = 1;
    }
    else if (key  == 'e')//x
    {

        command(0) = -1;
        flag = 1;
    }

    if(key == 49)//x rot
    {

        command(3) = 1;
        flag = 1;
    }
    else if (key  == 50)//y rot
    {

        command(4) = 1;
        flag = 1;
    }
    else if (key  == 51)//z rot
    {

        command(5) = 1;
        flag = 1;
    }

    if(key == 52)//z rot
    {

        command(3) = -1;
        flag = 1;
    }
    else if (key  == 53)//z rot
    {

        command(4) = -1;
        flag = 1;
    }
    else if (key  == 54)//y
    {

        command(5) = -1;
        flag = 1;
    }

    else if (key  == 32)
    {
        command(0) = 0;
        command(1) = 0;
        command(2) = 0;
        command(3) = 0;
        command(4) = 0;
        command(5) = 0;
        joint3 = 0;
    }

    // else if (key  == 65)
    // {
    //     joint3 =  0.01;
    // }

    // else if (key  == 66)
    // {
    //     joint3 =  -0.01;
    // }


    return joint3;
}



