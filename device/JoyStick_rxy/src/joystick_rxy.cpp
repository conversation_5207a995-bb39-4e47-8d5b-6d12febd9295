#include"../include/joystick_rxy.h"
#include<iostream>
Joystick_rxy::Joystick_rxy(){}

Joystick_rxy::~Joystick_rxy(){}

int Joystick_rxy::xbox_open(const char *file_name)  
{  
    int xbox_fd;  

    xbox_fd = open(file_name, O_RDONLY);  
    if (xbox_fd < 0)  
    {  
        perror("open");  
        return -1;  
    }  

    return xbox_fd;  
} 

int Joystick_rxy::xbox_map_read(xbox_map_t *map)  
{  
    int len, type, number, value;  
    struct js_event js;  

    len = read(xbox_fd, &js, sizeof(struct js_event));  
    if (len < 0)  
    {  
        perror("read");  
        return -1;  
    }  

    type = js.type;  
    number = js.number;  
    value = js.value;  

    map->time = js.time;  

    if (type == JS_EVENT_BUTTON)  
    {  
        switch (number)  
        {  
            case XBOX_BUTTON_A:  
                map->a = value;  
                break;  

            case XBOX_BUTTON_B:  
                map->b = value;  
                break;  

            case XBOX_BUTTON_X:  
                map->x = value;  
                break;  

            case XBOX_BUTTON_Y:  
                map->y = value;  
                break;  

            case XBOX_BUTTON_LB:  
                map->lb = value;  
                break;  

            case XBOX_BUTTON_RB:  
                map->rb = value;  
                break;  

            case XBOX_BUTTON_START:  
                map->start = value;  
                break;  

            case XBOX_BUTTON_BACK:  
                map->back = value;  
                break;  

            case XBOX_BUTTON_HOME:  
                map->home = value;  
                break;  

            case XBOX_BUTTON_LO:  
                map->lo = value;  
                break;  

            case XBOX_BUTTON_RO:  
                map->ro = value;  
                break;  

            default:  
                break;  
        }  
    }  
    else if (type == JS_EVENT_AXIS)  
    {  
        switch(number)  
        {  
            case XBOX_AXIS_LX:  
                map->lx = value;  
                break;  

            case XBOX_AXIS_LY:  
                map->ly = value;  
                break;  

            case XBOX_AXIS_RX:  
                map->rx = value;  
                break;  

            case XBOX_AXIS_RY:  
                map->ry = value;  
                break;  

            case XBOX_AXIS_LT:  
                map->lt = value;  
                break;  

            case XBOX_AXIS_RT:  
                map->rt = value;  
                break;  

            case XBOX_AXIS_XX:  
                map->xx = value;  
                break;  

            case XBOX_AXIS_YY:  
                map->yy = value;  
                break;  

            default:  
                break;  
        }  
    }  
    else  
    {  
        /* Init do nothing */  
    }  

    return len;
}

void Joystick_rxy::xbox_close(void)  
{  
    close(xbox_fd);  
    return;  
}

int Joystick_rxy::xbox_init(void)
{
    int len, type;  
    int axis_value, button_value;  
    int number_of_axis, number_of_buttons ;  

    xbox_fd = xbox_open("/dev/input/js0");  
    if(xbox_fd < 0)  
    {  
        return -1;  
    }  

    return 0;
}

int Joystick_rxy::xbox_read(xbox_map_t *xbox_m)
{

    int len = xbox_map_read(xbox_m);  
    if (len < 0)  
    {  
        return -1;
    }  
    return 0;
}

void Joystick_rxy::init()
{
    int ret = -1;
    xbox_m.a = 0.0;
    xbox_m.b = 0.0;
    xbox_m.x = 0.0;
    xbox_m.y = 0.0;
    xbox_m.lx = 0.0;
    xbox_m.ly = 0.0;
    xbox_m.rx = 0.0;
    xbox_m.ry = 0.0;
    xbox_m.xx = 0.0;
    xbox_m.yy = 0.0;
    xbox_m.lt = -32767;
    current_fsmstate_command = "";
    // ret = pthread_create(&Joystick::xbox_thread, NULL, Joystick::xbox_run, NULL);
    xbox_thread = std::thread(&Joystick_rxy::xbox_run,this);
}

void Joystick_rxy::xbox_run()
{
    int len,ret;
    
    ret = xbox_init(); 
    if(ret < 0)
    {
        printf("xbox init fail\n");
    }

    while(ret == 0)
    {
        len = xbox_read(&xbox_m);
        if (len < 0)  
        {  
            // usleep(10*1000);  
            continue;  
        }  

        // printf("Time:%8d A:%d B:%d X:%d Y:%d LB:%  m.b, xbox_m.x, xbox_m.y, xbox_m.lb, xbox_m.rb, xbox_m.start, xbox_m.back, xbox_m.home, xbox_m.lo, xbox_m.ro,  
        //         xbox_m.xx, xbox_m.yy, xbox_m.lx, xbox_m.ly, xbox_m.rx, xbox_m.ry, xbox_m.lt, xbox_m.rt);  
        // fflush(stdout);  

        usleep(10*1000);  
    }       
}

std::string Joystick_rxy::get_state_change()
{
    if(xbox_m.a == 1.0){
        current_fsmstate_command = "gotoZero";
        return "gotoZero";
    }else if(xbox_m.b == 1.0){
        current_fsmstate_command = "GotoPowerOff";
        return "GotoPowerOff";
    }else if(xbox_m.x == 1.0){
        current_fsmstate_command = "GotoPowerOn";
        return "GotoPowerOn";
    }else if(xbox_m.y == 1.0){
        current_fsmstate_command = "gotoNLP";
        return "gotoNLP";
        return "";
    }else{
        return "";
    }

}

std::string Joystick_rxy::get_current_state_command()
{
    return current_fsmstate_command;
}

double Joystick_rxy::get_walk_x_direction_speed()
{
    int x_value = xbox_m.ly;
    if((abs(x_value) > deadarea)&&(abs(x_value)<=maxvalue)){
        if(x_value > 0){
            return ly_dir*maxspeed_x*((double)(abs(x_value)-deadarea)/(maxvalue-deadarea));
        }else{
            return ly_dir*minspeed_x*((double)(abs(x_value)-deadarea)/(maxvalue-deadarea));
        }
    }else{
        return 0.0;
    }
}

double Joystick_rxy::get_walk_y_direction_speed()
{
    int y_value = xbox_m.lx;
    if((abs(y_value) > deadarea)&&(abs(y_value)<=maxvalue)){
        if(y_value > 0){
            return lx_dir*maxspeed_y*((double)(abs(y_value)-deadarea)/(maxvalue-deadarea));
        }else{
            return lx_dir*minspeed_y*((double)(abs(y_value)-deadarea)/(maxvalue-deadarea));
        }
    }else{
        return 0.0;
    }
}

double Joystick_rxy::get_walk_yaw_direction_speed()
{
    int yaw_value = xbox_m.rx;
    if((abs(yaw_value) > deadarea)&&(abs(yaw_value)<=maxvalue)){
        if(yaw_value > 0){
            return rx_dir*maxspeed_yaw*((double)(abs(yaw_value)-deadarea)/(maxvalue-deadarea));
        }else{
            return rx_dir*minspeed_yaw*((double)(abs(yaw_value)-deadarea)/(maxvalue-deadarea));
        }
    }else{
        return 0.0;
    }
}