#ifndef JOYSTICK_H_
#define JOYSTICK_H_
#include <stdio.h>  
#include <unistd.h>  
#include <string>  
#include <sys/types.h>  
#include <sys/stat.h>  
#include <fcntl.h>  
#include <errno.h>  
#include <pthread.h>
#include <thread>
#include <stdio.h>  
#include <unistd.h>  
#include <string.h>  
#include <sys/types.h>  
#include <sys/stat.h>  
#include <fcntl.h>  
#include <errno.h>  

#include <linux/input.h>  
#include <linux/joystick.h>  
// #include "xbox.h"
#define XBOX_TYPE_BUTTON    0x01  
#define XBOX_TYPE_AXIS      0x02  

#define XBOX_BUTTON_A       0x00  
#define XBOX_BUTTON_B       0x01  
#define XBOX_BUTTON_X       0x02  
#define XBOX_BUTTON_Y       0x03  
#define XBOX_BUTTON_LB      0x04  
#define XBOX_BUTTON_RB      0x05  
#define XBOX_BUTTON_START   0x06  
#define XBOX_BUTTON_BACK    0x07  
#define XBOX_BUTTON_HOME    0x08  
#define XBOX_BUTTON_LO      0x09    /* 左摇杆按键 */  
#define XBOX_BUTTON_RO      0x0a    /* 右摇杆按键 */  

#define XBOX_BUTTON_ON      0x01  
#define XBOX_BUTTON_OFF     0x00  

#define XBOX_AXIS_LX        0x00    /* 左摇杆X轴 */  
#define XBOX_AXIS_LY        0x01    /* 左摇杆Y轴 */  
#define XBOX_AXIS_RX        0x03    /* 右摇杆X轴 */  
#define XBOX_AXIS_RY        0x04    /* 右摇杆Y轴 */  
#define XBOX_AXIS_LT        0x02  
#define XBOX_AXIS_RT        0x05  
#define XBOX_AXIS_XX        0x06    /* 方向键X轴 */  
#define XBOX_AXIS_YY        0x07    /* 方向键Y轴 */  

#define XBOX_AXIS_VAL_UP        -32767  
#define XBOX_AXIS_VAL_DOWN      32767  
#define XBOX_AXIS_VAL_LEFT      -32767  
#define XBOX_AXIS_VAL_RIGHT     32767  

#define XBOX_AXIS_VAL_MIN       -32767  
#define XBOX_AXIS_VAL_MAX       32767  
#define XBOX_AXIS_VAL_MID       0x00  
/**
 * @brief joystick:
 * author : rxy
 * email : <EMAIL>
 */
typedef struct xbox_map  
{  
    int     time;  
    int     a;  
    int     b;  
    int     x;  
    int     y;  
    int     lb;  
    int     rb;  
    int     start;  
    int     back;  
    int     home;  
    int     lo;  
    int     ro;  

    int     lx;  
    int     ly;  
    int     rx;  
    int     ry;  
    int     lt;  
    int     rt;  
    int     xx;  
    int     yy;  

}xbox_map_t;  

class Joystick_rxy{
    public:
    Joystick_rxy();
    ~Joystick_rxy();
    void init();
    void xbox_run();
    /**
     * @brief Get the state change object
     * A: goto SetZero
     * B: goto Power off
     * X: goto Power on
     * Y: 
     * @return std::string 
     */
    std::string get_state_change(); 
    std::string get_current_state_command();
    /**
     * @brief 
     * 
     */
    int deadarea = 5000;
    int maxvalue = 32767;
    double lx_dir = -1.0;
    double ly_dir = -1.0;

    double rx_dir = -1.0;
    double ry_dir = -1.0;

    double xx_dir = -1.0;
    double yy_dir = -1.0;
    // get command  velocity for walk
    double get_walk_x_direction_speed();
    double maxspeed_x = 0.5;// - x
    double minspeed_x = -0.5;// +x
    
    double get_walk_y_direction_speed();
    double maxspeed_y = 0.5;
    double minspeed_y = -0.5;

    double get_walk_yaw_direction_speed();
    double maxspeed_yaw = 0.5;
    double minspeed_yaw = -0.5;
    private:
    // fsmstate
    std::string current_fsmstate_command;
    // 
    xbox_map_t xbox_m;
    // pthread_t xbox_thread;
    std::thread xbox_thread;
    // xbox
    int xbox_fd;
    int xbox_open(const char *file_name);
    int xbox_map_read(xbox_map_t *map);
    void xbox_close(void);
    int xbox_init(void);
    int xbox_read(xbox_map_t *xbox_m);
};
#endif