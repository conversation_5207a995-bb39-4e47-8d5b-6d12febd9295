#include"../include/can_comm.hpp"
#include<iostream>
int main(){
    std::cout<<"test can function: "<<std::endl;
    can_comm can_func;
    std::cout<<"test Setmode: "<<std::endl;
    unsigned char* sendbuff = new unsigned char[8];
    can_func.CanComm_SetMotorMode(MotorMode::CMD_MOTOR_MODE,sendbuff,8);
    std::cout<<"CMD_MOTOR_MODE: ";
    for(int i = 0; i<8;i++)
    {
        std::cout<<std::hex<<(int)(sendbuff[i])<<std::dec<<" ";
    }
    std::cout<<std::endl;
    can_func.CanComm_SetMotorMode(MotorMode::CMD_RESET_MODE,sendbuff,8);
    std::cout<<"CMD_RESET_MODE: ";
    for(int i = 0; i<8;i++)
    {
        std::cout<<std::hex<<(int)(sendbuff[i])<<std::dec<<" ";
    }
    std::cout<<std::endl;
    can_func.CanComm_SetMotorMode(MotorMode::CMD_ZERO_POSITION,sendbuff,8);
    std::cout<<"CMD_ZERO_POSITION: ";
    for(int i = 0; i<8;i++)
    {
        std::cout<<std::hex<<(int)(sendbuff[i])<<std::dec<<" ";
    }
    std::cout<<std::endl;

    float f_p = 1.0;
    float f_v = 3.0;
    float f_kp = 2.0;
    float f_kd = 4.0;
    float f_t = 5.0;
    can_func.CanComm_SendControlPara(f_p, f_v, f_kp, f_kd, f_t, sendbuff, 8);
    std::cout<<"test send commd: ";
    for(int i = 0; i<8;i++)
    {
        std::cout<<std::hex<<(int)(sendbuff[i])<<std::dec<<" ";
    }
    std::cout<<std::endl;    

    int can_id = 0.0;
    float pos = 0.0;
    float vel = 0.0;
    float current = 0.0;
    unsigned char recvbuff[8] = {0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    can_func.CanComm_GetCurPVT(can_id,pos,vel,current,recvbuff,8);
    std::cout<<"recv: "<<std::endl
    <<"can_id: "<<can_id<<std::endl
    <<"pos: "<<pos<<std::endl
    <<"vel: "<<vel<<std::endl
    <<"current: "<<current<<std::endl;
}