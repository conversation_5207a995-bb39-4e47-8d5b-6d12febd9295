#ifndef __CAN_COMM_H
#define __CAN_COMM_H
#include"can_basicfunction.hpp"
#define P_MIN -95.5f    // Radians
#define P_MAX 95.5f        
#define V_MIN -45.0f    // Rad/s
#define V_MAX 45.0f
#define KP_MIN 0.0f     // N-m/rad
#define KP_MAX 500.0f
#define KD_MIN 0.0f     // N-m/rad/s
#define KD_MAX 5.0f
#define T_MIN -18.0f
#define T_MAX 18.0f

enum MotorMode {CMD_MOTOR_MODE=0,CMD_RESET_MODE,CMD_ZERO_POSITION};
class can_comm{
    public:
    can_comm();
    ~can_comm();
    // set mode
    // return 0 for error
    int CanComm_SetMotorMode(MotorMode cmd, unsigned char* send_buff, int buff_len);
    // send control para include p_c v_c kp kv tau_c
    // return 0 for error
    int CanComm_SendControlPara(float f_p, float f_v, float f_kp, float f_kd, float f_t, unsigned char* send_buff, int buff_len);
    // get pos_a vel_a and current_a
    int CanComm_GetCurPVT(int &can_id, float &pos, float &vel, float &current, 
                            unsigned char* recv_buff, int buff_len);
};

#endif
