#include "../include/can_comm.hpp"
// #include "can.h"
#include<iostream>


#define LIMIT_MIN_MAX(x,min,max) (x) = (((x)<=(min))?(min):(((x)>=(max))?(max):(x)))

can_comm::can_comm(){}

can_comm::~can_comm(){}

int can_comm::CanComm_SetMotorMode(MotorMode cmd, unsigned char* send_buff, int buff_len)
{
    if(buff_len != 8){
        std::cout<< "send buff length error!"<<std::endl;
        return 0;
    }else{
        // send_buff = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00};
        for(int i = 0; i < buff_len; i++)
        {
            send_buff[i] = 0xFF;
        }
        send_buff[7] = 0x00;
        switch(cmd)
        {
            case CMD_MOTOR_MODE:
            {
                send_buff[7] = 0xFC;
                break;
            }            
            case CMD_RESET_MODE:
            {
                send_buff[7] = 0xFD;
                break;
            }           
            case CMD_ZERO_POSITION:
            {
                send_buff[7] = 0xFE;
                break;
            }            
            default:
            {
                std::cout<<" no such Motor Mode!"<<std::endl;
                return 0; /* ֱ���˳����� */
            }
        } 
        return 1;  
    }
 
}

int can_comm::CanComm_SendControlPara(float f_p, float f_v, float f_kp, float f_kd, float f_t, unsigned char* send_buff, int buff_len)
{
    uint16_t p, v, kp, kd, t;
    // uint8_t buf[8] = ;
    
    /* ��������Ĳ����ڶ���ķ�Χ�� */
    LIMIT_MIN_MAX(f_p,  P_MIN,  P_MAX);
    LIMIT_MIN_MAX(f_v,  V_MIN,  V_MAX);
    LIMIT_MIN_MAX(f_kp, KP_MIN, KP_MAX);
    LIMIT_MIN_MAX(f_kd, KD_MIN, KD_MAX);
    LIMIT_MIN_MAX(f_t,  T_MIN,  T_MAX);
    
    /* ����Э�飬��float��������ת�� */
    p = can_basicfunc::float_to_uint(f_p,      P_MIN,  P_MAX,  16);            
    v = can_basicfunc::float_to_uint(f_v,      V_MIN,  V_MAX,  12);
    kp = can_basicfunc::float_to_uint(f_kp,    KP_MIN, KP_MAX, 12);
    kd = can_basicfunc::float_to_uint(f_kd,    KD_MIN, KD_MAX, 12);
    t = can_basicfunc::float_to_uint(f_t,      T_MIN,  T_MAX,  12);
    if(buff_len == 8){
        send_buff[0] = p>>8;
        send_buff[1] = p&0xFF;
        send_buff[2] = v>>4;
        send_buff[3] = ((v&0xF)<<4)|(kp>>8);
        send_buff[4] = kp&0xFF;
        send_buff[5] = kd>>4;
        send_buff[6] = ((kd&0xF)<<4)|(t>>8);
        send_buff[7] = t&0xff;
        return 1;
    }else{
        std::cout<<"send buff length is error"<<std::endl;
        return 0;
    }
}
int can_comm::CanComm_GetCurPVT(int &can_id, float &pos, float &vel, float &current, 
                            unsigned char* recv_buff, int buff_len)
{
    uint16_t tmp_value;
    if(buff_len == 8){
        // can id: byte[0]
        can_id= recv_buff[0];
        // pos: byte[1] 15-8
        // pos: byte[2] 7-0
        tmp_value = (recv_buff[1]<<8)|(recv_buff[2]);
        pos = can_basicfunc::uint_to_float(tmp_value, P_MIN,  P_MAX,  16);
        // vel: byte[3] 11-4
        // vel: byte[4] 3-0
        tmp_value = (recv_buff[3]<<4)|(recv_buff[4]>>4);
        vel = can_basicfunc::uint_to_float(tmp_value, V_MIN,  V_MAX,  12);
        // cur: byte[4] 11-8
        // cur: byte[5] 7-0
        tmp_value = ((recv_buff[4]&0x0f)<<8)|(recv_buff[5]);
        current = can_basicfunc::uint_to_float(tmp_value,  T_MIN,  T_MAX,  12);
        return 1;
    }else{
        std::cout<<"recv buff length is error!"<<std::endl;
        return 0;
    }
}





