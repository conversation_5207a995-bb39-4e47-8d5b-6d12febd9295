###############################################################################
#                    HiPNUC IMU Configuration File
#
# This configuration file is used to set up HiPNUC IMU devices.
# For more detailed information, please refer to the Programming and User Manual
# available at www.hipnuc.com
###############################################################################

# 1. Device Information Output
# This command displays the firmware version and other device details
LOG VERSION

###############################################################################
# 2. Data Output Configuration
###############################################################################

# Configure HI91 message output at 50Hz (0.02s period)
LOG HI91 ONTIME 0.02

###############################################################################
# 3. Serial Configuration
# WARNING: Changing baud rate incorrectly may lead to communication loss.
# Only uncomment and modify these lines if you're sure about the changes.
###############################################################################
# To change the baud rate:
# 1. Uncomment the following three lines
# 2. Replace 115200 with your desired baud rate if different
# 3. Save this file and run the 'write' command with this config file
#
# SERIALCONFIG 115200
# SAVECONFIG
# REBOOT
#
# IMPORTANT: After changing the baud rate
# 1. You MUST disconnect your current connection
# 2. Update your terminal/software serial port settings to match the new baud rate
# 3. Reconnect to the device using the new baud rate, or call probe command again

###############################################################################
# 4. Additional Configuration Options
#
# For more advanced configuration options, please refer to the
# Programming and User Manual available at www.hipnuc.com
###############################################################################