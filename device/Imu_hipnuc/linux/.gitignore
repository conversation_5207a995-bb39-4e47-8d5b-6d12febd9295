# Build directories
build/
cmake-build-*/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile

# Compiled object files
*.o
*.obj

# Compiled dynamic libraries
*.so
*.dylib
*.dll

# Compiled static libraries
*.a
*.lib

# Executables
*.exe
*.out
imu_reader

# IDE specific files (for example, if you're using Visual Studio Code)
.vscode/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*~
*.bak

# Log files
*.log

# Core dumps
core

build
