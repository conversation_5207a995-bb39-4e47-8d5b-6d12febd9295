#include"../include/udp_group.hpp"
// #define UDP_GROUP_TEST
udpGroup::udpGroup(std::string _server_ip="************",int _server_port = 8001,
            std::string _end_ip="************",int _end_port = 4001,
            int us_timeout = 20000)
            {
                sock = socket(AF_INET,SOCK_DGRAM,0);
                std::cout<<"sock:"<<sock<<std::endl;          
                server_ip = _server_ip;
                server_port = _server_port;
                end_ip = _end_ip;
                end_port = _end_port;
                // local_point = new struct sockaddr_in();
                local_point.sin_family = AF_INET;
                local_point.sin_port = htons(server_port);
                local_point.sin_addr.s_addr = inet_addr(server_ip.c_str());
                if(bind(sock,(struct sockaddr*)&local_point,sizeof(local_point))<0)
                {
                    std::cerr<<"bind error"<<std::endl;   
                    exit(1);        
                }
                // recv timeout
                timeout.tv_sec = (int)(us_timeout/1000000);//秒
                timeout.tv_usec = us_timeout - (int)(us_timeout/1000000)*1000000;//微秒
                if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) == -1) {
                    std::cout<<"setsockopt failed:"<<std::endl;
                }
                std::cout<<"time out: "<< timeout.tv_sec<<" s, "<<timeout.tv_usec<<" us"<<std::endl;
                // end_point = new struct sockaddr_in();
                end_point.sin_family = AF_INET;
                end_point.sin_port = htons(end_port);
                end_point.sin_addr.s_addr = inet_addr(end_ip.c_str());
                // recv_buff_vector_data[0] = 0x28;
                // send_buff_vector_data[0] = 0x28;

                // recv_buff_vector_data[1] = 0x00;
                // recv_buff_vector_data[2] = 0x00;
                // recv_buff_vector_data[3] = 0x00;
                // recv_buff_vector_data[4] = 0x01;
                // send_buff_vector_data[1] = 0x00;
                // send_buff_vector_data[2] = 0x00;    
                // send_buff_vector_data[3] = 0x00;
                // send_buff_vector_data[4] = 0x01;             
                // for(int i = 5; i<13; i++){
                //     recv_buff_vector_data[i] = 0x00;
                //     send_buff_vector_data[i] = 0x00;
                // }
            }
udpGroup::~udpGroup()
{
    close(sock);
    // delete local_point;
    // delete end_point;
}

void udpGroup::init(std::vector<int> ID_list_)
{
    std::cout<<"udpGroup start init"<<std::endl;
    motornum = ID_list_.size();
    std::cout<<"motornum: "<<motornum<<std::endl;
    if(motornum <= 0)
    {
        std::cout<<"no motor!"<<std::endl;
        exit(1);
    }
    ID_list = ID_list_;
    std::cout<<"ID list: "<<std::endl;
    for(int i = 0; i < motornum;i++)
    {
        std::cout<<ID_list[i]<<" ";
    }
    std::cout<<std::endl;
    // allocated buffer
    unsigned char * recv_buff;
    unsigned char * send_buff;
    for(int i = 0; i < motornum; i++)
    {
        recv_buff = new unsigned char[13]();
        send_buff = new unsigned char[13]();
        // init data
        recv_buff[0] = 0x08;

        // recv_buff[1] = 0x00;
        // recv_buff[2] = 0x00;
        // recv_buff[3] = 0x00;
        recv_buff[4] = ID_list[i];

        send_buff[0] = 0x08;

        // send_buff[1] = 0x00;
        // send_buff[2] = 0x00;    
        // send_buff[3] = 0x00;
        send_buff[4] = ID_list[i];

        // for(int j = 5; j<13; j++){
        //     recv_buff[j] = 0x00;
        //     send_buff[j] = 0x00;
        // }
        recv_buff_vector.push_back(recv_buff);
        send_buff_vector.push_back(send_buff);
    }
    std::cout<<"buffer init complete: "<<recv_buff_vector.size()<<" "<<send_buff_vector.size()<<std::endl;
    // for(int i = 0;i<motornum;i++)
    // {
    //     std::cout<<"motor "<< i<<" buffer: ";
    //     for(int j = 0;j<13;j++)
    //     {
    //         std::cout<<std::hex<<(int)recv_buff_vector[i][j]<<std::dec<<" ";
    //     }
    //     std::cout<<std::endl;
    // }
    // command and states
    float* pvcpd;
    float* pvc;
    for(int i = 0; i < motornum;i++)
    {
        pvcpd = new float[5]();
        pvc = new float[3]();

        command.push_back(pvcpd);
        command[i][0] = 0.0;
        command[i][1] = 0.0;
        command[i][2] = 0.0;
        command[i][3] = 0.0;
        command[i][4] = 0.0;
        states.push_back(pvc);
    }
    std::cout<<"command and states init complete: "<<command.size()<<" "<<states.size()<<std::endl;

}

void udpGroup::start()
{
    sendrecv_thread = std::thread(&udpGroup::run,this);
}

void udpGroup::run()
{
    // send recv all once in one while 
    timeval start;
    timeval end;
    while(true){
        gettimeofday(&start,NULL);
        for(int i = 0; i<motornum;i++){
            // send command
            unsigned char send_buff_data[8];
            int error = can_msg.CanComm_SendControlPara(command[i][0],command[i][1],command[i][2],command[i][3],command[i][4],send_buff_data,8);
            for(int j = 0; j<8; j++)
            {
                send_buff_vector[i][j+5] = send_buff_data[j];
            }
            socklen_t len = sizeof(end_point);
            ssize_t s = sendto(sock,send_buff_vector[i],13,0,(struct sockaddr*)&end_point, len);
            // recv states
            usleep(1000);
            struct sockaddr_in end_point_recv;
            len = sizeof(end_point_recv);
            s = recvfrom(sock,recv_buff_vector[i],13,0,(struct sockaddr*)&end_point_recv,&len);
            if(s == -1){
                std::cout<<"motor "<< ID_list[i] << "close to time out!"<<std::endl;
                s = sendto(sock,send_buff_vector[i],13,0,(struct sockaddr*)&end_point, len);
                // recv states
                usleep(1000);
                s = recvfrom(sock,recv_buff_vector[i],13,0,(struct sockaddr*)&end_point_recv,&len);
                if(s == -1){
                    std::cout<<"motor "<< ID_list[i] << " time out!"<<std::endl;
                }
            }else{
                unsigned char recv_buff_data[8];
                int can_id;
                for(int j=0;j<8;j++){
                    recv_buff_data[j] = recv_buff_vector[i][j+5];
                }
                int error = can_msg.CanComm_GetCurPVT(can_id,states[i][0],states[i][1],states[i][2],recv_buff_data,8);   
                if(!error){
                    std::cout<<"CanComm_GetCurPVT error!"<<std::endl;
                } 
                // std::cout<<"can ID: "<< can_id<<std::endl;
                // if(can_id != ID_list[i])
                // {
                //     std::cout<<"send and recv canID not match!"<<std::endl;
                // }               
            }          
        }
        gettimeofday(&end,NULL);
        std::cout<<"sendrecv intervals(us): "<<(end.tv_sec-start.tv_sec)*1000000 + end.tv_usec - start.tv_usec<<std::endl; 
    }
}

void udpGroup::PowerOn()
{
    std::cout<<"Power on start"<<std::endl;
    unsigned char send_buff_data[8];
    int error = can_msg.CanComm_SetMotorMode(MotorMode::CMD_MOTOR_MODE,send_buff_data,8);
    for(int i = 0; i < motornum; i++)
    {

        // std::cout<<"send buff data: ";
        // for(int j = 0; j<8;j++)
        // {
        //     std::cout<<std::hex<<(int)send_buff_data[j]<<std::dec<<" ";
        // }
        // std::cout<<std::endl;

        for(int j = 0; j<8; j++)
        {
            send_buff_vector[i][j+5] = send_buff_data[j];
        }
        // std::cout<<"send_buff_vector "<<i<<" :";
        // for(int j = 0; j<13;j++)
        // {
        //     std::cout<<std::hex<<(int)send_buff_vector[i][j]<<std::dec<<" ";
        // }
        // std::cout<<std::endl;

        
        // for(int j = 0; j<13;j++)
        // {
        //     send_buff_vector_data[j] = send_buff_vector[i][j];
        // }   
        // std::cout<<"sizeof send buff: "<<sizeof(send_buff_vector_data)<<std::endl;
        socklen_t len = sizeof(end_point);
        ssize_t s = sendto(sock,send_buff_vector[i],13,0,(struct sockaddr*)&end_point, len);  
        
        struct sockaddr_in end_point_recv;
        len = sizeof(end_point_recv);
        s = recvfrom(sock,recv_buff_vector[i],13,0,(struct sockaddr*)&end_point_recv,&len);
        if(s == -1){
            std::cout<<"motor "<< ID_list[i] << " time out!"<<std::endl;
        }else{
            // for(int j = 0; j<13;j++)
            // {
            //     recv_buff_vector[i][j] = recv_buff_vector_data[j];
            // } 
            unsigned char recv_buff_data[8];
            int can_id;
            for(int j=0;j<8;j++){
                recv_buff_data[j] = recv_buff_vector[i][j+5];
            }
            int error = can_msg.CanComm_GetCurPVT(can_id,states[i][0],states[i][1],states[i][2],recv_buff_data,8);   
            if(!error){
                std::cout<<"CanComm_GetCurPVT error!"<<std::endl;
            } 
            if(can_id != ID_list[i])
            {
                std::cout<<"send and recv canID not match!"<<std::endl;
            }   
            // set target pos to cur pos
            command[i][0] = states[i][0];            
        }      
    } 
    std::cout<<"Power on done"<<std::endl;  
}

void udpGroup::PowerOff()
{
    std::cout<<" Power off start"<<std::endl;
    unsigned char send_buff_data[8];
    int error = can_msg.CanComm_SetMotorMode(MotorMode::CMD_RESET_MODE,send_buff_data,8);
    for(int i = 0; i < motornum; i++)
    {
        for(int j = 0; j<8; j++)
        {
            send_buff_vector[i][j+5] = send_buff_data[j];
        }   
        socklen_t len = sizeof(end_point);
        ssize_t s = sendto(sock,send_buff_vector[i],13,0,(struct sockaddr*)&end_point, len);  
        usleep(10000);
    } 
    std::cout<<"Power off done"<<std::endl;
}

void udpGroup::SetZero()
{
    unsigned char send_buff_data[8];
    int error = can_msg.CanComm_SetMotorMode(MotorMode::CMD_ZERO_POSITION,send_buff_data,8);
    for(int i = 0; i < motornum; i++)
    {
        for(int j = 0; j<8; j++)
        {
            send_buff_vector[i][j+5] = send_buff_data[j];
        }   
        socklen_t len = sizeof(end_point);
        ssize_t s = sendto(sock,send_buff_vector[i],13,0,(struct sockaddr*)&end_point, len);  
        command[i][0] = 0.0;
        command[i][1] = 0.0;
        command[i][4] = 0.0;
    }    
}

void udpGroup::GetPVT(std::vector<float*> &states_)
{
    if(states.size() == motornum){
        for(int i = 0; i< motornum; i++){
            for(int j = 0; j < 3; j++){
                states_[i][j] = states[i][j];
            }
        }
    }else{
        std::cout<<" GetPVT motornum not match"<<std::endl;
    }
}

void udpGroup::SetPVTPD(std::vector<float*> command_)
{
    if(command_.size() == motornum)
    {
        for(int i = 0; i< motornum; i++){
            for(int j = 0; j < 5; j++){
                command[i][j] = command_[i][j];
            }
        }
    }else{
        std::cout<<" SetPVTPD motornum not match"<<std::endl;
    }
}