#include"../include/udp_group.hpp"
#include<vector>
#include<math.h>
int main(){
    // cycle time
    int time_count = 10;//ms
    int time_count_total = 10000/time_count;
    float sim_time = 0;
    // can id
    int motornum = 8;
    std::vector<int> CanIDList(motornum);
    CanIDList[0] = 1;
    CanIDList[1] = 2;
    CanIDList[2] = 3;
    CanIDList[3] = 4;
    CanIDList[4] = 5;
    CanIDList[5] = 6;
    CanIDList[6] = 7;
    CanIDList[7] = 8;
    // states and command
    std::vector<float*> states;
    std::vector<float*> command;
    float* states_;
    float* command_;

    float pos_cmd = 0.0;
    float vel_cmd = 0.0;
    float kp_cmd = 2.0;
    float kd_cmd = 0.2;
    float current_cmd = 0.0;
    for(int i = 0;i<motornum;i++)
    {
        states_ = new float[3]();
        command_ = new float[5]();
        command_[2] = kp_cmd;
        command_[3] = kd_cmd;
        states.push_back(states_);
        command.push_back(command_);
    }
    // UDP
    std::string server_ip = "************";
    int server_port = 8001;
    std::string client_ip = "************";
    int client_port = 4001;
    udpGroup Group(server_ip,server_port,client_ip,client_port,20000);
    Group.init(CanIDList);
    Group.SetZero();
    sleep(1);
    Group.PowerOn();
    sleep(1);
    Group.SetZero();
    sleep(1);
    Group.start();
    //
    float A = 0.0;//M_PI/10.0;
    float omega = 2.0*M_PI/10.0;
    while(time_count_total){
        Group.GetPVT(states);
        std::cout<<"motor ID, pos, vel, current, sim_time: "<<std::endl;
        for(int i = 0;i<motornum;i++)
        {
            std::cout<<CanIDList[i]<<" "<<states[i][0]<<" "<<states[i][1]<<" "<<states[i][2]<<" "<<sim_time<<std::endl;
        }
        // 0 pi sin
        pos_cmd = A*sin(omega*sim_time);
        // pos_cmd = 1.57;
        vel_cmd = A*omega*cos(omega*sim_time);
        for(int i = 0;i<motornum;i++)
        {
            command[i][0] = pos_cmd;
            command[i][1] = vel_cmd;
        }
        Group.SetPVTPD(command);

        usleep(time_count*1000);
        sim_time = sim_time + time_count*0.001;
        time_count_total--;
    }
    sleep(1);
    // float pos_cmd = 1.57;
    // float vel_cmd = 0.0;
    // float current_cmd = 0.0;
    // float kp_cmd = 2.0;
    // float kd_cmd = 0.2;

    // float pos_a = 0.0;
    // float vel_a = 0.0;
    // float current_a = 0.0;
    // Group.SetPD(kp_cmd,kd_cmd);
    // Group.SetPVT(pos_cmd,vel_cmd,current_cmd);
    // Group.GetPVT(pos_a,vel_a,current_a);
    // std::cout<<"pos: "<<pos_a << "vel: "<<vel_a<<"current: "<<current_a<<std::endl;
    Group.PowerOff();
    // Group.start();
    return 0;
}