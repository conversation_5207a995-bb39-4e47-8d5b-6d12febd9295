#ifndef UDP_GROUP_H
#define UDP_GROUP_H                                                                                                                    
#include<iostream>     
#include<cstdio>       
#include<string>  
#include<unistd.h>
#include<sys/socket.h>
#include<stdlib.h>    
#include<sys/types.h> 
#include<netinet/in.h>
#include<arpa/inet.h> 
#include<thread>
#include<vector>
#include<sys/time.h>
#include<stdio.h>
#include<chrono>
#include"../../can_interface_rxy/include/can_comm.hpp"                    
class udpGroup       
{                    
   private:     
     std::string server_ip;
     int server_port;      
     int sock;
     std::string end_ip;
     int end_port;
     struct sockaddr_in end_point; 
     struct sockaddr_in local_point; 
     // time out: us
     timeval timeout;
     // wait time
     int us_wait_time = 4000;
     // can id list
     std::vector<int> ID_list;
     // true: has been set, false: has not been set
     std::vector<bool> SetValue;
     int motornum;
     // data buffer
     //  unsigned char recv_buff[13];
     //  unsigned char send_buff[13]; 
     std::vector<unsigned char*> recv_buff_vector;
     std::vector<unsigned char*> send_buff_vector;

     // used for broadcast
     unsigned char* send_buffer_pakage;
     unsigned char* recv_buffer_pakage;
     // return total len of the send pakage
     int CopyFromSendVectorToSendPakage();
     //init recv_buffer_pakage to 0xff, set SetValue to false;
     void clear_recv_buffer_pakage();
     // return the number of the can frames
     int CopyFromRecvPakageToRecvVector();
     //for test
    //  unsigned char send_buff_vector_data[13];
    //  unsigned char recv_buff_vector_data[13];
     // send command
    //  float f_p = 0.0;
    //  float f_v = 0.0;
    //  float f_kp = 0.0;
    //  float f_kd = 0.0;
    //  float f_t = 0.0;
     std::vector<float*> command;
     // get cvp
    //  int can_id = 1;
    //  float pos = 0.0; 
    //  float vel = 0.0;
    //  float current = 0.0;
    std::vector<float*> states;
     // recv and send thread
    //  std::thread recv_thread;
    //  std::thread send_thread; 
    std::thread sendrecv_thread;
     // can msg transf
     can_comm can_msg; 
   public:          
        udpGroup(std::string _server_ip,int _server_port,
                 std::string _end_ip,int _end_port,
                 int us_timeout);
        ~udpGroup();
        void init(std::vector<int> ID_list_);
        // void recv_data();
        // void send_data();
        void start();
        void run();
        void PowerOn();
        void PowerOff();
        void SetZero();
        void GetPVT(std::vector<float*> &states_);
        void SetPVTPD(std::vector<float*> command_);
};
#endif
   
