
#include "mujoco_interface.h"

MujocoInterface::MujocoInterface(DataPackage &data) {
    
    std::cout << "MujocoInterface config reading ..." << std::endl;
    std::string file_path = "../MujocoInterface/config/mini_phybot_mimic.yaml";
    YAML::Node config = YAML::LoadFile(file_path);
    
    char error[1000] = "Could not load binary model";
    std::string env_path = config["env_path"].as<std::string>();
    mjModel*mj_model = mj_loadXML(env_path.c_str(), 0, error, 10000);

    std::cout << "########xxxxxxxxxxxxxxxxxxxxxxxxx###########" << std::endl;

    // // mj_data = mj_makeData(mj_model);

    jointNum = data.actuatedDofNum;
    motor_pos.setZero(jointNum);
    motor_pos_old.setZero(jointNum);
    motor_vel.setZero(jointNum);
    motor_torque.setZero(jointNum);



    std::vector<std::string> JointName = config["joint_names"].as<std::vector<std::string>>();

    baseName= config["baseName"].as<std::string>();
    orientationSensorName=config["orientationSensorName"].as<std::string>();
    velSensorName=config["velSensorName"].as<std::string>();
    gyroSensorName=config["gyroSensorName"].as<std::string>();
    accSensorName=config["accSensorName"].as<std::string>();

    metorvel_noise_std=config["metorvel_noise_std"].as<double>();



    // timeStep=mj_model->opt.timestep;
    // jointNum=JointName.size();
    jntId_qpos.assign(jointNum, 0); 
    jntId_qvel.assign(jointNum, 0);
    jntId_dctl.assign(jointNum, 0);
    // motor_pos.assign(jointNum,0);
    // motor_vel.assign(jointNum,0);
    // motor_pos_Old.assign(jointNum,0);


    // for (int i=0;i<jointNum;i++)
    // {
    //     int tmpId= mj_name2id(mj_model,mjOBJ_JOINT,JointName[i].c_str());
    //     if (tmpId==-1)
    //     {
    //         std::cerr <<JointName[i]<< " not found in the XML file!" << std::endl;
    //         std::terminate();
    //     }
    //     jntId_qpos[i]=mj_model->jnt_qposadr[tmpId];
    //     jntId_qvel[i]=mj_model->jnt_dofadr[tmpId];

    //     // motorName="M"+motorName.substr(1);
    //     std::cout<<JointName[i]<<std::endl;
    //     tmpId= mj_name2id(mj_model,mjOBJ_ACTUATOR,JointName[i].c_str());
    //     if (tmpId==-1)
    //     {
    //         std::cerr <<JointName[i]<< " not found in the XML file!" << std::endl;
    //         std::terminate();
    //     }
    //     jntId_dctl[i]=tmpId;


    // }


    for (int i=0;i<jointNum;i++)
    {
        int tmpId= mj_name2id(mj_model,mjOBJ_JOINT,JointName[i].c_str());
        if (tmpId==-1)
        {
            std::cerr <<JointName[i]<< " not found in the XML file!" << std::endl;
            std::terminate();
        }
        jntId_qpos[i]=mj_model->jnt_qposadr[tmpId];
        jntId_qvel[i]=mj_model->jnt_dofadr[tmpId];

        // motorName="M"+motorName.substr(1);
        tmpId= mj_name2id(mj_model,mjOBJ_ACTUATOR,JointName[i].c_str());
        if (tmpId==-1)
        {
            std::cerr <<JointName[i]<< " not found in the XML file!" << std::endl;
            std::terminate();
        }
        jntId_dctl[i]=tmpId;


    }

    baseBodyId= mj_name2id(mj_model,mjOBJ_BODY, baseName.c_str());
    orientataionSensorId= mj_name2id(mj_model, mjOBJ_SENSOR, orientationSensorName.c_str());
    velSensorId= mj_name2id(mj_model,mjOBJ_SENSOR,velSensorName.c_str());
    gyroSensorId= mj_name2id(mj_model,mjOBJ_SENSOR,gyroSensorName.c_str());
    accSensorId= mj_name2id(mj_model,mjOBJ_SENSOR,accSensorName.c_str());

    jointP = Eigen::VectorXd::Zero(jointNum);
    jointD = Eigen::VectorXd::Zero(jointNum);
    // jointP << 300, 300, 300, 300, 300, 20,
    //     300, 300, 300, 300, 300, 20,
    //     300, 
    //     150, 150, 150,
    //     150, 150, 150;

    // jointP = 1 * jointP;
    // jointD << 30, 30, 30, 30, 30, 5,
    //     30, 30, 30, 30, 30, 5,
    //     30, 
    //     15, 15, 15,
    //     15, 15, 15;
    // jointD = 1 * jointD;

    // jointP << 100, 100, 100, 100, 100, 20,
    //     100, 100, 100, 100, 100, 20,
    //     100, 
    //     50, 50, 50,
    //     50, 50, 50;

    // jointP = 1 * jointP;
    // jointD << 20, 20, 20, 20, 20, 5,
    //     20, 20, 20, 20, 20, 5,
    //     20, 
    //     15, 15, 15,
    //     15, 15, 15;
    // jointD = 1 * jointD;

    jointP << 150, 150, 150, 150, 150, 30,
        150, 150, 150, 150, 150, 30,
        150, 
        150, 150, 30, 150,
        150, 150, 30, 150;

    jointP = 1 * jointP;
    jointD << 10, 10, 10, 10, 10, 6,
        10, 10, 10, 10, 10, 6,
        10, 
        10, 10, 6, 10,
        10, 10, 6, 10;
    jointD = 1 * jointD;

    std::cout << "MujocoInterface config reading ... done" << std::endl;


    // q_desire<<-0.2,0,0,0.4,-0.2,0,-0.2,0,0,0.4,-0.2,0;

    // mju_copy(mj_data->qpos, mj_model->key_qpos, mj_model->nq*1); // set ini pos in Mujoco

}

void MujocoInterface::GetDataFromSim(mjModel*mj_model, mjData* mj_data) {

    // std::default_random_engine generator;
    std::default_random_engine generator(static_cast<unsigned>(std::chrono::system_clock::now().time_since_epoch().count()));
    std::normal_distribution<double> gaussian_noise(0.0, metorvel_noise_std); // 均值0，标准差0.1，可根据需要修改

    for (int i=0;i<jointNum;i++)
    {
        motor_pos[i]=mj_data->qpos[jntId_qpos[i]];
        motor_vel[i]=mj_data->qvel[jntId_qvel[i]] + gaussian_noise(generator);
        motor_torque[i] = mj_data->qfrc_actuator[jntId_qvel[i]];
    }

    // std::cout<<"xanchor: "<< mj_data->xanchor[2] <<std::endl;
    // std::cout<<"njnt: "<< mj_model-> njnt <<std::endl;
    // for (int i = 0; i < mj_model->nq; ++i) {  // mj_model->njnt 是关节的数量
    //     std::cout << "Joint " << i << " position: " << mj_data->qpos[i] << " ";
    // }
    // std::cout << std::endl;



    imu_quat.x() = mj_data->sensordata[mj_model->sensor_adr[orientataionSensorId]+1];
    imu_quat.y() = mj_data->sensordata[mj_model->sensor_adr[orientataionSensorId]+2];
    imu_quat.z() = mj_data->sensordata[mj_model->sensor_adr[orientataionSensorId]+3];
    imu_quat.w() = mj_data->sensordata[mj_model->sensor_adr[orientataionSensorId]+0];

    // for (int i=0;i<4;i++)
    //     baseQuat[i]=mj_data->sensordata[mj_model->sensor_adr[orientataionSensorId]+i];
    // double tmp=baseQuat[0];
    // baseQuat[0]=baseQuat[1];
    // baseQuat[1]=baseQuat[2];
    // baseQuat[2]=baseQuat[3];
    // baseQuat[3]=tmp;



    for (int i=0;i<3;i++)
    {
        double last_pos = base_pos[i];
        base_pos[i]=mj_data->xpos[3*baseBodyId+i];
        imu_lin_acc[i]=mj_data->sensordata[mj_model->sensor_adr[accSensorId]+i];
        imu_angular_vel[i]=mj_data->sensordata[mj_model->sensor_adr[gyroSensorId]+i];
        base_lin_vel[i]=(base_pos[i]-last_pos)/(mj_model->opt.timestep);
        

    }


    Eigen::Vector3d global_vel(base_lin_vel[0], base_lin_vel[1], base_lin_vel[2]);
    // Eigen::Vector3d global_vel(mj_data->qvel[0], mj_data->qvel[1], mj_data->qvel[2]);
    Eigen::Vector3d local_vel = imu_quat.inverse() * global_vel;
    base_lin_vel_local[0] = local_vel.x();
    base_lin_vel_local[1] = local_vel.y();
    base_lin_vel_local[2] = local_vel.z();

}
 

void MujocoInterface::SetDataToPackage(DataPackage &data) 
{
    data.motor_pos = motor_pos;
    data.motor_vel = motor_vel;
    data.motor_torque = motor_torque;
    data.base_pos = base_pos;
    // std::cout<<"base_pos_mea: "<<base_pos<<std::endl;
    // std::cout<<"base_lin_vel_mea: "<<base_lin_vel<<std::endl;
    data.imu_angular_vel = imu_angular_vel;
    data.imu_lin_acc = imu_lin_acc;
    data.base_lin_vel = base_lin_vel;
    data.imu_quat = imu_quat;
    data.imu_zyx = quatToZyx(imu_quat);
    // data.baseQuat = baseQuat;

    data.base_lin_vel_local = base_lin_vel_local;


}

Eigen::Matrix<double, 3, 1> MujocoInterface::quatToZyx(const Eigen::Quaternion<double>& q)
{
    Eigen::Matrix<double, 3, 1> zyx;

    double as = std::min(-2. * (q.x() * q.z() - q.w() * q.y()), 0.99999);

    as = std::max(std::min(as, 1.0), -0.99999);  // 【双向夹紧在[-1, 1]】

    zyx(0) =
        std::atan2(2 * (q.x() * q.y() + q.w() * q.z()), q.w() * q.w() + q.x() * q.x() - q.y() * q.y() - q.z() * q.z());
    zyx(1) = std::asin(as);
    zyx(2) =
        std::atan2(2 * (q.y() * q.z() + q.w() * q.x()), q.w() * q.w() - q.x() * q.x() - q.y() * q.y() + q.z() * q.z());
    return zyx;
}


void MujocoInterface::SetPos(DataPackage &data, mjData* mj_data) 
{
        mj_data->qpos[0] = 0;
        mj_data->qpos[1] = 0;
        mj_data->qpos[2] = 1.0;
        mj_data->qpos[3] = 1;
        mj_data->qpos[4] = 0;
        mj_data->qpos[5] = 0;
        mj_data->qpos[6] = 0;
        for (int i=0;i<jointNum;i++){
            mj_data->qpos[7+i] = 0;
            mj_data->qvel[6+i] = 0;
        }

        for (int i=0;i<jointNum;i++){
            mj_data->qpos[7+i] = 0;
            mj_data->qvel[6+i] = 0;
        }


}   


void MujocoInterface::SetTorque(DataPackage &data, mjData* mj_data) 
{

    data.output_nlp = data.output_nlp;
    for (int i=0;i<jointNum;i++){
        // std::cout << "P: " << jointP[i] << " out: " << data.output_nlp[i] << " pos: " << motor_pos[i] << " D: " << jointD[i] << " vel: " << motor_vel[i] << std::endl;;
        mj_data->ctrl[i] = jointP[i] * (data.output_nlp[i] - motor_pos[i]) + jointD[i] * (-motor_vel[i]);
    }
}   

void MujocoInterface::SetTorque_zero(DataPackage &data, mjData* mj_data) 
{
    for (int i=0;i<jointNum;i++){

        mj_data->ctrl[i] = jointP[i] * (data.default_dof_pos[i] - motor_pos[i]) + jointD[i] * (-motor_vel[i]);
    }
}   








